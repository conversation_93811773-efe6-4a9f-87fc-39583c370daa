# Hierarchical Menu System - Implementation Guide

## Overview
This document provides a simple implementation guide for adding a hierarchical menu system to the Mirraw application. The system introduces "Main Menus" (Women, Men, Kids, etc.) that contain existing "Menus" (Sarees, Lehengas, etc.).

## Quick Summary
- **Goal**: Create two-level menu hierarchy for better navigation
- **Approach**: Add MainMenu model that groups existing Menu items
- **Impact**: Improved user experience with organized categories
- **Compatibility**: Existing menus continue to work unchanged

## Database Changes

### 1. Create Main Menus Table
```sql
-- Migration: create_main_menus.rb
CREATE TABLE main_menus (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  link TEXT,
  position INTEGER,
  country VARCHAR(255),
  app_source VARCHAR(255),
  app_name VARCHAR(255),
  is_hidden BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

### 2. Add Foreign Key to Menus
```sql
-- Migration: add_main_menu_id_to_menus.rb
ALTER TABLE menus ADD COLUMN main_menu_id INTEGER;
ALTER TABLE menus ADD FOREIGN KEY (main_menu_id) REFERENCES main_menus(id);
```

## Model Implementation

### MainMenu Model
```ruby
# app/models/main_menu.rb
class MainMenu < ActiveRecord::Base
  has_many :menus, dependent: :nullify
  
  validates :title, presence: true
  validates :position, presence: true
  
  scope :visible, -> { where(is_hidden: false) }
  scope :ordered, -> { order(:position) }
  
  include IdentifyLinkType
  before_save :identify_link
  
  def visible_menus
    menus.where(hide: false).order(:position)
  end
end
```

### Update Menu Model
```ruby
# app/models/menu.rb
class Menu < ActiveRecord::Base
  # Add this line
  belongs_to :main_menu
  
  # Add this method
  def self.hierarchical_menu_by_country(country)
    main_menus = MainMenu.includes(:menus).visible.ordered
    standalone_menus = where(main_menu_id: nil, hide: false)
    
    { main_menus: main_menus, standalone_menus: standalone_menus }
  end
end
```

## Controller Updates

### ApplicationController
```ruby
# app/controllers/application_controller.rb
def set_static_menu
  country = request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || 
           session[:country][:country_code]
  
  # New hierarchical menu data
  @hierarchical_menu = promise {
    Menu.hierarchical_menu_by_country(country)
  }
  
  # Keep existing menu data for compatibility
  @static_menu = promise {
    Menu.menu_column_and_item_by_hide_appsource_country(country).all
  }
end
```

## View Implementation

### Hierarchical Menu Partial
```haml
<!-- app/views/layouts/_hierarchical_menu.html.haml -->
- hierarchical_data = @hierarchical_menu

- if hierarchical_data[:main_menus].present?
  - hierarchical_data[:main_menus].each do |main_menu|
    %li.menu-list.main-menu-item
      = link_to main_menu.link, class: 'menu-link main-menu-link' do
        = main_menu.title
        %span.arrow
      
      - if main_menu.visible_menus.present?
        .megamenu-box.container-fluid
          .row
            - main_menu.visible_menus.each do |menu|
              .col-md-3.menu-section
                %h4.menu-title= menu.title
                = render 'layouts/menu_items', menu_columns: menu.menu_columns

- if hierarchical_data[:standalone_menus].present?
  - hierarchical_data[:standalone_menus].each do |menu|
    %li.menu-list
      = link_to menu.link, class: 'menu-link' do
        = menu.title
        %span.arrow
      .megamenu-box.container-fluid
        = render 'layouts/menu_items', menu_columns: menu.menu_columns
```

### Helper Method
```ruby
# app/helpers/application_helper.rb
def use_hierarchical_menu?
  # Feature toggle - can be controlled via environment variable
  ENV['ENABLE_HIERARCHICAL_MENU'] == 'true' || Rails.env.development?
end

def menu_display_partial
  use_hierarchical_menu? ? 'layouts/hierarchical_menu' : 'layouts/static_menu'
end
```

### Update Layout
```haml
<!-- In your main layout file -->
= render menu_display_partial
```

## Admin Interface

### Add to Rails Admin
```ruby
# config/initializers/rails_admin.rb
config.included_models = [
  # ... existing models ...
  "MainMenu",
  # ... rest of models ...
]
```

## Data Migration

### Populate Initial Data
```ruby
# Migration: populate_initial_main_menus.rb
def up
  # Create main categories
  MainMenu.create!([
    { title: 'Women', link: '/women', position: 1 },
    { title: 'Men', link: '/men', position: 2 },
    { title: 'Kids', link: '/kids', position: 3 },
    { title: 'Jewellery', link: '/jewellery', position: 4 }
  ])
  
  # Map existing menus (optional)
  women = MainMenu.find_by(title: 'Women')
  Menu.where(title: ['Sarees', 'Lehengas', 'Kurtis'])
      .update_all(main_menu_id: women.id) if women
end

def down
  MainMenu.destroy_all
  Menu.update_all(main_menu_id: nil)
end
```

## Basic Styling

### CSS
```scss
// app/assets/stylesheets/hierarchical_menu.scss
.main-menu-item {
  .main-menu-link {
    font-weight: bold;
    text-transform: uppercase;
  }
  
  .menu-section {
    padding: 15px;
    border-right: 1px solid #eee;
    
    .menu-title {
      font-weight: 600;
      margin-bottom: 10px;
      border-bottom: 2px solid #e91e63;
      padding-bottom: 5px;
    }
  }
}
```

## Implementation Steps

### Step 1: Database Setup
1. Create and run the migrations
2. Verify tables are created correctly

### Step 2: Models
1. Create MainMenu model
2. Update Menu model with association
3. Test in Rails console

### Step 3: Views
1. Create hierarchical menu partial
2. Add helper methods
3. Test display with sample data

### Step 4: Admin Interface
1. Add MainMenu to Rails Admin
2. Create sample main menus
3. Assign existing menus to main menus

### Step 5: Feature Toggle
1. Add environment variable control
2. Test both old and new menu systems
3. Gradually enable for users

## Testing

### Quick Tests in Rails Console
```ruby
# Create a main menu
main_menu = MainMenu.create!(title: 'Women', position: 1)

# Assign a menu to it
menu = Menu.first
menu.update!(main_menu: main_menu)

# Test hierarchical loading
Menu.hierarchical_menu_by_country('IN')
```

### Verification Checklist
- [ ] Migrations run successfully
- [ ] MainMenu model works in console
- [ ] Menu association works
- [ ] Hierarchical menu displays correctly
- [ ] Existing menus still work
- [ ] Admin interface functional

## Rollback Plan

If issues occur:
1. Set `ENABLE_HIERARCHICAL_MENU=false`
2. Restart application
3. If needed, rollback migrations: `rails db:rollback STEP=3`

## Benefits

- **Better Organization**: Logical grouping of product categories
- **Improved UX**: Easier navigation for customers
- **Mobile Friendly**: Better mobile menu experience
- **SEO Benefits**: Clearer site structure
- **Admin Efficiency**: Easier menu management

## Notes

- Main menu ID is optional (nullable) for backward compatibility
- Existing menus without main_menu_id display as standalone
- Feature can be toggled on/off without affecting existing functionality
- All existing menu functionality remains unchanged
