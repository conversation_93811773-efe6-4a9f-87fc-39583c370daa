# Hierarchical Menu System Implementation

## Overview

This implementation adds a two-level hierarchical menu system to the Mirraw application, where "Super Menus" (main categories like Women, Men, Kids, etc.) contain regular "Menus" (specific product types like Sarees, Lehengas, Kurtis, etc.).

## Database Schema Changes

### New Table: `super_menus`

```sql
CREATE TABLE super_menus (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  link TEXT,
  position INTEGER,
  country VARCHAR(255),
  app_source VARCHAR(255),
  is_hidden BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

### Modified Table: `menus`

Added foreign key reference to super_menus:
```sql
ALTER TABLE menus ADD COLUMN super_menu_id INTEGER REFERENCES super_menus(id);
```

## Models

### SuperMenu Model (`app/models/super_menu.rb`)

- **Associations**: `has_many :menus`
- **Validations**: Validates presence of title and position
- **Scopes**: 
  - `visible` - returns non-hidden super menus
  - `ordered` - orders by position
  - `mirraw`/`luxe` - filters by app type
- **Methods**:
  - `with_menus_by_country(country)` - gets super menus with menus for specific country
  - `visible_menus` - returns visible menus for this super menu
  - `has_visible_menus?` - checks if super menu has any visible menus

### Updated Menu Model (`app/models/menu.rb`)

- **New Association**: `belongs_to :super_menu, optional: true`
- **New Method**: `hierarchical_menu_by_country(country)` - returns structured data for hierarchical display

## Controllers

### ApplicationController Updates

The `set_static_menu` method now loads both hierarchical and traditional menu data:

```ruby
def set_static_menu
  country = request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['HTTP_COUNTRY'] || session[:country][:country_code]
  
  # Load hierarchical menu structure
  @hierarchical_menu = promise{
    Menu.hierarchical_menu_by_country(country)
  }
  
  # Keep backward compatibility with existing @static_menu
  @static_menu = promise{
    Menu.menu_column_and_item_by_hide_appsource_country(country).all
  }
end
```

## Views

### New Hierarchical Menu Partial (`app/views/layouts/_hierarchical_menu.html.haml`)

Displays the hierarchical menu structure with:
- Super menus as top-level navigation
- Regular menus grouped under their respective super menus
- Standalone menus (not associated with any super menu) displayed separately

### Helper Methods (`app/helpers/application_helper.rb`)

- `use_hierarchical_menu?` - determines when to use hierarchical menu
- `menu_display_partial` - returns appropriate partial based on menu type

## Admin Interface

SuperMenu is added to Rails Admin configuration for easy management:
- Create/edit super menus
- Assign menus to super menus
- Control visibility and positioning

## Styling

New CSS file `app/assets/stylesheets/hierarchical_menu.scss` provides:
- Styling for super menu items
- Responsive design
- Smooth transitions and animations
- Backward compatibility with existing menu styles

## Testing

### Test Files Created:
- `spec/models/super_menu_spec.rb` - Tests SuperMenu model functionality
- `spec/models/menu_hierarchical_spec.rb` - Tests hierarchical menu functionality
- `spec/factories/super_menus.rb` - Factory for creating test super menus

### Factory Updates:
- Existing `spec/factories/menus.rb` already includes super_menu support

## Migration Files

1. `20250122120000_create_super_menus.rb` - Creates super_menus table
2. `20250122120001_add_super_menu_id_to_menus.rb` - Adds foreign key to menus table
3. `20250122120002_populate_initial_super_menus.rb` - Populates initial super menu data

## Usage

### Creating Super Menus

```ruby
# Via Rails Admin or console
SuperMenu.create!(
  title: 'Women',
  link: '/women',
  position: 1,
  is_hidden: false
)
```

### Assigning Menus to Super Menus

```ruby
# Via Rails Admin or console
women_super_menu = SuperMenu.find_by(title: 'Women')
sarees_menu = Menu.find_by(title: 'Sarees')
sarees_menu.update!(super_menu: women_super_menu)
```

### Displaying Hierarchical Menu

In your layout file:
```haml
= render menu_display_partial
```

## Backward Compatibility

- Existing menu functionality remains unchanged
- Menus without super_menu_id are displayed as standalone menus
- Traditional menu display is still available
- All existing menu-related code continues to work

## Feature Toggle

The hierarchical menu can be toggled using the `use_hierarchical_menu?` helper method. Currently configured to show in development environment or when `?hierarchical_menu=true` parameter is present.

## Future Enhancements

1. **Admin Interface Improvements**: Custom admin interface for better menu management
2. **Menu Analytics**: Track menu usage and performance
3. **Dynamic Menu Loading**: AJAX-based menu loading for better performance
4. **Menu Personalization**: User-specific menu customization
5. **Multi-level Hierarchy**: Support for more than two levels if needed

## Deployment Notes

1. Run migrations: `rails db:migrate`
2. Populate initial data: The migration will create basic super menus
3. Configure existing menus via Rails Admin
4. Test both hierarchical and traditional menu displays
5. Update any custom menu-related code if necessary
