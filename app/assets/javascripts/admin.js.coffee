rack_list = []

mark_stitching_done_success = (rack_list_code,element, stitching_done_rack=undefined) ->
  number = $(element).attr('data-item-id')
  order_no = $(element).attr('data-order-no')
  rack_flag = true
  ###
  rack_list_number =[]
  if stitching_done_rack == null || stitching_done_rack == undefined  
    for rl in rack_list
      if rl.description == 'stitching_done'
        rack_list_number.push(rl.code)
        if rl.code == rack_list_code
          rack_flag = true
  else
    if stitching_done_rack == rack_list_code
      rack_flag = true
    else
      rack_list_number.push(stitching_done_rack)  
  ###
  if rack_flag
    $.ajax(paramMarkStitchingDone(number, order_no, stitching_done_rack))
  else
    alert "Rack #{rack_list_code} not available. \nAvailable Racks #{rack_list_number}"


change_rack_success = (rack_list_code,element) ->
  rack_flag = true
  designer_order_id = $(element).attr('data-designer-order-id')
  ###
  rack_list_number =[]
  stitching_flag = $(element).attr('data-stitching-flag')
  total_qty = $(element).attr('data-total-qty')
  stitching_done_flag = $(element).attr('data-stitching-done-flag')
  des_ord_type = $(element).attr('data-des-order-type')
  for rl in rack_list
    rack_save = false
    if (des_ord_type != 'jewellery' && stitching_done_flag == "true" && rl.description == 'stitching_done') ||
       (des_ord_type != 'jewellery' && stitching_flag == "true" && stitching_done_flag != 'true' && rl.description == 'stitching') ||
       (des_ord_type == 'jewellery' && rl.description == 'jewellery') ||
       (stitching_flag isnt "true" && des_ord_type != 'jewellery' && !rl.description?)
      if rl.quantity >= total_qty
        rack_list_number.push rl.code
      if rack_list_number.indexOf(rack_list_code) > -1 && rl.code == rack_list_code
        rack_flag = true
  ###
  if rack_flag
    $.ajax(changeRack(designer_order_id, rack_list_code))
  else
    alert "Rack #{rack_list_code} not available. \nAvailable Racks #{rack_list_number}"

getRackList = (rack_list_code,after_success_function,element) ->
  type: "GET"
  url: '/rack_list/codes'
  dataType: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined && data.error_text
      alert data.error_text
    else
      rack_list = data.content
      after_success_function(rack_list_code,element)

$(window).load ->
  shipper = $('#best_shipper_notice').text().toLowerCase()
  shipper = shipper.trim().replace(" ","_")
  $('#'+shipper).removeClass().addClass('label label-confirmed')



checkReceivedItemsForOrder = (order_no) ->
  type: 'GET'
  dataType: 'json'
  url: '/orders/check_items/'+order_no
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error

paramMarkReceived = (itemVal, order_no) ->
  type: "GET"
  data:
    item:itemVal
  url: '/packages/mark_item_received'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('#'+itemVal+'.mark-item-received').parent().append(data.received_status)
      if (data.received_rack).length > 0
        $('#'+itemVal+'.mark-item-received').parent().append("</br>Item Code- "+data.received_code)
        $('#'+itemVal+'.mark-item-received').parent().append("</br>Rack Code- "+data.received_rack)
        $('#rack_sticker_link_'+data.designer_order_id).show()
      $('#'+itemVal+'.mark-item-received').remove()
      $.ajax(checkReceivedItemsForOrder(order_no))

changeRack = (designer_order_id, rack_list_code) ->
  type: "POST"
  data:
    rack_list_code: rack_list_code
  url: "/designer_orders/#{designer_order_id}/change_rack"
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error == false
      alert "Rack Changed"
    else
      alert data.error_text

$ ->
  $('.resend_addon_payment_link').on('submit',(e) ->
    e.preventDefault()
    $.ajax(resendAddonPaymentLink($(this)))
    )

  resendAddonPaymentLink=(element) ->
    url: element.prop('action')
    data: element.serialize()
    datatype: 'json'
    success: (data) ->
      alert data.status

  $('.mark-item-received').click ->
    number = $(this).attr('id')
    order_no = $(this).attr('data-order-no')
    stitching = $(this).attr('data-stitching')
    ###
      rack_list_code = prompt('Add to Rack?')
      rack_flag = false
      rack_list_number =[]
      $.each rack_list, (i) ->
        if stitching == "null" && (!rack_list[i].description? || rack_list[i].description == "")
          rack_list_number.push rack_list[i].code
          if rack_list[i].code == rack_list_code
            rack_flag = true
        else if stitching == 'Y' && rack_list[i].description == 'stitching'
            rack_list_number.push rack_list[i].code
            if rack_list[i].code == rack_list_code
              rack_flag = true
      if rack_list_code.toLowerCase() == 'n' || rack_flag
        $.ajax(paramMarkReceived(number,order_no, rack_list_code))
      else
        alert "Rack #{rack_list_code} not available. \nAvailable Racks #{rack_list_number}"
    ###
    $.ajax(paramMarkReceived(number,order_no))

  $('.change-rack-code').click ->
    rack_list_code = prompt('change Rack?')
    if rack_list_code.length <= 0
      #$.ajax(getRackList(rack_list_code,change_rack_success,$(this)))
      alert 'Please input Rack code'
    else
      change_rack_success(rack_list_code,$(this))

  $('.assign-rack-code').click ->
    designer_order_id = $(this).attr('data-designer-order-id')
    rack_list_code = $(this).attr('data-rack-list-code')
    $.ajax(changeRack(designer_order_id, rack_list_code))


paramMarkStitchingSent = (itemVal, order_no) ->
  type: "GET"
  data:
    item:itemVal
  url: '/packages/mark_stitching_sent'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('.mark-stitching-sent-'+itemVal).parent().append(data.stitching_sent_status)
      $('.mark-stitching-sent-'+itemVal).remove()

$ ->
  $('.mark-stitching-sent').on('click', (e) ->
    number = $(this).attr('data-item-id')
    order_no = $(this).attr('data-order-no')
    $.ajax(paramMarkStitchingSent(number,order_no)))

paramMarkStitchingDone = (itemVal, order_no, rack_list_code) ->
  type: "GET"
  data:
    item:itemVal
    rack_code:rack_list_code
  url: '/packages/mark_stitching_done'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined      
      if $('#stitching_done_panel_notice').length == 0
        alert data.error 
      else
        $('#stitching_done_panel_notice').removeClass('hide').removeClass('alert-success').addClass('alert-danger').text(data.error)
    else
      if $('#stitching_done_panel_notice').length == 0
        alert "Rack changed to " + rack_list_code
        $('.mark-stitching-done-'+itemVal).parent().append(data.stitching_done_status)
        $('.mark-stitching-done-'+itemVal).remove()
      else    
        $('#stitching_done_panel_notice').removeClass('hide').removeClass('alert-danger').addClass('alert-success').text(data.stitching_done_status + ' and Rack Changed to ' + rack_list_code)
        $('#stitching_done_panel_submit').addClass('hide')
        $('#stitching_done_panel_notice').append("<a target='_blank' href=#{data.sticker_link} style='margin-left:10px;'>Rack Code Sticker</a>")
        $('#input_barcode').val('').focus()


stitchingDoneOperation = (element, stitching_done_rack) ->
  if stitching_done_rack == undefined || stitching_done_rack == null
    rack_list_code = prompt('change Rack?')
    $.ajax(getRackList(rack_list_code,mark_stitching_done_success, element))
  else
    mark_stitching_done_success('', element, stitching_done_rack)

$ ->
  $('.mark-stitching-done').on('click', (e) ->
    view_flag = $(this).attr('data-view-flag')
    stitching_done_rack = $(this).data('stitching-done-rack')
    if view_flag == "true"
      stitchingDoneOperation($(this), stitching_done_rack)
    )

paramMarkStitchingRequired = (itemVal, order_no, qc) ->
  type: "GET"
  data:
    item:itemVal
  url: '/packages/mark_stitching_required'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('.mark-stitching-required-'+itemVal).parent().append(data.stitching_required_status)
      $('.mark-stitching-required-'+itemVal).remove()
      if (data.qc_done == 'Y' || qc == 'true')
        $('.fabric_measured_class_'+itemVal).show()
        $('.stitching_required_class_'+itemVal).hide()
      location.reload()

paramRemoveStitchingRequired = (itemVal) ->
  type: "GET"
  data:
    item: itemVal
  url: '/packages/remove_stitching_required'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('#remove-stitching-required-'+ itemVal).parent().append(data.stitching_required_status)
      $('#remove-stitching-required-'+ itemVal).remove()

paramRemoveWarehouseTag = (item_id) ->
  type : 'POST'
  data:
    item_id: item_id
  url: '/line_item/remove_warehouse_tag'
  datatype: 'json'
  success: (data) ->
    if data.errors != undefined && data.error_message
      alert('Seller does not have required quantity')
    else
      $("#mark_receive-#{item_id}").modal('hide')
      $("#warehouse_receive_#{item_id}, #warehouse_sor_tag_#{item_id}").hide()

paramMarkSorLost = (item_id, account_id) ->
  type : 'POST'
  data:
    item_id: item_id,
    mark_lost: true,
    account_id: account_id
  url: '/line_item/remove_warehouse_tag'
  datatype: 'json'
  success: (data) ->
    if data.errors != undefined && data.error_message
      alert('Something went Wrong. Not able to remove and mark lost warehouse tag')
    else
      $("#mark_receive-#{item_id}").modal('hide')
      $("#warehouse_receive_#{item_id}, #warehouse_sor_tag_#{item_id}").hide()

paramMarkWarehouseReceived = (formdata,id) ->
  type: 'GET'
  data:
    formdata
  url: '/packages/mark_warehouse_received'
  dataType: 'json'
  success: (data,status,jqXHR) ->
    alert(data['received_status'])
    if data['error'] == undefined
      jQuery.noConflict()
      $('#'+id+'.mark-item-received').parent().append(data['received_status'])
      $("#mark_receive-#{id}").modal('hide')
      $("#warehouse_receive_#{id}").hide()

$ ->
  $('.remove_sor_tag').click ->
    item_id = $(this).data('item-id')
    if(confirm("Are you sure you want to remove SOR tag?") == true)
      $.ajax(paramRemoveWarehouseTag(item_id))

  $('.mark_sor_lost').click ->
    item_id = $(this).data('item-id')
    account_id = $(this).data('account-id')
    if(confirm("Are you sure you want to mark Lost?") == true)
      $.ajax(paramMarkSorLost(item_id, account_id))

  $('.mark_warehouse_package_assigned').on 'submit',(e) ->
    e.preventDefault()
    form_data = $(this).serialize()
    id = $(this).data('id')
    $.ajax(paramMarkWarehouseReceived(form_data,id))

  $('.mark-stitching-required').on('click', (e) ->
    number = $(this).attr('data-item-id')
    order_no = $(this).attr('data-order-no')
    qc_val = $(this).attr('data-qc')
    view_flag = $(this).attr('data-view-flag')
    if view_flag == "true"
      $.ajax(paramMarkStitchingRequired(number,order_no, qc_val)))

  $('.remove-stitching-required').click ->
    item_id = $(this).attr('data-item-id')
    view_flag = $(this).attr('data-view-flag')
    if view_flag == "true"
      $.ajax(paramRemoveStitchingRequired(item_id))

paramMarkFabricMeasurementDone = (itemVal, order_no) ->
  type: "GET"
  data:
    item:itemVal
  url: '/packages/mark_fabric_measurement_done'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('.mark-fabric-measurement-done-'+itemVal).parent().append(data.fabric_measurement_done_status)
      $('.mark-fabric-measurement-done-'+itemVal).remove()
      $('.measurement_confirmed_class_'+itemVal).show()
      $('.stitching_sent_class_'+itemVal).show()
      location.reload()

$ ->
  $('.mark-fabric-measurement-done').on('click', (e) ->
    number = $(this).attr('data-item-id')
    order_no = $(this).attr('data-order-no')
    view_flag = $(this).attr('data-view-flag')
    if view_flag == "true"
      $.ajax(paramMarkFabricMeasurementDone(number,order_no)))





























$ ->
  if ('.state-change-designer').length > 0
    paramStateChangeDesigner = (state, designer_id) ->
      type: 'GET'
      data:
        state: state
        designer_id: designer_id
      url: '/event_trigger/trigger_event_for_designer'
      success: (data, status, jqhxr) ->
        if data.error == undefined
          $('.'+designer_id+'_state').html(data.state)
        else
          alert(data.error)

    $('.state-change-designer').on 'click', ->
      state = $(this).val()
      designer_id = $(this).attr('data-designer-id')
      $.ajax(paramStateChangeDesigner(state, designer_id))



paramDuplicateOrderDetails = (order_no, design_id, replace_data, old_variant_id=undefined, variant_id=undefined) ->
  type: 'POST'
  data:
    order_no: order_no
    design_id: design_id
    replace_data: replace_data
    old_variant_id: old_variant_id
    variant_id: variant_id
  url: '/orders/duplicate'
  success: (data) ->
    if data == 'notok'
      alert 'Order not created'
    else if data.variants_available != undefined
      if data.variants_available == true
        if data.old_variants != null && data.old_variants.length > 0
          old_variant_id = prompt(data.old_variants)
          if old_variant_id == '' || !data.old_variants.includes(old_variant_id)
            alert('Replaced product variant not found.')
        variant_id = prompt(data.variants)
        $.ajax(paramDuplicateOrderDetails(order_no,design_id,replace_data,old_variant_id,variant_id)) if variant_id != '' or variant_id != undefined
      else if data.variants_available == false
        alert ('Design variants/size options not available')
    else
      alert 'Created Order Number: '+ data.order_no
      display_order_notes(data)

$ ->
  $('.duplicate_order').click ->
    order_no = $(this).attr('id')
    all_design_ids = $('#design_ids').val().trim().split(' ')
    design_id = prompt('Enter Product Id')
    replace_des_id = prompt('If stitching required design then enter replaced id,qty,mes_ids format else enter replaced id only.')
    replace_data = if replace_des_id == null then '' else replace_des_id.trim().split(',')
    if replace_des_id == '' || !all_design_ids.includes(replace_data[0])
      alert('replaced product data not found.')
    else if design_id != '' or design_id != undefined
      $.ajax(paramDuplicateOrderDetails(order_no,design_id,replace_data))

paramCreateDesignerIssue = (order_id, design_id, issue_type) ->
  type: 'POST'
  data:
    order_id: order_id
    design_id: design_id
    issue_type: issue_type
  url: '/admin/create_designer_issue'
  success: (data, status, jqhxr) ->
    if data.error == undefined
      if data.notes != undefined
        display_order_notes(data)
      if data.tags != undefined
        $('.'+order_id+'.tags').html('Tags: '+data.tags)
      if data.variant_available != undefined && data.variant_available != false
        jQuery.noConflict()
        $('#oos_line_item_id').val(data.variant_available)
        $('#mark_variant_oos_modal').modal('show')
      else
        alert data.mess
    else
      alert data.error

$ ->
  $('#mark_variant_oos_btn').on 'click', (e) ->
    e.preventDefault()
    form = $('form#select_oos_form')
    $.ajax
      url: form.attr('action')
      data: form.serialize()
      type: 'post'
      success: (data, status, jqhxr)->
        if data.error != undefined
          alert data.error
        else
          jQuery.noConflict()
          $('#mark_variant_oos_modal').modal('hide')
          alert data.mess

$('.orders_list').on('submit', '.new_designer_issue', (e) ->
  e.preventDefault()
  order_id = $(this).find('#designer_issue_order_id').val()
  design_id = $(this).find('#designer_issue_design_id').val()
  issue_type = $(this).find('#designer_issue_issue_type').val()
  $.ajax(paramCreateDesignerIssue(order_id, design_id, issue_type)))

refundAction = (data) ->
  display_order_notes(data)

minReqAction = (data) ->
  $('.'+data.order_id+'.tags').html('Tags:' + data.tags)
  display_order_notes(data)

requestBasedAction = (request_type, data) ->
  if request_type == 'refund_complete'
    refundAction(data)
  else if request_type == 'min_req'
    minReqAction(data)

sendCustomerMail = (order_id, request_type) ->
  type: 'POST'
  data:
    order_id: order_id
    request_type: request_type
  url: '/orders/customer_order_mail'
  success: (data, status, jqhxr) ->
    if data.error != undefined
      alert (data.error)
    else
      requestBasedAction(request_type, data)



paramOrderStateFilter = (stateVal='new') ->
  type: "GET"
  data:
    state: -> if stateVal then stateVal else $('#order_state_hidden').val()
  url: '/orders'
  datatype: 'script'

$ ->
  $("#filter_orders").change ->
    $(".orders_list").hide()
    state = $("option:selected", this).val()
    $.ajax(paramOrderStateFilter(state))

paramDesignerOrderStateFilter = (stateVal='new') ->
  type: "GET"
  data:
    state: -> if stateVal then stateVal else $('#order_state_hidden').val()
    type: 'designer_order'
  url: '/orders'
  datatype: 'script'


paramDesignerOrderTrackingNum = (trackingVal, designerOrderId) ->
  type: "POST"
  data:
    tracking:trackingVal
    id:designerOrderId
  url: '/designer_orders/add_tracking_num'
  datatype: 'script'

$ ->
  $('.designer_order_tracking').on('submit', (e) ->
    e.preventDefault()
    tracking = $('.tracking_num', this).val()
    designer_order_id = $(this).children('.designer_order_id').val()
    $(this).siblings('.tracking_link').attr('href', tracking)
    $('.designer_order_tracking').hide()
    $.ajax(paramDesignerOrderTrackingNum(tracking, designer_order_id)))

paramAddVariant = (variant_id, order_id, old_design_id=undefined, old_variant_id=undefined) ->
  type: 'POST'
  data:
    variant_id: variant_id
    order_id: order_id
    old_variant_id: old_variant_id
    old_design_id: old_design_id
  url: '/orders/add_variant'
  success: (data, status, jqhxr) ->
    if data.error != undefined
      alert data.error

paramAddDesign = (design_idVal, order_idVal, replace_data, old_variant_id=undefined) ->
  type: "POST"
  data:
    design_id:design_idVal
    order_id:order_idVal
    replace_data:replace_data
    old_variant_id: old_variant_id
  url: '/orders/add_design'
  success: (data, status, jqhxr) ->
    if data.error != undefined
      alert data.error
    else if data.variants_available != undefined
      if data.variants_available == true
        variants = data.variants
        if data.old_variants != null && data.old_variants.length > 0
          old_variant = prompt(data.old_variants)
          if old_variant == '' || !data.old_variants.includes(old_variant)
            alert('Replaced product variant not found.')
        variant_id = prompt(variants)
        old_design_id = replace_data[0] if old_variant == undefined || old_variant == ''
        $.ajax(paramAddVariant(variant_id,order_idVal,old_design_id,old_variant)) if variant_id != '' or variant_id != undefined
      else if data.variants_available == false
        alert ('Design variants/size options not available')
    else if data.old_variants != undefined && data.old_variants.length > 0
      old_variant_id = prompt(data.old_variants)
      if old_variant_id != '' && data.old_variants.includes(old_variant_id)
        $.ajax(paramAddDesign(design_idVal, order_idVal, replace_data, old_variant_id))
      else
        alert('Replaced product variant not found.')

$ ->
  $('.order_add_design').on('submit', (e) ->
    e.preventDefault()
    design_id = $('.add_design', this).val()
    order_id = $(this).children('.order_id_add_design').val()
    design_ids = $('#design_ids').val().trim().split(' ')
    replace_des_id = ''
    replace_des_id = prompt('If stitching required design then enter replaced id,qty,mes_ids format else enter replaced id only.')
    replace_data = if replace_des_id == null then '' else replace_des_id.trim().split(',')
    if design_id != '' && replace_des_id != '' && design_ids.includes(replace_data[0])
      $.ajax(paramAddDesign(design_id, order_id, replace_data))
    else
      alert('replaced product data not found.')
  )

$(document).ready ->
  if $('#order-notes').length > 0
    $.fn.dataTableExt.oSort['time-date-sort-pre'] = (value) ->
      Date.parse value
    $.fn.dataTableExt.oSort['time-date-sort-asc'] = (a, b) ->
      a - b
    $.fn.dataTableExt.oSort['time-date-sort-desc'] = (a, b) ->
      b - a
    $('#order-notes thead td.filter').each ->
      $(this).html '<input type="text" placeholder="Search" class="form-control input-sm" />'
      return

    Notetable = $('#order-notes').DataTable(
      'paging': true
      'bSortCellsTop': true
      'info': false
      'columnDefs': [{'type': 'time-date-sort', 'targets': [0]}]
      'order': [[0, 'desc']]
      'lengthMenu': [[15, 30, 50, -1], [15, 30, 50, "All"]]
      'pageLength': 15
    )
    $('#order-notes thead input').on 'keyup change', ->
      Notetable.column($(this).parent().index()).search(@value).draw()
      return

display_order_notes = (data) ->
  Notetable = $('#order-notes').DataTable()
  Notetable.row.add([data.time, data.notes.done_by, data.notes.note_type, data.notes.notes]).draw( false )
  $('#order-notes').show()

paramAddNotes = (notes_idVal, order_idVal, note_type) ->
  type: "POST"
  data:
    notes_id:notes_idVal
    order_id:order_idVal
    note_type:note_type
  url: '/orders/add_notes'
  datatype: 'json'
  success: (data,status,jqhxr) ->
    if data.error == undefined
      if note_type != undefined
        alert('Successfully Added.')
      else  
        display_order_notes(data)
    else
      alert data.error

$ ->
  $('.orders_list').on('submit', '.order_add_notes',(e) ->
    e.preventDefault()
    notes_id = $('.add_notes', this).val()
    if notes_id != ''
      order_id = $(this).children('.order_id_add_notes').val()
      note_type = $(this).children('.order_note_type').val()
      design_id = $(this).children('.order_design_id').val()
      if design_id != undefined
        notes_id = (notes_id + ' (' + design_id + ')')
        $('.add_notes', this).val('')
      setTimeout (->
        alert "adding notes..."
      ), 1
      $.ajax(paramAddNotes(notes_id, order_id, note_type))
  )

paramAddDesignerNotes = (notes_idVal, designer_order_idVal) ->
  type: "POST"
  data:
    notes_id:notes_idVal
    designer_order_id:designer_order_idVal
  url: '/designer_orders/add_notes'
  datatype: 'json'
  success: (data,status,jqhxr) ->
    if data.error == undefined
      $('#designer_notes_' + designer_order_idVal).html(data.notes)
    else
      alert data.error

paramAddDesignerSecondaryTracking = (tracking_number_value, designer_order_id_value, reason) ->
  type: "POST"
  data:
    tracking_number:tracking_number_value
    designer_order_id:designer_order_id_value
    reason: reason
  url: '/designer_orders/add_secondary_tracking'
  datatype: 'json'
  success: (data,status,jqhxr) ->
    if data.error == undefined
      $('#designer_notes_' + designer_order_id_value).html(data.notes)
    else
      alert data.error      

$ ->
  $('.designer_order_add_notes').on('submit', (e) ->
    e.preventDefault()
    notes_id = $('.add_notes', this).val()
    if notes_id != ''
      designer_order_id = $(this).children('.designer_order_id_add_notes').val()
      setTimeout (->
        alert "adding notes..."
      ), 1
      $.ajax(paramAddDesignerNotes(notes_id, designer_order_id)))

$ ->
  $('.designer_order_add_secondary_tracking').on('submit', (e) ->
    e.preventDefault()
    tracking_number = $('#secondary_tracking',this).val()
    if tracking_number != ''
      designer_order_id = $(this).children('.designer_order_id_add_secondary_tracking').val()
      $.ajax(paramAddDesignerSecondaryTracking(tracking_number, designer_order_id, $('#tracking_reason',this).val())))

paramAddTags = (tags_Val, order_idVal, reason_Val) ->
  type: "POST"
  data:
    tags:tags_Val
    order_id:order_idVal
    reason:reason_Val
  url: '/orders/add_tags'
  datatype: 'json'
  success: (data,status,jqhxr) ->
    if data.error == undefined
      $('.'+order_idVal+'.tags').html('Tags: '+data.tags)
      display_order_notes(data)
    else
      alert data.error

$ ->
  $('.remove_tags_button').on('click', (e) ->
   $('.add_tags, .add_tags_reason').removeAttr('required')
   $('.remove_tags').prop('required',true)
   $('.add_tags, .add_tags_reason').val('')
  )
$ ->
  $('.add_tags_button').on('click', (e) ->
    $('.remove_tags').removeAttr('required')
    $('.add_tags, .add_tags_reason').prop('required',true)
    $('.remove_tags').val('')
  )

$ ->
  $('.order_add_tags').on('submit', (e) ->
    e.preventDefault()
    tags = $('.add_tags', this).val() || $('.remove_tags', this).val()
    if tags != ''
      order_id = $(this).children('.order_id_add_tags').val()
      reason_val = $(this).children('.add_tags_reason').val()
      setTimeout (->
        alert "adding/removing tags..."
      ), 1
      $.ajax(paramAddTags(tags, order_id, reason_val))
  )


paramDeleteItem = (itemId,reason) ->
  type: "POST"
  data:
    id:itemId
    reason:reason
  url: '/orders/delete_line_item'
  datatype: 'script'


paramUpdateItem = (itemId, quantityVal) ->
  type: "POST"
  data:
    id:itemId
    quantity:quantityVal
  url: '/orders/update_line_item'
  datatype: 'script'

sentForInvoice = (itemId) ->
  type: "POST"
  data:
    id:itemId
  url: '/orders/check_order_label'
  datatype: 'json'
  success: (data,status,jqhxr) ->
    if data.item_status == 'Checked'
      check_item_div = $('#item_check_'+itemId)
      check_item_div.addClass('label label-success')
      check_item_div.text('Checked')
      $('#check_items_done_'+itemId).text('done by '+ data.done_by)

$ ->
  $(".update_line_item").on('click', (e) ->
    e.preventDefault()
    id = $(this).data('item')
    $.ajax(paramUpdateItem(id, $("#" + "quantity_" + id).val())))

$ ->
  $(".designer_orders .checkItem").on('click', (e) ->
    e.preventDefault()
    id = $(this).data('item')
    $.ajax(sentForInvoice(id))
  )

paramOOSItem = (design_id,state) ->
  type: 'POST'
  data:
    design_id: design_id
    state: state
  url: '/event_trigger/trigger_event_for_design'
  success: (data, status, jqxhr)->
    if data.errors != undefined
      alert (data.errors)
    else
      alert ('Design '+design_id+' new state = '+ data.state)

$ ->
  $(".remove_line_item").on('click', (e) ->
    e.preventDefault()
    if !confirm('are you sure ?') 
      return false
    jQuery.noConflict()
    $('#desorder_id').val($(this).data('item'))
    $('#cancel_design_id').val($(this).attr('data-design_id'))
    $('#order_no_for_cancel').val($(this).attr('date-order-no'))
    $('#modal_cancel_desorder').modal('show')
    $('#marking_seller_oos').removeClass('hide')
  )

$ ->
  $("#filter_designer_orders").change ->
    $(".orders_list").hide()
    state = $("option:selected", this).val()
    $.ajax(paramDesignerOrderStateFilter(state))

oss_item_payment_message = "There oos items do you still want to send. Enter y Or n"

paramSendPaypalInvoice = (numberVal) ->
  type: "POST"
  data:
    number:numberVal
  url: '/orders/send_paypal_invoice'
  datatype: 'script'
  success: (data, status, jqhxr) ->
    if data.has_error != undefined && data.has_error
      alert data.errors 

sendPayPal = (number) ->
  setTimeout (->
    alert "Sending Paypal Invoice..."
  ), 1
  $.ajax paramSendPaypalInvoice(number)

$ ->
  $(".paypal_invoice_link").on "click", (e) ->
    number = $(this).attr("id").replace("paypal_", "")
    order_state = $(this).attr("data-oos")
    if order_state == 'true'
      send = prompt oss_item_payment_message
      sendPayPal(number) if send == 'y'
    else if order_state == 'false'
      sendPayPal(number)

paramMultipleLocalInvoiceLinks = (numberVal,use_param) ->
  type: "POST"
  data:
    number:numberVal
    use:use_param
  url: '/orders/multiple_local_invoice_links'
  datatype: 'script'

sendMultipleLocalInvoiceLinks = (number, use_param) ->
  setTimeout (->
    alert "Sending multiple local invoice links.."
  ), 1
  $.ajax paramMultipleLocalInvoiceLinks(number,use_param)

$ ->
  $('.multiple_local_invoice_links').on 'click', (e) ->
    number = $(this).attr("id").replace("payu_", "")
    order_state = $(this).attr("data-oos")
    use_param = $(this).attr('data-use')
    if order_state == 'true'
      send = prompt oss_item_payment_message
      sendMultipleLocalInvoiceLinks(number, use_param) if send == 'y'
    else if order_state == 'false'
      sendMultipleLocalInvoiceLinks(number, use_param)

paramSendEmail = (numberVal) ->
  type: "POST"
  data:
    number:numberVal
  url: '/orders/send_order_ack_email'
  datatype: 'script'

$ ->
  $('.order-ack-email').on('click', (e) ->
    number = $(this).attr('id').replace('Ack_Email_', '')
    setTimeout (->
      alert "Sending Order Ack Email..."
    ), 1
    $.ajax(paramSendEmail(number)))


paramShipDirect = (numberVal) ->
  type: "POST"
  data:
    number:numberVal
  url: '/orders/send_ship_direct_email'
  datatype: 'script'

$ ->
  $('.ship-direct-email').on('click', (e) ->
    number = $(this).attr('id').replace('ship_direct_', '')
    setTimeout (->
      alert "Sending Ship direct Email..."
    ), 1
    $.ajax(paramShipDirect(number)))


paramConvertToGharpay = (numberVal) ->
  type: "POST"
  data:
    number:numberVal
  url: '/orders/convert_to_gharpay'
  datatype: 'script'

sendGharpay = (number) ->
  setTimeout (->
    alert "Converting to gharpay..."
  ), 1
  $.ajax paramConvertToGharpay(number)

$ ->
  $('.convert-to-gharpay').on('click', (e) ->
    number = $(this).attr("id").replace("gharpay_", "")
    order_state = $(this).attr("data-oos")
    if order_state == 'true'
      send = prompt oss_item_payment_message
      sendGharpay(number) if send == 'y'
    else if order_state == 'false'
      sendGharpay(number)
  )

convertCOD = (order_id) ->
  type: 'GET'
  data:
    order_id: order_id
  url: '/api/convert_to_cod'
  success: (data, status, jqhxr) ->
    if typeof(data.error) == 'undefined'
      if data.cod == true
        alert 'Order converted to cod. Redirecting to order'
        window.location.href = '/orders?search='+ data.order_number
      else
        alert 'COD not available'
    else
      alert data.error

$ ->
  $('.convert-to-cod').on('click', (e) ->
    order_id = $(this).attr('data-order-id')
    $.ajax(convertCOD(order_id))
  )

$ ->
  if $('.update_invoice').length > 0
    $('.update_invoice').on('change', ->
      if $(this).val() != ''
        cat_name = $(this).attr('data-cat-name')
        quantity_id = '#' + cat_name + '_quantity'
        quantity = parseInt($(quantity_id).val())
        invoice_price_id = '#' + cat_name + '_invoice_price'
        invoice_price = parseInt($(invoice_price_id).val())
        total_price_id = '#' + cat_name + '_total_price'
        total_price = quantity * invoice_price
        if total_price > 0
          $(total_price_id).val(total_price)
    )

  $(document).on('click', '.mail_action', (e) ->
    request_type = $(this).attr('data-request-type')
    order_id = $(this).attr('id')
    send = prompt ('Do you want to send mail for ' + request_type + '. Type y for yes')
    $.ajax(sendCustomerMail(order_id, request_type)) if send == 'y'
    )

paramOrderEventTrigger = (orderId, stateVal, courier_company_val, tracking_number_val, reason) ->
  type: "POST"
  data:
    order_id: orderId
    state: stateVal
    courier_company: courier_company_val
    tracking_number: tracking_number_val
    reason: reason
  url: '/event_trigger/trigger_event_for_order'
  datatype: 'script'
  beforeSend: ->
    $('.loading-image').show()
  complete: ->
    $('.loading-image').hide()


paramDesignerOrderEventTrigger = (designerOrderId, stateVal, reason) ->
  type: "POST"
  data:
    designer_order_id: designerOrderId
    state: stateVal
    reason: reason
  url: '/event_trigger/trigger_event_for_designer_order'
  datatype: 'script'

paramsStitchingMeasurementConfirmed = (itemVal, order_no) ->
  type: "GET"
  data:
    item:itemVal
  url: '/packages/mark_measurement_confirmed'
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert data.error
    else
      $('.measurement-confirmed-required-'+itemVal).parent().append(data.measurement_confirmed_status)
      $('.measurement-confirmed-required-'+itemVal).remove()

$ ->
  $('.designer_order_event').on('click', (e) ->
    jQuery.noConflict()
    e.preventDefault()
    form = $(this).closest('form')
    designer_order_id = form.children('.designer_order_id').val()
    state = form.children('.state').val()
    if state == 'canceled'
      if $("#tailoring_receive_pending_#{designer_order_id}").val() == 'true'
        alert('Please update the receive status of tailor assigned products')
      else    
        $('#desorder_id').val(designer_order_id)
        $('#modal_cancel_desorder').modal('show')
        $('#marking_seller_oos').addClass('hide')
    else
      $.ajax(paramDesignerOrderEventTrigger(designer_order_id, state)))

  $('#cancel_desorder_with_reason').click ->
    reason = $('#reason_for_cancel')
    if reason.val() == ''
      $('#cancel_reason_alert').text('Please select reason for canceling !')
    else
      item_id = $('#desorder_id').val()
      if $('#cancel_design_id').val().length > 2
        $.ajax(paramDeleteItem(item_id,$('#reason_for_cancel').val()))
        $('state_'+item_id).hide()
        $('status_'+item_id).text("Canceled")
        state = 'seller_out_of_stock'
        $.ajax(checkReceivedItemsForOrder($('#order_no_for_cancel').val()))
        if mark_seller_oos == 'YES'
          $.ajax(paramOOSItem($('#cancel_design_id').val(),state))
      else
        $.ajax(paramDesignerOrderEventTrigger(item_id,'canceled',reason.val()))
      location.reload()

$ ->
  $('.order_event').on('submit', (e) ->
    e.preventDefault()
    orderid = $(this).children('.order_id').val()
    state = $(this).children('.state').val()
    if state == 'cancel'
      cancel_reason = $('#reason',this).val()
      if cancel_reason.length <=0
        return alert('please select reason for cancel')
    courier_company = $(this).children('#Courier').val()
    tracking_number = $(this).children('#Tracking_number').val()
    $.ajax(paramOrderEventTrigger(orderid, state, courier_company, tracking_number, cancel_reason)))

  $('.measurement-confirmed-required').on('click', (e) ->
    number = $(this).attr('data-item-id')
    order_no = $(this).attr('data-order-no')
    view_flag = $(this).attr('data-view-flag')
    if view_flag == "true"
      $.ajax(paramsStitchingMeasurementConfirmed(number,order_no)))

$ ->
  toggleOrderCancelReason()
  $('#order_change_state .state').on 'change', ->
    toggleOrderCancelReason()

toggleOrderCancelReason = ->
  if $('#order_change_state .state').val() == 'cancel'
    $('.order_cancel_reason').show()
  else
    $('.order_cancel_reason').hide()

checkCodAvailability = (cod_form) ->
  product_ids = cod_form.find('#product_ids').val()
  pincode = cod_form.find('#pincode').val()
  $.ajax
    type: 'GET'
    data:
      pincode: pincode
      id: product_ids
    url: '/api/cod_design'
    datatype: 'JSON'
    success: (data, status, jqhxr) ->
      $('.cod_availability_notice').show()
      if data.cod_available
        $('.cod_availability_notice').text("COD is Available")
      else
        $('.cod_availability_notice').text("COD is not available")

$ ->
  $('.cod_availability #pincode').focusout ->
    cod_form = $('form#cod_availability')
    checkCodAvailability(cod_form)

  $('.cod_availability #check_cod_availability').click ->
    cod_form = $('form#cod_availability')
    checkCodAvailability(cod_form)

$ ->
  $('#claimbtn').click ->
    alert "Marked For Claim"
    $(this).hide()

paramsStitchingMeasurementInfo =(height,product_designable_type,weight,data_item_id,namespace)->
  type: "GET"
  data:
    height: height
    product_designable_type: product_designable_type
    weight: weight
  url: "/stitching_measurement/measurement_info"
  datatype: 'json'
  success: (data, status, jqXHR) ->
    if data.error != undefined
      alert 'error'
    else
      $('#'+namespace+'_hip_size_measurement_form_'+data_item_id).val(data.hip_size)
      $('#'+namespace+'_waist_size_measurement_form_'+data_item_id).val(data.waist_size)
      $('#'+namespace+'_chest_size_measurement_form_'+data_item_id).val(data.chest_size)
      $('#'+namespace+'_shoulder_size_measurement_form_'+data_item_id).val(data.shoulder_size)
      $('#'+namespace+'_under_bust_measurement_form_'+data_item_id).val(data.under_bust)
      $('#'+namespace+'_size_around_arm_hole_measurement_form_'+data_item_id).val(data.size_around_arm_hole)
      $('#'+namespace+'_bottom_length_measurement_form_'+data_item_id).val(data.bottom_length)
      $('#'+namespace+'_size_around_knee_measurement_form_'+data_item_id).val(data.size_around_knee)
      $('#'+namespace+'_size_around_ankle_measurement_form_'+data_item_id).val(data.size_around_ankle)
      $('#'+namespace+'_size_around_thigh_measurement_form_'+data_item_id).val(data.size_around_thigh)
      $('#'+namespace+'_size_around_arm_hole_measurement_form_'+data_item_id).val(data.size_around_arm_hole)
      if product_designable_type == 'blouse'
        $('#'+namespace+'_waist_size_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_bottom_length_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_size_around_knee_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_size_around_ankle_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_size_around_thigh_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_hip_size_measurement_form_'+data_item_id).hide()
      else if product_designable_type == 'lehenga_choli'
        $('#'+namespace+'_size_around_knee_measurement_form_'+data_item_id).hide()
        $('#'+namespace+'_size_around_ankle_measurement_form_'+data_item_id).hide()





$ ->
  $('.measurement_vals').click ->
    item_id = $(this).data('item-id')
    namespace = $(this).data('namespace')
    height = $("##{namespace}_height_measurement_form_#{item_id}").val()
    product_designable_type = $(".designable_selector_#{item_id}_#{namespace}").val()
    weight = $("##{namespace}_weight_measurement_form_#{item_id}").val()
    $.ajax(paramsStitchingMeasurementInfo(height,product_designable_type,weight,item_id,namespace))

  $('.measurement_form_btn').click ->
    alert('Submitted Successfully')
  
  $('.measurement_form_create_btn').click ->
    $(this).hide()
    alert('Submitted Successfully')

SetResetRequiredElement=(item_id, namespace, is_required)->
    required_elements = ['height', 'weight', 'length']
    for id in required_elements
      $('#'+namespace+'_'+id+'_measurement_form_'+item_id).attr('required',is_required)

paramStandardSizeSelection=(item_id,namespace,current_size)->
  type: "GET"
  data:
    current_size: current_size
    item_id: item_id
  url: '/stitching_measurement/standard_measurement_info'
  datatype: 'json'
  success: (data)->
    if data.errors != undefined && data.error_message
      alert(data.error_message)
    else
      $('.measurement_vals').hide()
      SetResetRequiredElement(item_id,namespace,false)
      for key, value of data.std_sizes
        $('#'+namespace+'_'+key+'_measurement_form_'+item_id).val(value)

$ ->
  $('.standard_size_selection').change ->
    item_id = $(this).data('item-id')
    namespace = $(this).data('namespace')
    current_size = $('option:selected', this).val()
    if current_size !=''
      $.ajax(paramStandardSizeSelection(item_id,namespace,current_size))
    else
      $(this).closest('form').find("input[type=text], input[type=number], textarea").val("")
      $('.measurement_vals').show()
      $('#'+namespace+'_code_measurement_form_'+item_id).val(null)
      pre_measurements_elements = ['sleeves_length', 'front_neck', 'sleeves_around', 'back_neck']
      for id in pre_measurements_elements
        $('#'+namespace+'_'+id+'_measurement_form_'+item_id).val('As per fabric or given')
      SetResetRequiredElement(item_id,namespace,true)

$ ->
  $('.measurement_info_editable').click ->
    item_id = $(this).data('item-id')
    namespace = $(this).data('namespace')
    $("#measurement_info_form_#{item_id}_#{namespace} :text").prop('disabled',false)
    $("##{namespace}_height_measurement_form_#{item_id}").prop('disabled',false)
    $("##{namespace}_weight_measurement_form_#{item_id}").prop('disabled',false)
    $("##{namespace}_length_measurement_form_#{item_id}").prop('disabled',false)
    $("#select_product_designable_type_#{item_id}_#{namespace}").show()

$ ->
  $('#ticket_issue').change ->
    if $(this).val() == 'Refund not received' && $('#ticket_department').val() == 'accounts'
      $('#ticket_return_id').attr('disabled',false)
      $('.refund_ids').show()
    else
      $('#ticket_return_id').attr('disabled',true)
      $('.refund_ids').hide()
    if jQuery.inArray($(this).val(), ['Ordered for Occasion','Delayed Delivery', 'TAT Adherence']) != -1
      $('#expected_delivery_block').show()
      $('#expected_resolve_block').hide()
    else
      $('#expected_delivery_block').hide()
      $('#expected_resolve_block').show()

  $('#ticket_department').change ->
    if ['support', 'admin', 'accounts_admin'].includes($('#ticket_raised_by_department').val()) || $(this).val() == 'stitching'
      $('#stitching_images').show()
    else
      $('#stitching_images').hide()

  $('.stitching_image_upload').change ->
    file = @files[0]
    extension = file.name.split('.')[file.name.split('.').length - 1]
    extension_list = ['jpg','png','jpeg','gif']
    if file.size > 4000000
      alert 'Maximum file size is 4MB.'
      $(this).val('')
    unless extension_list.includes(extension)
      alert 'Incorrect File Format! Please upload files in any of following format : jpeg,jpg,png,gif'
      $(this).val('')

  $('#pay_type_for_transaction_id').on 'change',(e) ->
    if $('#pay_type_for_transaction_id').val() == 'duplicate_order'
      $('#transaction_id').attr('placeholder','Source/Original Order Number')
    else
      $('#transaction_id').attr('placeholder','Transaction Id')  

  $('#mark_sane_with_transaction_id').on 'click', (e) ->
    txn_id = $('#transaction_id').val()
    if txn_id.length > 0
      attribute_name = $('#pay_type_for_transaction_id').val()
      if attribute_name == 'paypal_txn_id' && txn_id.length != 17
        $('#txn_id_notice.alert-danger').text('Please enter valid Transaction Id')
      else if attribute_name == 'duplicate_order'
        $.ajax(checkTxnForDuplicateOrder(txn_id,gon.order_number))  
      else  
        $('#form_transaction_id').val(attribute_name + '::' + txn_id)
        $.ajax(checkValidTxnId(attribute_name,txn_id))  
    else
      $('#txn_id_notice.alert-danger').text('Please Enter ' + $('#transaction_id').attr('placeholder')) 
      $('#transaction_id').focus()

  $('.btn_change_order_state').on 'click', (e) ->
    jQuery.noConflict()
    state = $('.order_state').val()
    if state == 'sane'
      e.preventDefault() 
      $('#modal_sane_orde').modal('show') 

  $('.additional_payment_form').on 'submit', (e) ->
    e.preventDefault()
    id = $(this).data('id')
    if $("#additional_pay_type_#{id}").val() == 'paypal_txn_id' && $("#additional_transaction_id_#{id}").val().length == 17
      $.ajax(markAdditionalPaymentSane(id))
    else if  $("#additional_pay_type_#{id}").val() != 'paypal_txn_id'
      $.ajax(markAdditionalPaymentSane(id))
    else
      alert('Paypal transaction_id needs to be of 17 digits')

markAdditionalPaymentSane = (id) ->
  type: 'GET'
  data:
    order_id: $("#additional_pay_order_id_#{id}").val()
    pay_type: $("#additional_pay_type_#{id}").val()
    transaction_id: $("#additional_transaction_id_#{id}").val()
    additional_payment_id: $("#additional_pay_id_#{id}").val()
  url: '/admin/mark_additional_payment_complete'
  success: (data,status) ->
    $("#additional_payment_form_#{id}").hide()
    alert('Payment marked completed')

checkValidTxnId = (atr,txn_id) ->
  type: 'GET'
  data:
    attribute_name: atr
    txn_id: txn_id
    order_number: $('#order_number_for_transaction').val()
  url: '/admin/check_for_unique_transaction_id'
  success: (data, status) ->
    if data.status == 'OK'
      $('#order_change_state').submit();
    else
      $('#txn_id_notice.alert-danger').text('Transaction ID is already present')

checkTxnForDuplicateOrder = (source_order_number,order_number) ->
  type: 'GET'
  data:
    source_order_number: source_order_number
    order_number: order_number
  url: '/admin/get_transaction_id_for_duplicate_order'
  success: (data,status) ->
    if data.status == 'OK'
      $('#form_transaction_id').val(data.transaction_id)
      $('#source_order_id').val(data.parent_order_id)
      $('#order_change_state').submit();
    else
      $('#txn_id_notice.alert-danger').text(data.errors)  

$ ->
  $('.to_type_radio_button input[type="radio"]').on 'click', (evt) ->
    if $(this).val() == 'Customer'
      $('#customer_emails').attr('required',true)
      $('#customer_emails').show()
      $('#designer_emails').attr('required',false)
      $('#designer_emails').hide()
      $('#from').val('<EMAIL>')
    else
      $('#designer_emails').attr('required',true)
      $('#designer_emails').show()
      $('#customer_emails').attr('required',false)
      $('#customer_emails').hide()
      $('#from').val('<EMAIL>')

$ ->
  $('#send_email_order_detail').on 'submit',(e) ->
    $('#send_email_button').attr('disabled',true)
    $('#send_email_button').attr('value','Sending Mail')
    if CKEDITOR.instances.cktext_body_.getData() == ''
      e.preventDefault(); 
      alert("Mail Cannot be Sent with Empty Body")
      $('#send_email_button').attr('disabled',false)
      $('#send_email_button').attr('value','Send Email')

$ ->
  $('#add_another').click ->
    value = $(this).attr("value")
    item_id = $('#item_id').val()
    order_num = $('#order_number').val()
    order_id = $('#order_id').val()
    total_tabs = $("##{value}-modal-#{item_id} ul li").length
    $(this).prop('disabled', true)
    $.ajax(renderAtvDetailsForm(item_id,order_num,value,order_id,total_tabs))

$ ->     
renderAtvDetailsForm = (item_id,order_num,value,order_id,total_tabs) ->
  type: 'get'
  data: 
    value: $(this).attr("value")
    item_id: $('#item_id').val()
    order_num: $('#order_number').val()
    order_id: $('#order_id').val()
    total_tabs: $("##{value}-modal-#{item_id} ul li").length
  dataType: 'json'
  url: "/line_item/get_addon_type_value_form/#{value}"
  success: (data,status) ->
    - tab_no = (total_tabs + 1)
    - tab_id = "#{value}-form-#{tab_no}"
    - form = data['form']
    $("##{value}-modal-#{item_id} ul li").removeClass("active");
    $("##{value}-modal-#{item_id} .nav.nav-tabs").append("<li class ='active' id='#{value}-tab-#{tab_no}'><a data-toggle = 'tab' href='##{tab_id}'><h5> #{value} #{tab_no}</h5></a></li>")
    $("##{value}-modal-#{item_id} .tab-content .tab-pane").removeClass('active').removeClass('in')
    $("##{value}-modal-#{item_id} .tab-content").append("<div class='active fade in tab-pane' id='#{tab_id}'></div>")
    $("##{tab_id}").html("#{form}")
    $('#add_another').prop('disabled', false)

$ ->
    
  UpdateTicketNoteView = () ->
    $('.ticket_notes_form #notes, #add_new_notes').show()
    $('.ticket_notes_form #add_notes').hide().val('')
    $('.ticket_notes_form #submit_notes').hide().attr('value', 'Submit Notes').data('type', 'notes')
    $('.back_arrow').hide()
    $('#ticket_notice_message').hide()

  $('.t_notes').click ->
    ticket_id = $(this).data('id')
    notes = $(this).data('notes')
    if notes != '' || notes != undefined
      notes = notes.split(' ... ').reverse().join("\r\n")
    $('.ticket_notes_form #ticket_id').val(ticket_id)
    $('.ticket_notes_form #notes').val(notes)
    UpdateTicketNoteView()    
    if !($(this).data('allow-add-notes'))
      $('.ticket_notes_form #add_new_notes').hide()
    $('#ticketModalLabel').text("Ticket Notes #{ticket_id}")
    $(this).removeClass('btn-default').addClass('btn-success')

  $('.ticket_notes_form #add_new_notes').on 'click', (e) ->
    e.preventDefault()
    $('.ticket_notes_form #notes, #add_new_notes').hide()
    $('.ticket_notes_form #add_notes, #submit_notes').show()
    $('.ticket_notes_form #submit_notes').attr('value', 'Submit Notes').data('type', 'notes')
    $('.back_arrow').show()

  $('.back_arrow').click ->
    UpdateTicketNoteView()

  $('.ticket_notes_form #submit_notes').on 'click',(e) ->
    e.preventDefault()
    ticket_id = $('.ticket_notes_form #ticket_id').val()
    new_notes = $('.ticket_notes_form #add_notes').val()
    token = $(".ticket_notes_form input[name='authenticity_token']").val();
    if $(this).data('type') != 'reopen' 
      $.ajax(AddTicketNotes(ticket_id, new_notes, token))
    else if new_notes.trim() == ''
      $('#ticket_notice_message').show().html('Please add notes.')
    else  
      $.ajax(ReopenTicket(ticket_id, new_notes, token))

  $('.ticket_reopen').on 'click',(e) ->
    $('.ticket_notes_form #notes, #add_new_notes').hide()
    $('.ticket_notes_form #add_notes').show().val('')
    $('.ticket_notes_form #ticket_id').val($(this).data('id'))
    $('.ticket_notes_form #submit_notes').show().attr('value', 'Reopen').data('type', 'reopen')
    $('#ticket_notice_message').hide()
    
  AddTicketNotes = (ticket_id, notes, token) ->
    type: 'POST'
    data:
      ticket_id: ticket_id
      new_notes: notes
      authenticity_token: token
    url: '/tickets/add_ticket_notes'
    success: (data,status) ->
      if data.error
        alert(data.error)
      else
        updated_notes = data.notes.split(' ... ').reverse().join("\r\n")
        UpdateTicketNoteView()
        $('.ticket_notes_form #notes').val(updated_notes)
        $("#open_modal_#{ticket_id}").data('notes', updated_notes)
        

  ReopenTicket = (ticket_id, notes, token) ->
    type: 'POST'
    data:
      ticket_id: ticket_id
      notes: notes
      authenticity_token: token
    url: '/tickets/ticket_reopen'
    success: (data,status) ->
      if data.error
        $('#ticket_notice_message').show().html(data.error)
      else
        $('#ticket_notice_message').show().html(data.success)
        $("#ticket_reopen_#{ticket_id}").hide()
        $('.ticket_notes_form #submit_notes').hide()
        $("#ticket_#{ticket_id}_state").html('reopen')
        $("#open_modal_#{ticket_id}").data('notes', data.notes)

  $('#ticket_notes_modal .close').on 'click', ->
    $('.t_notes').removeClass('btn-success').addClass('btn-default')

  $('.tickets_collapse').click ->
    span_element = $(this).children('span')
    if span_element.hasClass('glyphicon-chevron-down')
      $('.tickets_collapse span').removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down')
    span_element.toggleClass('glyphicon-chevron-down').toggleClass('glyphicon-chevron-up')

$ ->
  $('.reverse_shipment').click (e) ->
    e.preventDefault()
    id = $(this).data('shipment-id')
    $.ajax(reverseShipment($(this), id))

reverseShipment = (element, id) ->
  type: 'POST'
  url: '/shipments/'+ id + '/reverse'
  beforeSend: (xhr) ->
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
  success: (data,status) ->
    if data.error
      alert(data.error)
    else
      element.text("Reversed")
      element.contents().unwrap()
      $("#shipment_state_#{id}").text('processing_abandoned')

$ ->
  $('.replacement_need').change ->
    item_id = $(this).data('id')
    if $("option:selected", this).val()=='Yes'
      $('.rtv_quantity_input_' + item_id).show()
    else
      $('.rtv_quantity_input_' + item_id).hide()

$ ->
  $('#order_sms_form').submit (e) ->
    e.preventDefault()
    form = $('#order_sms_form')
    submit = form.find('input[type=submit]')
    submit.val('processing')
    alert = $('#order_sms_form').find('.alert')
    alert.removeClass('alert-danger alert-success')
    submit.prop('disabled', true);
    $.ajax
      type: 'POST'
      data:
        sms_type: form.find('select').val()
      url: form.attr('action')
      complete: (e, xhr, settings) ->
        if e.status == 200
          alert.addClass('alert-success')
          alert.html('Message sent successfully')
        else
          alert.addClass('alert-danger')
          alert.html('Unable to send message')
        submit.val('send')
        submit.prop('disabled', false);
        alert.show()

$ ->  
  $('#stitching_done_panel_form #stitching_done_panel_submit').on 'click', (e) ->
    e.preventDefault()
    stitch_done_rack = $(this).attr('data-stitching-done-rack')
    scanned_barcode = $('#input_barcode').val()    
    if scanned_barcode != ''
      barcode_data = scanned_barcode.split('-')
      barcode_length = barcode_data.length
      if jQuery.inArray(barcode_length, [2,3,4,5]) != -1 
        stitchingDoneOperation($(this), stitch_done_rack)      
      else
        alert('Wrong Barcode Scanned.')
    else
      alert('Please Scan Barcode.')

$ ->
  VALID_EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/i
  $('#cc_email').keyup ->
    validity_flag = true
    email = $('#cc_email').val()
    email_id  = email.split(',')
    $.each(email_id, (_, value) ->
      if !VALID_EMAIL_REGEX.test(value.trim()) 
        validity_flag = false
        return false
    );
    if validity_flag or email==''
      $('#cc_email').removeAttr 'style'
      $('#cc_email')[0].setCustomValidity '';
    else
      $('#cc_email').css 'border', '1px solid red'
      $('#cc_email')[0].setCustomValidity 'Enter valid email ids with comma(,) seperation'

$ ->
  $('#email-report').click ->
    alert("Your report will be mailed to you soon")

  $("#accounts_report_form input[name='report_type'").change ->
    if $(this).val() == 'purchase'
      $('#accounts_report_form #date, #accounts_report_form #date_label').removeClass('hide').prop('required', true)
    else
      $('#accounts_report_form #date, #accounts_report_form #date_label').addClass('hide').prop('required', false)