# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/

$ ->
  $("#delete_designer_image").click (e) ->
    e.preventDefault()
    $(".show_designer_image").hide()
    $(".show_file_field").show()

  $("#delete_cancelled_cheque_image").click (e) ->
    e.preventDefault()
    $(".show_cancelled_cheque_image").hide()
    $(".show_file_field_cancelled_cheque").show()

  $("#delete_pan_card_image").click (e) ->
    e.preventDefault()
    $(".show_pan_card_image").hide()
    $(".show_file_field_pan_card").show()

  $("#delete_vat_certificate_image").click (e) ->
    e.preventDefault()
    $(".show_vat_certificate_image").hide()
    $(".show_file_field_vat_certificate").show()

  $("#delete_tin_certificate_image").click (e) ->
    e.preventDefault()
    $(".show_tin_certificate_image").hide()
    $(".show_file_field_tin_certificate").show()

  $("#delete_cst_certificate_image").click (e) ->
    e.preventDefault()
    $(".show_cst_certificate_image").hide()
    $(".show_file_field_cst_certificate").show()
  $("#delete_gst_certificate_image").click (e) ->
    e.preventDefault()
    $(".show_gst_certificate_image").hide()
    $(".show_file_field_gst_certificate").show()
  $("#delete_msme_certificate_image").click (e) ->
    e.preventDefault()
    $(".show_msme_certificate_image").hide()
    $(".show_file_field_msme_certificate").show()

checkBucketWise = (current_radio_btn) ->
  table = current_radio_btn.closest('tbody');
  $('input[type=checkbox]',table).removeAttr('checked').val('').addClass('hide')
  $('.check_box_'+current_radio_btn.val()).val('selected').removeClass('hide').prop('checked','checked')

$ ->
  $('.radio_shipment_buckets').change ->
    checkBucketWise($(this))

$ ->
  $('#select_all_items').change ->
    if $(this).is(":checked")
      $('input[type=radio]','table').prop('disabled', true)
      $('input[type=checkbox]','table').val('selected').removeClass('hide').prop('checked','checked')
    else
      $('input[type=radio]','table').prop('disabled', false)
      checkBucketWise($('input[type=radio]').first())

$ ->
  $('#shipment_dispatch_form').submit ->
    $("input[type=checkbox]").removeAttr("disabled")


$ ->
  $('.information-box .show-me').click ->
    $(this).parent().addClass('active')
  $('.information-box .hide-me').click ->
    $(this).parent().removeClass('active')

$ ->
  $("#create_new_design").click ->
    $("#create_new_form").toggle()

paramDesignerFilter = (sortVal='published', sortName='unpublished', designer) ->
 type: "GET"
 data: 
   sort: -> if sortVal then sortVal else $('#sort_hidden').val()
   sortname: sortName
 url: '/designers/' + designer + '/boutique'
 beforeSend: ->
   $('#loading-image').show()
 complete: ->
   $('#loading-image').hide() 
 datatype: 'script'       

$ ->
  $("#sort_select_designer").change ->
    $("#catalog").hide()
    sort = $("option:selected", this).val()
    sortName = $("option:selected", this).text()
    designer = $("#designer_hidden_id").val()
    $.ajax(paramDesignerFilter(sort, sortName, designer))
    
$ ->
  $('#display_rows').click ->
    $(this).hide()
    $('.hide-table-row').show()
$ ->
  $('#set_vacation_mode_link').click ->
    $("#vacation-dialog-form").modal('show')

$ ->
  $('.set_inactive_link').click ->
    $("#deactivate-dialog-form").modal('show')    

$ ->
  $(document).on 'click', '.coupon-claim', (e) ->
    if $('#is_signed_in').attr('value') == 'not-signedin'
      $('#dialog-form').show()
      $('#dialog-form').dialog 'open'
      return false
    return
    
$ ->
  $(document).on 'click', '.unpublish_designs_checkbox_all', ->
    $('input[type="checkbox"]').prop 'checked', 'true'    

$ ->
  $(document).on 'click', '.unpublish_designs_checkbox_none', ->
    $('input[type="checkbox"]').removeAttr('checked')

$ ->
  $(document).on 'click', '.orders_checkbox_all', ->
    $('input[type="checkbox"]').prop 'checked', 'true'

$ ->
  $(document).on 'click', '.orders_checkbox_none', ->
    $('input[type="checkbox"]').removeAttr('checked')


  $(document).on 'click', '.payout_checkbox_all', ->
    $('input[type="checkbox"]').prop 'checked', 'true'


  $(document).on 'click', '.payout_checkbox_none', ->
    $('input[type="checkbox"]').removeAttr('checked')

$ ->
  $(document).on 'click', '.genearte_payout', ->
    if $('#freeze_payout')[0].checked == true
      confirm("Designer Orders will be FREEZED !!!. Are you sure?")

$ ->
  $(document).on 'click', '.cancel_cod_order_button', ->
    confirm("Are you sure to cancel orders ?")

    
paramUpdateDesignQuantity = (idVal, quantityVal) ->
  type: "POST"
  beforeSend: (xhr) ->
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
  data:
    id: idVal   
    quantity: quantityVal
  url: '/designs/update_design_quantity'
  datatype: 'JSON'
  success: (data) ->
    updateDesignDetails(idVal,data)

updateDesignDetails = (idVal,data) ->
  if data.status == 'ok'
    $('#'+idVal+'_state').html(data.state)
    $('#'+idVal+'_effective_price').html(data.effective_price)
    new PNotify({text: 'Design ' + idVal + ' updated Sucessfully', type: 'success', styling: 'bootstrap3'})
  else if data.status == 'error'
    new PNotify({title: 'Cannot update design', text: data.error_message, type: 'error', styling: 'bootstrap3'})

paramUpdateDesignPrice = (idVal, value) ->
  type: "POST"
  beforeSend: (xhr) ->
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
  data:
    id: idVal
    price: value
  url: '/designs/update_design_quantity'
  datatype: 'JSON'
  success: (data) ->
    updateDesignDetails(idVal,data)

paramUpdateDesignDiscountPercent = (idVal, value) ->
  type: "POST"
  beforeSend: (xhr) ->
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
  data:
    id: idVal
    discount_percent: value
  url: '/designs/update_design_quantity'
  datatype: 'JSON'
  success: (data) ->
    updateDesignDetails(idVal,data)


$ ->
  $(document).on('click','.update_design_quantity',(e) ->
    id = $(this).attr('id').split('_')[1]
    $.ajax(paramUpdateDesignQuantity(id, $("#" + "quantity_" + id).val())))     

  $('.enabled_design_quantity').keypress (e) ->
    if(e.which == 13)
      e.preventDefault()
      $.ajax(paramUpdateDesignQuantity($(this).attr('id').split('_')[1],$(this).val()))
    return

  $(document).on('click','.update_design_price',(e) ->
    id = $(this).attr('id').split('_')[2]
    $.ajax(paramUpdateDesignPrice(id, $("#" + "price_" + id).val())))     

  $('.enabled_design_price').keypress (e) ->
    if(e.which == 13)
      e.preventDefault()
      $.ajax(paramUpdateDesignPrice($(this).attr('id').split('_')[1],$(this).val()))
    return

  $(document).on('click','.update_design_discount_percent',(e) ->
    id = $(this).attr('id').split('_')[3]
    $.ajax(paramUpdateDesignDiscountPercent(id, $("#" + "discount_percent_" + id).val())))     

  $('.enabled_design_discount_percent').keypress (e) ->
    if(e.which == 13)
      e.preventDefault()
      $.ajax(paramUpdateDesignDiscountPercent($(this).attr('id').split('_')[2],$(this).val()))
    return


$ ->
  if $('#designer_report_list').length > 0
    $('#designer_report_list').tabs ->
      collapsible: true
      active: false
      alwaysOpen: false



$ ->
  $('.edit').click ->
    alert "You can now edit this design "
    a = "row_#{this.id}"
    $("##{a}").find(':disabled').prop('disabled',false)

populateSelect  = (element, index ,Array) ->
  $("#bulk_upload_ids").get(0).options[index+1] = new Option(element, element)

getBulkUploadIds = (idVal) ->
  type: "GET"
  data:
    designer_id: idVal
  url: '/designers/get_bulk_upload_ids'
  datatype: 'JSON'
  success: (data) ->
    if data.status == 'ok'
      $('#bulk_upload_ids').show()
      $('#review_list').show()
      data.designer_ids.forEach populateSelect
    else
      $('#bulk_upload_ids').hide()
      $('#review_list').hide()
      alert data.errors

$ ->
  $('#select_designer').change ->
    $("#bulk_upload_ids").get(0).options[0] = new Option("Select Bulk Upload Id", "-1")
    $.ajax(getBulkUploadIds($('#select_designer').val()))
   
$ ->
  if $('#txtFrom').length
    $('#txtFrom').datepicker
      numberOfMonths: 1
      onSelect: (selected) ->
        dt = new Date(selected)
        dt.setDate dt.getDate() + 1
        $('#txtTo').datepicker 'option', 'minDate', dt
        return
    $('#txtTo').datepicker
      numberOfMonths: 1
      onSelect: (selected) ->
        dt = new Date(selected)
        dt.setDate dt.getDate() - 1
        $('#txtFrom').datepicker 'option', 'maxDate', dt

$(document).ready ->
  $('#bulk_upload_xl').change ->
    file = @files[0]
    name = file.name.split('.')[file.name.split('.').length-1]
    if name.toLowerCase() != 'xlsx'
      alert 'Incorrect File Format!'
      $('#bulk_upload_xl').val('')
    return

  checkCsvFile = (files,file_field) ->
    file = files[0]
    name = file.name.split('.')[file.name.split('.').length-1]
    console.log(name)
    file_field_id = "##{file_field}"
    if name.toLowerCase() != 'csv'
      alert 'Incorrect File Format!'
      $(file_field_id).val('')

  $('#csv_file').change ->
    checkCsvFile(@files,@id)

  $('#csv_file_courier_reconciliation').change ->
    checkCsvFile(@files,@id)
    $('#csv_file').val('')

  $('#save_payout_csv_file').change ->
    checkCsvFile(@files,@id)

  $('#paid_payout_csv').change ->
    checkCsvFile(@files,@id) 
  
  $('#image_en_file').change ->
    file = @files[0]
    window.URL = window.URL or window.webkitURL
    name = file.name.split('.')[file.name.split('.').length-1]
    if (name.toLowerCase() != 'jpg' && name.toLowerCase() != 'png')
      alert 'Incorrect File Format! Only png/jpg are supported'
      $('#image_en_file').val('')
      $('#image_preview').empty()
    else
      img = new Image
      img.onload = ->
        if @width != 1300 && @height != 500
          alert('Incorrect image dimensions. Upload image with dimensions 1300x500 only')
          $('#image_en_file').val('')
          $('#image_preview').empty()
        else if @width == 1300 && @height == 500
          $('#image_preview').empty()
          banner_img = document.createElement('img')
          banner_img.id = 'preview_img_banner'
          reader = new FileReader
          reader.onloadend = ->
            $('#subscribe_us .modal-content').css('background', '#23212C url('+reader.result+') no-repeat' );
          reader.readAsDataURL(file);
          banner_img.src = img.src
          banner_img.height = 375
          banner_img.width = 975
          banner_img.onload = ->
            window.URL.revokeObjectURL @src
          $('#image_preview').append banner_img
      img.src = window.URL.createObjectURL(@files[0])
      img.onerror = ->
        alert 'not a valid file: ' + file.type

  $('#view_preview').on('click', (event) ->
    event.preventDefault()
    if $('#preview_img_banner').attr('src') != undefined
      url = $('#preview_img_banner').attr('src')
      $('#subscribe_us').animate({'bottom': '0px'}, 1000);
      $('#subscribe_us .modal-content').css('background-size', 'cover');
      $('.btn-subscribe').prop("disabled", true)
      $('.msg').text('This is test message').show()
      $('#overlay_subscription').css('height',''+$(document).height()+'px');
    else
      alert 'Please choose image first'
  )

  $('.close_subscribe').click ->
    $('#subscribe_us').animate({'bottom': '-436px'}, 1000);
    $('#overlay_subscription').css('height','0px');

  $('.invoice_uploader #invoice_file').change ->
    file = @files[0]
    name = file.name.split('.')[file.name.split('.').length-1]
    if file.size > 5500000
      alert 'Maximum file size is 5MB'
      $(this).val('')
    if (name.toLowerCase() != 'jpeg' && name.toLowerCase() != 'jpg' && name.toLowerCase() != 'pdf' && name.toLowerCase() != 'png')
      alert 'Incorrect File Format! Please upload files in any of following format : jpeg,jpg,png,pdf'
      $(this).val('')

$ ->
  $('.claim_form_view').click ->
    data = $(this).data('attr-id')
    $('.stage-'+data).show()

paramCreateClaimForm = (designer_issue,design_id) ->
  type: 'POST'
  data:
    designer_issue: designer_issue
    design_id: design_id
  datatype: 'json'
  url: '/designers/claim_form'
  success: (data, status, jqhxr) ->
    if data.error == undefined
      new PNotify({text: data.message, type: 'success', styling: 'bootstrap3'})
      if data.design_code != undefined
        $("#requests-"+designer_issue).append("<li>You placed a request with design : <a href='/designers/" + data.designer_slug + "/designs/"+ data.cached_slug + "'>" + data.design_title + " - " + data.design_code + "</a></li>")
        $('html,body').animate({ scrollTop: $("#requests-"+designer_issue).offset().top }, 1000)
    else
      new PNotify({text: data.error, type: 'error', styling: 'bootstrap3'})

markReadyForDispatch = () ->
  type: 'GET'
  datatype: 'json'
  url: '/admin/mark_dispatch'
  success: (data, status, jqhxr) ->
    alert data.message

$ ->
  $('#ready_for_dispatch').click ->
    dispatch = confirm('Mark Orders Ready for dispatch?')
    if dispatch
      $.ajax(markReadyForDispatch())

$ ->
  $('.claim_form').on('submit', (e) ->
    e.preventDefault()
    designer_issue = $(this).find('#item').val()
    design_id = $(this).find('#design_id').val()
    issue_design_id = $(this).find('#issue_design_id').val()
    $(this).find('#design_id').val(' ')
    if issue_design_id == design_id
      new PNotify({text: 'You cannot request claim for same item', type: 'error', styling: 'bootstrap3'})
    else
      $.ajax(paramCreateClaimForm(designer_issue, design_id)))

  $('#promotion_tab').click ->
    if $('#pane_promotion_links').is(':visible')
      $('#pane_promotion_links').hide()
      $('#additional_discount_pane').hide()
    else
      $('#pane_promotion_links').show()
      $('#additional_discount_pane').hide()

  $('#additional_discount_btn').click ->
    if $('#additional_discount_pane').is(':visible')
      $('#additional_discount_pane').hide()
    else
      $('#additional_discount_pane').show()

  $('.submit #continue_btn').click ->
    $('.pane #optional_fields').removeClass('hide')
    $('.pane #step_second').addClass('hide')
    $('input#designer_business_name').focus()
    $(this).hide()

  $('.designer_notification_pane .close_btn').click ->
    $('.designer_notification_pane').hide()

$ ->
  $('.arrange_pickup_date_input').each ->
    $(this).datepicker
      dateFormat: 'yy-mm-dd'
      minDate: '+1D'
      maxDate: '+4D'

read_msg_list = []

markMessageRead = (msg_ids) ->
  type: 'POST'
  beforeSend: (xhr) ->
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
  data:
    msg_ids: msg_ids
    read_msg_list: read_msg_list
  datatype: 'json'
  url: '/messages/mark_read_msgs'
  success: (data) ->
    if data.message == 'success'
      read_msg_list = data.read_msg_ids

$ ->
  $('#designer_messages .msg_link').click ->
    if $(read_msg_list).not($(read_msg_list).not($(this).data('msg-ids'))).length == 0 && $(this).data('msg-ids').length > 0
      $.ajax(markMessageRead($(this).data('msg-ids')))

$ ->
  $('#generate_manifest_btn').on 'click', (e) ->
    if ($("input[type=checkbox]:checked").length == 0)
      e.preventDefault()
      alert 'Select atleast one item to proceed'
      return false

  $(document).ready ->
    if (/designers/i.test(window.location.href)) && ($('#WarningModel').length > 0)
      if $('#WarningModel').attr('data-notice-type') == 'gst' && $('#WarningModel').attr('data-to-show') == undefined
        $('#WarningModel').modal 'show'
      else
        notice = Cookies.get('designer_notice')
        if notice != 'true'
          Cookies.set 'designer_notice', true, expires: 1
          $('#WarningModel').modal 'show'
      return
    
    if(parseInt($('.completion_percent').text()) != 100)
      $('.profile-wiget').show(); 

$ ->
  if typeof($().popover) == 'function'
    $('[data-toggle="popover"]').popover();

$ ->
  $('#home_decor_form #home_decor_types').on 'change', (e) ->
    $('#dropbox_link').attr('href', gon.home_decor[$(this).val()])
    $('#dropbox_link').text($(this).val() + ' XLSX format download')

$ ->
  $('.grading_prmotion .more').on 'click', (e) ->
    e.preventDefault()
    type = $(this).data('type')
    state = $(this).data('state')
    window.location.href = '/grading/config?promotable_type='+type+'&state='+state

$ ->
  $('.grading_prmotion .remove').on 'click', (e)->
    e.preventDefault()
    promotable = $(this).closest('.promotable')
    $.ajax
      type: 'POST'
      url: '/grading/change_config'
      dataType: 'json'
      data:
        promotion_id: $(this).data('promotion-id')
      beforeSend: (xhr) ->
        xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))
      complete: (e, xhr, settings) ->
        if e.status == 200
          promotable.remove()

$ ->
  $(document).on 'click', '.edit_owner', (e) ->
    e.preventDefault()
    $('#edit_owner_modal select').attr('data-id', $(this).data('id'))
    $('#edit_owner_modal label').html('Change owner for '+$(this).data('name'))
    $('#edit_owner_modal').modal('show')

  $(document).on 'click', '.submit_changed_owner', (e) ->
    e.preventDefault()
    submit_btn = $(this)
    owner_modal = $('#edit_owner_modal')
    select_owner_modal =  owner_modal.find('select')
    owner_id =  owner_modal.find('option:selected').val()
    owner_name = owner_modal.find('option:selected').html()
    id = select_owner_modal.data('id')
    $.ajax
      url: '/designers/' + id + '/change_owner'
      method: 'POST'
      data:
        owner_id: owner_id
      beforeSend: ->
        submit_btn.html('Please wait')
      success: (data) ->
        submit_btn.html('Submit')
        if data['error'] != undefined
          owner_modal.find('.alert p').html(data['error']);
          owner_modal.find('.alert').show()
        else
          $('.edit_owner[data-id=' + id + ']').parents('tr').find('.owner_name').html(owner_name)
          owner_modal.modal('hide')

copyToClipboard = (elem) ->
  elem = elem[0]
  origSelectionStart = undefined
  origSelectionEnd = undefined
  target = elem
  origSelectionStart = elem.selectionStart
  origSelectionEnd = elem.selectionEnd
  # select the content
  currentFocus = document.activeElement
  target.focus()
  endLength = target.value.length
  target.setSelectionRange 0, endLength
  # copy the selection
  succeed = undefined
  try
    succeed = document.execCommand('copy')
  catch e
    succeed = false
  # restore original focus
  if currentFocus and typeof currentFocus.focus == 'function'
    currentFocus.focus()
  # restore prior selection
  elem.setSelectionRange origSelectionStart, origSelectionEnd
  succeed

$ ->
  $('.create-link').on 'click',(e) ->
    $this = $(this)
    code = $this.data('code')
    succeed = copyToClipboard($('#link-'+code))
    new PNotify({text: "Copied to clipboard!", type: 'success', styling: 'bootstrap3'})
    $this.text("COPY LINK").fadeIn(500)

$ ->
  $('.cod_confirmation_message').on 'click', (e) ->
      if (confirm('Are You Sure?') == true)
        order_number = $(this).data('order-number')
        $.ajax(sendConfirmationSms(order_number))
        $('#message_button_'+order_number).hide()

  sendConfirmationSms = (order_number) ->
    type: 'POST'
    data:
      order_number : order_number
    url: '/orders/send_sms_for_cancellation_of_cod_order'
    success: (data) ->
      if data.error == true
        $('#message_status_'+order_number).addClass('alert alert-danger').text(data.error_text).show()
      else
        $('#message_status_'+order_number).addClass('alert alert-success').text('Message sent Successfully.').show()

$(document).on 'click', '.delete_batch', (e) ->
  if batch_ids.value == ''
    alert ('Please select batch ids')
  else
    e.preventDefault()
    $.ajax
      url: 'delete_batch'
      type: 'POST'
      data:
        batch_ids: batch_ids.value 
      dataType: 'json'
      success: (data) ->
        if data.error? 
          alert data.error
        else
          alert data.notice
          location.reload()
