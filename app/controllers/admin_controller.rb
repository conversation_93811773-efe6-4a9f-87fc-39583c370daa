class AdminController < ApplicationController
  before_filter :authenticate_account!
  load_and_authorize_resource
  skip_authorize_resource :only => [:designer_report, :designer_issues, :domestic_dispatch_report,:mark_order_urgent,:get_notes_bifurcated,:check_for_unique_transaction_id,:get_transaction_id_for_duplicate_order,:compare_designer_performance,:update_user_phone]
  skip_before_filter :verify_authenticity_token, only: [:create_designer_issue,:mark_order_urgent,:get_notes_bifurcated,:check_for_unique_transaction_id,:get_transaction_id_for_duplicate_order,:compare_designer_performance]
  layout :determine_layout
  include LocalyticsNotification

  require 'csv'
  def fb_publish_new
    @fb_pages = current_account.accountable.fb_pages
    # Find last post of each page
    # When user selects a given page, next post time should change accordingly (+1 hour), automatically.
  end


  def add_designs_to_collection

  end

  def move_collection_to_category

  end

  def add_designs_to_featured_products
    if request.post? && params[:perform_action].present? && params[:design_ids].present?
      begin
        if params[:perform_action] == 'add_products'
          Design.in_stock.where(id: params[:design_ids]).update_all(featured_product: true)
        elsif params[:perform_action] == 'remove_products'
          Design.in_stock.where(id: params[:design_ids]).update_all(featured_product: false)
        end
        Rails.cache.delete(:featured_products_desktop) if params[:design_ids].size > 20
        render json: {success: true}
      rescue Exception => e
        render json: {success: false}
      end
    else
      @featured_products = Design.featured_products_memcached
    end
  end

  def upload_newsletter_image
    @integration_status = 'new'
  end

  def get_tally_report
    if request.post?
      if params[:entry_start] && params[:csv_file].present?       
        filename  = "accounts/report_formation/" + "#{Time.now.strftime('%m_%d_%H_%M') + SecureRandom.hex(7) +'.csv'}"
        AwsOperations.create_aws_file(filename, params[:csv_file])
        if params[:report_type] == 'purchase'
          SidekiqDelayGenericJob.perform_async("Admin", nil, "generate_tally_report", params[:date], params[:entry_start], filename, {"#{current_account.class}": current_account.id})
          #Admin.sidekiq_delay
          #     .generate_tally_report(params[:date], params[:entry_start], 
          #                            filename, current_account)
        elsif params[:report_type] == 'gstr_3b'
          SidekiqDelayGenericJob.perform_async("Admin", nil, "generate_je_report", params[:entry_start].to_i, filename, {"#{current_account.class}": current_account.id})
          #Admin.sidekiq_delay
          #     .generate_je_report(
          #       params[:entry_start].to_i,
          #       filename, current_account
          #     )
        elsif params[:report_type] == 'paypal_entry_report'
          SidekiqDelayGenericJob.perform_async("Admin", nil, "generate_tally_entry_report", params[:entry_start].to_i, filename, {"#{current_account.class}": current_account.id})
          #Admin.sidekiq_delay
          #     .generate_tally_entry_report(
          #       params[:entry_start].to_i,
          #       filename,
          #       current_account
          #     )
        else
          SidekiqDelayGenericJob.perform_async("Admin", nil, "generate_report_from_bank_statement", params[:entry_start].to_i, filename, {"#{current_account.class}": current_account.id})
          #Admin.sidekiq_delay
          #     .generate_report_from_bank_statement(
          #       params[:entry_start].to_i,
          #       filename,
          #       current_account
          #     )
        end
        redirect_to (request.env["HTTP_REFERER"] || get_tally_report_path), :notice => 'Report Generation is scheduled. It will be mailed to you.'
      end
    end
  end

  def cache_clear
    redirect_to root_path, notice: 'You are not authorized to access this page.' unless ACCESSIBLE_EMAIL_ID['cache_clear'].to_a.include?(current_account.try(:email))
    cache = CacheClearOgJob.new
    case params[:platform]
    when 'All'
      cache.perform
    when 'Mobile'
      cache.clear_mobile_cache
    when 'API'
      cache.clear_api_cache
    when 'Luxe'
      cache.clear_luxe_cache
    when 'Desktop'
      Rails.cache.clear
    end
    if params[:platform].present?
      MarketingTeamMailer.notify_cache_clear(params[:Reason],params[:platform],current_account.email).deliver_now 
      flash[:success] = "cache clear has been done on #{params[:platform]}"
    end
  end

  def upload_newsletter_image_create
    begin
      file_ext = params[:image_en_file].original_filename.split('.').last
      if file_ext == 'png' || file_ext == 'jpg'
        connection = Fog::Storage.new({
          :provider                 => 'AWS',
          :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
          :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
          :region                   => ENV['AWS_REGION']
        })

        directory = connection.directories.new(
          :key    => ENV['S3_BUCKET']
        )

        filename  = "newsletter_subscription_image/" + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6)}" + ".#{file_ext}"

        file =  directory.files.create(
          :key    => filename,
          :body   => params[:image_en_file].read,
          :public => true
        )
        append_url = (Rails.env.production? || Rails.env.admin? || Rails.env.staging?) ? "#{ENV['PREFIX_AKAMAI_ASSETS_URL']}0.mirraw.com" : 'mirraw-test.s3.amazonaws.com'
        full_path =  "#{append_url}/#{filename}"
        system_constant_img = SystemConstant.find_by_name('SUBSCRIBE_NEWSLETTER_BANNER_IMAGE')
        system_constant_img.value = "#{full_path},#{current_account.email}"
        if system_constant_img.save!
          Rails.cache.delete('newsletter_banner_image')
          redirect_to :back, :notice => "Successfully uploaded subscribtion image"
        else
          redirect_to :back, :alert => "Problem in uploading image!!! Try after sometime."
        end
      else
        redirect_to :back, :alert => 'Incorrect File Format! Only png/jpg are supported'
      end
    rescue => error
      redirect_to :back, :alert => error.message
    end
  end

  def upload_designs_size_chart
    if request.post?
      csv = Roo::Spreadsheet.open(params[:file].tempfile, extension: :csv)
      begin
        image = Image.first
        Image.sidekiq_delay(queue: 'low').save_size_chart_images(csv)
        # SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(image , image.id ,"save_size_chart_images",{csv.class => csv.id})
        # image.sidekiq_delay(queue: 'low').save_size_chart_images(csv)
      rescue => error
        ExceptionNotify.sidekiq_delay.notify_exceptions('Upload Deisgn Error', 'Size Size Chart', { error: error.inspect })
      end # End of rescue
      flash[:notice] = 'File is being Processed'
    end
  end

  def enable_design_video
    redirect_to root_path, notice: 'You are not authorized to access this page.' unless ACCESSIBLE_EMAIL_ID['enable_design_video'].to_a.include?(current_account.try(:email))
    if params[:has_video].present? && params[:design_ids].present?
      enable_design_video = params[:has_video]
      design_ids = params[:design_ids].split(/\r?\n|,/)
      designs = Design.where(:id => design_ids)
      designs.update_all(:has_video => enable_design_video)
    end
    if params[:has_video].present? && params[:design_ids].present?
      flash[:success] = "Update complete"
    end
  end

  def move_collection_to_category_create
    if params[:collection_title].present? && params[:category_title].present?
      collection_title = params[:collection_title]
      category_title = params[:category_title]
      category = Category.find_by_namei(category_title)
      designs = Design.published.tagged_with(collection_title)
      #Does it exist?
      if category.present? && designs.present?
        designs.each do |design|
          design.collection_list.remove(collection_title)
          design.categories << category
          #Was the collection moved successfully?
          if design.save
            flash[:notice] = "Moved Successfully"
            log = CollectionLog.new(collection_name: collection_title, moved_by: current_account.id)
            log.save
          else
            flash[:alert] = "There was an error while moving the collection"
          end
        end
      else
        flash[:alert] = "Please re-check if the category and collection names are valid"  
      end
    else
        #Fields are empty
        flash[:alert] = "Fields cannot be empty"
    end
    #Success
    redirect_to :back
  end 

  def add_designs_to_collection_create
    if params[:tag_type].present? && params[:tag_title].present? && params[:design_ids].present? && params[:app_name].present?
      tag_type = params[:tag_type] # Either "collection" or "catalogue"
      tag_title = params[:tag_title].downcase
      app_name = params[:app_name]
      design_ids = params[:design_ids].split(/\r?\n|,/)
      designs = Design.where(id: design_ids)
  
      # Tag designs based on the selected tag type
      designs.each do |design|
        if tag_type == 'collection'
          design.collection_list.add(tag_title)
        elsif tag_type == 'catalogue'
          design.catalogue_list.add(tag_title)
        end
        design.skip_after_save_callback, design.skip_before_save_callback = true, true
        design.save(validate: false)
      end
  
      # Save the tag with the associated app name
      tag = ActsAsTaggableOn::Tag.find_or_initialize_by(name: tag_title)
      tag.app_name = app_name if app_name.present?
      tag.save
  
      # Redirect to the appropriate URL
      if tag_type == 'collection'
        redirect_to store_collection_url(params[:tag_title])
      elsif tag_type == 'catalogue'
        redirect_to store_catalogue_url(params[:tag_title])
      end
    else
      flash[:error] = "All fields are required."
    end
  end

  def add_designs_to_category
    categories = Category.get_name_to_ids.map{|k,v| [v,k]}.to_h
    if request.get?
      gon.category_list = categories.keys
    elsif request.post?
      notice = params[:action_type] == 'Remove' ? 'All designs have single category so cannot be removed' : 'Something went wrong'
      category_id = categories[params[:category_list]]
      if params[:category_list].present? && params[:design_ids].present? && category_id.present?
        design_ids = params[:design_ids].split(/\W+/).uniq.map(&:to_i)
        if params[:category_list].include? 'Premium'
          AdminController.sidekiq_delay(queue: 'critical')
                         .mark_design_premium_or_non_premium(
                           design_ids,params[:action_type]
                         )
        end
        notice = "#{params[:action_type]} Successful !" if CategoriesDesign.add_designs(design_ids, category_id, params[:action_type])
      end
      redirect_to :back, :notice => notice
    end
  end

  def add_designs_to_oos_alert
    @integration_status = 'new'
  end

  def add_designs_to_oos_alert_create
    if params[:email].present? && params[:design_ids].present?
      designs = get_design_ids
      if designs.present? && designs[:design_ids].present?
        designs[:design_ids].each do |design_id|
          oos_alert = OosAlert.new(email: params[:email], design_id: design_id,
            active: true, ad_url: params[:ad_url], campaign_name: params[:campaign_name])
          if oos_alert.save
            flash[:notice] = 'done.'
          else
            flash[:alert] = oos_alert.errors.messages.values.join(', ')
          end
        end
      else
        flash[:alert] = "Designs are not available."
      end
    else
      flash[:alert] = "One or More Parameters is missing."
    end
    redirect_to :back
  end


  def bulk_product_list_new

  end

  def find_property_values_name(property_name, design)
    property = Property.find_by_name property_name
    design.property_values.select(:name).where(property_id: property.id).try(:first).try(:name) if property.present?
  end

  def view_for_bulk_edit
    designer_batch = DesignerBatch.find(params[:designer_batch_id])
    state = params[:state].presence || 'review'
    if ['',nil].include? designer_batch.version
      designs = designer_batch.designs.where(state: state)
      rows = []
      designs.each do |design|
        row = []
        row[0]  = design['design_code']
        row[1]  = design['title']
        row[36] = design['embellish']
        row[38] = design['description']
        row[3]  = design['package_details']
        row[28] = design['price']
        row[29] = design['discount_percent']
        row[4]  = design['weight']
        row[26] = design['tag_list']
        row[35] = design['pattern']
        row[34] = design['region']
        row[27] = design['quantity']

        design.images.each_with_index do |image, ind|
          row[21 + ind] = 'http:' + image.photo.to_s
        end

        row[5]  =  find_property_values_name('fabric_of_saree', design)
        row[9]  =  find_property_values_name('work', design)
        row[10] =  find_property_values_name('type', design)
        row[6]  =  find_property_values_name('saree_color', design)
        row[12] =  find_property_values_name('fabric_of_blouse', design)
        row[13] =  find_property_values_name('blouse_color', design)
        row[16] =  find_property_values_name('blouse_work', design)
        row[19] =  find_property_values_name('petticoat_color', design)
        row[20] =  find_property_values_name('fabric_of_petticoat', design)
        row[37] =  find_property_values_name('celebrity', design)
        row[30] =  find_property_values_name('occasion', design)
        row[31] =  find_property_values_name('look', design)
        row[32] =  find_property_values_name('saree_border', design)
        row[33] =  find_property_values_name('pallu_style', design)
        row[2] = design.try(:category_parents_name).try(:last)

        d = design.designable
        if d.present?
          row[8]  = d['width']
          row[7]  = d['length']
          row[6]  = d['saree_color']
          row[11] = d['blouse_available']
          row[15] = d['blouse_image']
          row[14] = d['blouse_size']
          row[12] = d['blouse_fabric']
          row[13] = d['blouse_color']
          row[16] = d['blouse_work']
          row[17] = d['petticoat_available']
          row[18] = d['petticoat_size']
          row[19] = d['petticoat_color']
          row[20] = d['petticoat_fabric']
          row[39] = 'Saree'
        end
        row[40] = design['id']
        rows << row
      end
      respond_to do |format|
        format.html
        format.csv { send_data to_csv(rows), filename: "#{designer_batch.designer.name}_Batch_#{params[:designer_batch_id]}_#{state}.csv" }
      end
    else
      rows = designer_batch.to_csv(state: state)
      respond_to do |format|
        format.csv { send_data to_csv(rows,false), filename: "#{designer_batch.designer.name}_Batch_#{params[:designer_batch_id]}_#{state}.csv" }
      end
    end
  end

  def to_csv(rows, head=true)
    CSV.generate do |csv|
      csv << ['design_code', 'title', 'category', 'package details', 'weight_in_gms', 'fabric_of_saree', 'saree_color', 'length_of_saree_in_metres', 'width_of_saree_in_inches', 'work', 'type', 'blouse_availability', 'fabric_of_blouse', 'blouse_color', 'size_of_blouse_in_cms', 'blouse_as_shown_in_the_image', 'blouse_work', 'petticoat_availability', 'size_of_petticoat_metres', 'color_of_petticoat', 'fabric_of_petticoat', 'image', 'image1', 'image2', 'image3', 'image4', 'tag_list', 'quantity', 'price', 'discount_percent', 'occasion', 'look', 'saree_border', 'pallu_style', 'region', 'pattern', 'embellish', 'celebrity', 'description', 'product_type', 'mirraw_id'] if head.present?
      rows.each do |row|
        csv << row
      end
    end
  end



  def download_bulk_edit
    state = params[:state].presence || 'review'
    @states= ['review', 'processing', 'reject']

    @designer_batch_details = DesignerBatch.joins(:designs).where('designs.state = ?', state).uniq.order('designer_batches.id asc')
    if (version = params[:version]) != "All"
      @designer_batch_details = @designer_batch_details.where(version: version.presence)
    end
    @designer_batch_id = DesignerBatch.joins(:designs).where('designs.state = ?', state).where(params[:version] != 'All' ? {version: params[:version]} : nil).order('designer_batches.id asc').uniq.pluck(:id)
    @designer_batch_id.prepend(nil)
    @version = Rails.cache.fetch(:all_designer_batch_version, expires_in: 1.day) do
      DesignerBatch.pluck(:version).uniq.prepend('All')
    end
    if params[:batch_id].present?
      @designer_batches = DesignerBatch.where(id: params[:batch_id]).paginate(page:1)
    else
      @designer_batches = @designer_batch_details.preload(:designer).paginate(page: params[:page], per_page: 40)
    end
    if request.post?
      error = ''
      begin
        csv = Roo::Spreadsheet.open(params[:file].tempfile, extension: :csv)
      rescue => e
        error = e
      end
      if (csv.try(:first) === ['design_code', 'title', 'category', 'package details', 'weight_in_gms', 'fabric_of_saree', 'saree_color', 'length_of_saree_in_metres', 'width_of_saree_in_inches', 'work', 'type', 'blouse_availability', 'fabric_of_blouse', 'blouse_color', 'size_of_blouse_in_cms', 'blouse_as_shown_in_the_image', 'blouse_work', 'petticoat_availability', 'size_of_petticoat_metres', 'color_of_petticoat', 'fabric_of_petticoat', 'image', 'image1', 'image2', 'image3', 'image4', 'tag_list', 'quantity', 'price', 'discount_percent', 'occasion', 'look', 'saree_border', 'pallu_style', 'region', 'pattern', 'embellish', 'celebrity', 'description', 'product_type', 'mirraw_id'] and error.blank? )
        directory = Fog::Storage.new({
            provider: 'AWS',
            aws_access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
            region: ENV.fetch('AWS_REGION')
          }).directories.new(key: ENV.fetch('S3_BUCKET'))

        filename  = "admin_bulk_edit_uploads/#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(2) +'.csv'}"
        # upload that file
        file = directory.files.create(
          :key    => filename,
          :body   => params[:file].read,
          :public => true
        )
        fileurl = file.public_url
        design_temp = Design.first
        # design_temp.sidekiq_delay(queue: 'low').update_bulk_designs(fileurl)
        SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(design_temp.class.to_s , design_temp.id ,"update_bulk_designs",fileurl)
        flash[:notice] = 'Records Being Processed And will reflect in 30 minutes'
      else
        flash[:notice] = 'File Processing Failed please check your CSV'
      end
    end
  end

  def bulk_product_list_create
    @products = params[:links].split(/\r?\n|,/)
    @products.reject!{|l| l.empty?}
    #logger.info @products

    @designs = Array.new
    # Go through each link and see what it is ... Guessing is fine.
    @products.each do |product|
      if ((product.strip =~ /^\d+$/) == 0)
        design = Design.where(:id => product.strip).first
        @designs << design if design && design.quantity > 0
      elsif product.strip.include?('www')
        # else this should be link
        /.*\/designers\/(.*)\/designs\/(.*)/.match(product.strip)
        designer_id = $1
        design_id = $2
        design = Design.where(:cached_slug => design_id).first
        @designs << design if design && design.quantity > 0
      end
    end
    @cart = Cart.create(:hash1 => SecureRandom.hex(16))
    session[:cart_id] = @cart.id

    @designs.each do |design|
      @line_item = @cart.add_product(design: design)
      @line_item.quantity = 1
      @line_item.save
    end
    redirect_to cart_url(@cart.hash1)
  end

  def review_designer_batches
    multi_design_state_change(params[:design_ids], params[:state_change], params[:reason]) if params[:design_ids].present? and params[:state_change].present?
    # @designer = Designer.find_by_id(params[:designer_id]) if params[:designer_id].present?
    @version = Rails.cache.fetch(:all_designer_batch_version, expires_in: 1.day) do
      DesignerBatch.pluck(:version).uniq.prepend('All')
    end
    state, @ids = ['review','processing'], []
    @designer_name = Rails.cache.fetch(:all_designer_names_for_review,expires_in: 3.hours) do
      Designer.joins(:designer_batches).where('designer_batches.updated_at >= ?',5.months.ago).pluck(:id,:name).uniq.prepend(['All','All'])
    end
    session[:review_version] = params[:version] if params.has_key?(:version) && session[:review_version]!=params[:version]
    if (version = session[:review_version]) != "All"
      designer_batch = DesignerBatch.where(version: version.presence)
    else
      designer_batch = DesignerBatch
    end
    if params[:filter] == 'passed'
      designer_batch = designer_batch.where("(failed = ? OR failed IS NULL) AND passed > ?", 0, 0)  
    elsif params[:filter] == 'failed'
      designer_batch = designer_batch.where("failed > ?", 0)    
    end  
    if params[:designer_name].present? && params[:designer_name] != "All"
      @ids = designer_batch.joins(:designs).where(designer_id: params[:designer_name]).where('designs.state IN (?)', state).uniq.pluck(:id)
    elsif (params[:designer_name] == "All" || params[:designer_name].nil?)
      @ids = designer_batch.joins(:designs).where('designs.state IN (?)', state).uniq.pluck(:id)
    end
    if params[:date_from].present?
      params.each_key{|k| params[k]=nil  if params[k].blank? }
      params[:date_to] ||= params[:date_from] ||= Date.today.to_time.strftime('%m/%d/%Y')
      @designs = Design.includes(:images,:designer,:designable,:delay_designables,:dynamic_size_charts,categories: :dynamic_size_charts).where(state: state).where('designer_batch_id is NOT NULL')
        .created_between(params[:date_from], params[:date_to])
        .paginate(page: params[:page], per_page: 30)
    elsif params[:bulk_upload_ids].present?
      @designs = Design.includes(:images,:designer,:designable,:delay_designables,:designer_batch,:dynamic_size_charts,categories: :dynamic_size_charts).where(state: state,designer_batch_id: params[:bulk_upload_ids]).paginate(page: params[:page], per_page: 30)
      @failed_images = DelayImage.includes(:design).where(designs: {designer_batch_id: params[:bulk_upload_ids]})
    end
    # @designers = Designer.where('name is not null').order(:name).map { |i| [i.name.camelize, i.id] }
    # @designers = @designers.prepend(['Select Designer', 'Select Designer'])
     flash[:notice] = "Records Being #{params[:state_change]}" if params[:state_change].present? && params[:design_ids].present?
  end

  def fb_publish_create
    links = params[:links].split(',')
    page = FbPage.find(params[:fb_pages])

    run_at = DateTime.new(params[:date][:year].to_i, params[:date][:month].to_i, params[:date][:day].to_i, params[:date][:hour].to_i, params[:date][:minute].to_i)

    if params[:links].blank? || params[:name].blank? || params[:description].blank?
      flash[:alert] = "One or More Parameters is missing."
      redirect_to :back
      return
    end

    if Time.zone.local_to_utc(run_at) < DateTime.current.utc
      flash[:alert] = 'You cannot schedule Post for the time in the past'
      redirect_to :back
      return
    end

    if params[:date][:hour].present?
      session[:fb_page_hour] = params[:date][:hour].to_i
      session[:fb_page_day] = params[:date][:day].to_i
      session[:fb_page_id] = params[:fb_pages]
    end

    AdminController
      .sidekiq_delay_until(Time.zone.local_to_utc(run_at), queue: 'low')
      .post_to_facebook(links, page, params[:name], params[:description])
    redirect_to admin_fb_publish_new_path, notice: 'Done!'
  end

  def manage_pages
    if current_account.token?
      pages = current_graph.get_connections(current_account.uid, "accounts")
      @pages = Hash.new

      pages.each do |page|
        @pages[page['name']] = page['id']
      end
    end
  end

  def set_facebook_page
    page = current_graph.get_object(params[:page_id], :fields => "name, access_token, link")
    admin = current_account.accountable
    admin.fb_pages.build({:name => page["name"], :fb_id => page["id"], :access_token =>page["access_token"], :page_link => page["link"]  })
    admin.save
    redirect_to admin_fb_publish_new_path, :notice => "#{page["name"]} Was Linked Successfully."
  end

  def self.post_to_facebook(links, page, name, description)
    page_graph = Koala::Facebook::API.new(page.access_token)
    albuminfo = page_graph.put_object(page.fb_id, 'albums', :name => name, :description => description)

    album_id = albuminfo["id"]

    page_graph.batch do |batch_api|
      links.each do |link|
        /.*\/designers\/(.*)\/designs\/(.*)/.match(link.strip)
        designer_id = $1
        design_id = $2
        if designer_id && design_id
          designer = Designer.find(designer_id)
          design = designer.designs.includes([:images]).find(design_id) if designer
          if designer.present? && design.present?
            price_in_usd = CurrencyConvert.to_usd(design.effective_price).round(2)
            price_in_inr = design.effective_price
            price_in_myr = CurrencyConvert.to_myr(design.effective_price).round(2)

            message = "BOOK AT: " + link + " \n "
            message = message + "Product ID: " + design.id.to_s + " \n "
            message = message + "INR " + price_in_inr.to_s + " / USD " + price_in_usd.to_s + " / MYR " + price_in_myr.to_s + " \n "
            message = message + "Queries: <EMAIL> / #{MIRRAW_CONTACT_INFO}"

            batch_api.put_picture(design.master_image.photo.url(:original), { "message" => message }, album_id)
          end
        end
      end
    end
  end

  def bulk_upload_new
  end

  def bulk_upload_create
  end

  def availability_update_new
  end

  def availability_update_do
    # {design_id, quantity}
    if params[:utype] == 'design_code'
      design_codes = params[:design_codes].split(/\r?\n|,/)
      design_codes.each do |d|
        design = Design.where(:design_code => d).first
        if design
          design.quantity = 0
          design.save
        end
      end
    elsif params[:utype] == 'product_code'
      product_codes = params[:product_codes].split(/\r?\n|,/)
      product_codes.each do |d|
        design = Design.find(d)
        if design
          design.quantity = 0
          design.save
        end
      end
    else
      CSV.parse(params[:file].read, :headers => true) do |row|
        design = Design.where(:design_code => row[0]).first
        if design
          design.quantity = row[1]
          design.save
        end
      end
    end
    redirect_to admin_availability_update_new_path, :notice => "Done."
  end

  def canceled
    # All sane orders
    # default values for select_tags
    if (@start_date = params[:start_date]).present?
      @start_date = create_date(@start_date)
    else
      @start_date = Date.today
    end
    begin_day = @start_date.beginning_of_day
    end_day = @start_date.end_of_day
    sane_orders_email = Order.where('orders.confirmed_at BETWEEN ? AND  ?', begin_day, end_day).joins(:designer_orders).merge(DesignerOrder.unscoped.sane_orders).pluck(:email)
    sane_orders_email_1 = Order.where(pay_type: GHARPAY).where(state: 'pending').where('created_at BETWEEN ? AND  ?', begin_day, end_day).select('email')
    confirmed_orders_email = Order.where(state: 'confirmed').where('created_at BETWEEN ? AND  ?', begin_day, end_day).select('email')

    sane_orders_email += sane_orders_email_1 + confirmed_orders_email
    w_geo = params[:region] == 'International' ? "lower(country) <> 'india'" : (params[:region] == 'Domestic' ? "lower(country) = 'india'" : '')
    
    c_orders = Order.select('id,email,notes,created_at').includes(:line_items).where(state: 'cancel').where('created_at BETWEEN ? AND  ?', begin_day, end_day).where(w_geo)
    @order_counts={}
    orders=[]
    if c_orders
      @total_revenue_lost = 0
      already_included_canceled = Array.new
      c_orders.each do |o|
        unless sane_orders_email.include?(o.email) || already_included_canceled.include?(o.email) || (o[:notes].present? && o[:notes].include?('SUCCESS'))
          sub_total = o.line_items.to_a.sum(&:sub_total)
          orders << [o.id,sub_total,o.created_at]
          @total_revenue_lost += sub_total
          already_included_canceled << o.email
        end
        @order_counts[o.email]=@order_counts[o.email].to_i + 1
      end
    end
    orders.sort_by! {|created_at| created_at}
    orders.reverse! unless params[:sort] == 'ASC'
    @order_amount={}
    order_ids=[]
    @total_count = orders.count
    @orders = orders.paginate(page: params[:page],per_page: 10)
    @orders.each do |id,amount|
      order_ids << id
      @order_amount[id] =amount
    end    
    @canceled_orders =Order.select('id,email,notes,number,billing_country,created_at,billing_city,name').preload(:events).where(id: order_ids).sort_by{|ord| order_ids.index(ord.id)}
  end

  def view_manifest
    @integration_status = 'new'
    @designers = Designer.select('id,name').where(banned: false).order(:name)
    @shippers = Shipper.where(enabled: true, domestic: true).pluck(:name)
    manifest = []
    @shipper_designer = {}
    @shippers.each do |shipper|
      @shipper_designer[shipper.try(:downcase)] = []
    end
    start_date = params[:start_date].present? ? params[:start_date].to_date : Date.today
    search_string = "ManifestFiles/designers/#{start_date.strftime('%Y/%b/%d')}"
    search_string += "/#{params[:designer_name]}" if params[:designer_name].present? && params[:designer_name] != 'All'
    manifest = AWS::S3.new.buckets[ENV['S3_BUCKET']].objects.with_prefix("#{search_string}").collect(&:key)
    manifest.each do |file|
      file_name = file.to_s.split('/').last.to_s.split('_')
      @shipper_designer[file_name[1]] += [file_name[0]] if @shipper_designer.has_key?(file_name[1])
    end
    manifest = manifest.select{|c| c.include? params[:shipper].downcase} if params[:shipper].present? && params[:shipper] != 'All'
    @uploaded_manifest = manifest
  end

  def sales_pending
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i).try(:beginning_of_day)
    else
      @start_date = 3.days.ago.beginning_of_day
    end


    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i).try(:end_of_day)
    else
      @end_date = Date.today.end_of_day
    end
    @tag_list = Order.unscoped.where(:state => ['pending', 'followup']).where('created_at BETWEEN ? AND  ?', @start_date, @end_date).all_tags.map(&:name)
    @pending_orders = Order.unscoped.select('number,orders.created_at,billing_city,billing_country,total,name,email,notes,state').where(:state => ['pending', 'followup']).where('orders.created_at BETWEEN ? AND  ?', @start_date, @end_date).preload(:events).order("total DESC")
    @pending_orders = if params[:tag]
      @pending_orders.tagged_with(params[:tag])
    else
      @pending_orders
    end.paginate(page: params[:page], per_page: 30)
    order_compare_date = (@start_date - 45.days).beginning_of_day
    @order_email_count = Order.unscoped.where(email: @pending_orders.collect(&:email).uniq,state: 'sane').where{orders.created_at >= order_compare_date}.group(:email).count
  end

  def shipment_bill_no
    if params[:download]   
      file = Shipment.sidekiq_delay(queue: 'low').shipment_bill_csv
      notice = "Report has been successfully mailed"
      redirect_to shipment_bill_no_path, notice: notice and return
    elsif params[:csv_file]
      file = CSV.read(params[:csv_file].path, headers: true).to_a
      Shipment.sidekiq_delay(queue: 'low')
              .update_shipment_bill_csv(file, current_account.email)
      notice = "Shipment bill no. will be updated"
      redirect_to shipment_bill_no_path, notice: notice and return
    elsif params['date']
      date = create_date(params['date'])
      file = Shipment.sidekiq_delay(queue: 'low').shipment_bill_csv(date)
      notice = "Report has been successfully mailed"
      redirect_to shipment_bill_no_path, notice: notice and return
    end
  end

  def rack_check_page
    if params[:rack_num].present?
      rack_id = RackList.where(code: params[:rack_num].upcase).pluck(:id)
      if rack_id.present?
        rack_codes = []
        numbers = []
        total_products = params[:total_items]
        (1..total_products.to_i).each do |num| 
          input_id = "input_#{num}"
          barcode = params[input_id].presence
          barcode_split = barcode.to_s.strip.split('-')
          numbers << barcode_split[1]
          rack_codes << "#{barcode_split[2]}-#{barcode_split[3]}"
        end
        if params[:commit] == 'Mail Report'
          SidekiqDelayGenericJob.perform_async("DesignerOrder", nil, "mail_rack_reports", numbers, rack_codes, rack_id, params[:rack_num], {"#{current_account.class}": current_account.id})
          #DesignerOrder.sidekiq_delay(queue: 'low')
          #             .mail_rack_reports(numbers,rack_codes,
          #                                rack_id,
          #                                params[:rack_num],
          #                                current_account)
          flash[:success] = "Rack Report Mailed to #{current_account.email} and Rack Check Done Successfully."
        end
      else
        flash[:notice] = "No such rack"
      end
      redirect_to admin_rack_check_page_path
    end
  end

  def campaign_stats
    @start_date = params[:start_date].present? ? Date.parse(params[:start_date]) : Date.yesterday
    @end_date = params[:end_date].present? ? Date.parse(params[:end_date]) : Date.yesterday
    # Stats Query
    o_state = "state IN (?)", ['sane', 'confirmed', 'ready_for_dispatch', 'pickedup', 'dispatched', 'completed']
    all_orders = params[:campaign_name].present? ? Order.unscoped.select('total,number,billing_country,multi_channel_marketing').where(confirmed_at: @start_date.beginning_of_day..@end_date.end_of_day).where(o_state).joins(:tags).where('tags.name = ?', "convert-mkt-#{params[:campaign_name]}") :
                Order.unscoped.select('total,number,billing_country,multi_channel_marketing').where(confirmed_at: @start_date.beginning_of_day..@end_date.end_of_day).where(o_state).where("multi_channel_marketing <> ''")
    begin
      @campaign_details,@medium_names = get_campaign_stats(all_orders)
      if params[:campaign_name].present? && (@campaign_details["#{params[:campaign_name]}"][:total_revenue] == 0) && (@campaign_details[params[:campaign_name]][:total_orders_count] == 0)
        @no_data_notice = "No Such Campaign in prescribed time period"
      end
    rescue
      @no_data_notice = "Faulty Campaign data not able to process!!!"
    end
  end

  def stats
    redirect_to root_path, notice: 'You are not authorized to access this page.' unless ACCESSIBLE_EMAIL_ID['stats_access'].to_a.include?(current_account.try(:email))
    stats = Stat::OrderStatService.new(params[:start_date],params[:end_date],params["geo"])
    @total_order_count = stats.total_orders.size
    @total_revenue = stats.total_revenue
    @confirmed_orders = stats.confirmed_orders
    @total_int_order_count = stats.total_international_order_count
    @total_international_revenue = stats.total_international_revenue
    @total_domestic_order_count = stats.total_domestic_order_count
    @total_domestic_revenue = stats.total_domestic_revenue
    @cod_pending_count = stats.cod_pending_size
    @cod_pending_revenue = stats.cod_pending_total
    @cod_sane_count = stats.cod_sane_size
    @cod_sane_revenue = stats.cod_sane_total
    @stitch_amount = stats.stitching_amount.round(2)
    @shipping_amount = stats.shipping_amount.round(2)
    page_number = params[:page] || 1
    @orders = stats.all_sane_orders.paginate(page: page_number,per_page: 50)
  end

  def stats_summary1
    @start_date, @end_date = get_date
    s_distinct = 'DISTINCT designer_orders.order_id as order_id'
    dos_state = ['pending', 'dispatched', 'completed', 'pickedup']
    w_date = 'orders.confirmed_at BETWEEN ? AND  ?', @start_date.beginning_of_day, @end_date.end_of_day
    dos = DesignerOrder.unscoped.select(s_distinct).joins(:order).where(:state => dos_state).where(w_date)

    s_columns = 'COUNT(orders.id) as total_orders, SUM(orders.total) as total_revenue, SUM(orders.shipping) as total_shipping, SUM((orders.total/orders.currency_rate)*orders.currency_rate_market_value) as actual_revenue_inr' 
    w_order_id = "id IN (?)", dos.collect{|d| d[:order_id]}
    orders = Order.unscoped.select(s_columns).where(w_order_id).first

    s_date = "to_char(confirmed_at + interval '330 minutes', 'YYYY-MM-DD, DY') as confirmed_at_date"
    s_columns_date = s_columns + ', ' + s_date
    @orders_date = Order.unscoped.select(s_columns_date).where(w_order_id).group('confirmed_at_date').order('confirmed_at_date')

    s_paytype_columns = 'COUNT(orders.id) as total_orders, SUM(orders.total) as total_revenue, orders.pay_type'
    paytype_orders = Order.unscoped.select(s_paytype_columns).where(w_order_id).group(:pay_type)

    s_paytype_columns_date = s_paytype_columns + ', ' + s_date
    @paytype_orders_date = Order.unscoped.select(s_paytype_columns_date).where(w_order_id).group('confirmed_at_date').
                            group(:pay_type).group_by{|gpd| gpd[:confirmed_at_date]}

    number_of_days     = (@end_date - @start_date + 1).to_i
    @total_revenue     = orders[:total_revenue].to_i
    @actual_revenue_inr = orders[:actual_revenue_inr].to_i
    @total_order_count = orders[:total_orders].to_i
    @shipping_revenue  = orders[:total_shipping].to_i
    @avg_num_of_orders = @total_order_count/number_of_days
    @avg_order_value   = @total_revenue/@total_order_count if @total_order_count > 0
    @revenue_per_day   = @total_revenue/number_of_days    
    paytype_orders.each do |po|
      if po.pay_type == "Credit/Debit Card/Net Banking"
        @ccavenue = po
      elsif po.pay_type == "PayPal"
        @paypal = po
      elsif po.pay_type == "Cash Before Delivery"
        @cbd = po
      elsif po.pay_type == COD
        @cod = po
      end
    end
  end

  def category_stats
    @start_date = params[:start_date].present? ? (params[:start_date].is_a?(Hash) ? create_date(params[:start_date]) : Date.parse(params[:start_date])) : Date.today - 15.days
    @end_date = params[:end_date].present? ? (params[:end_date].is_a?(Hash) ? create_date(params[:end_date]) : Date.parse(params[:end_date])) : Date.today
    if @start_date > @end_date
      @start_date,@end_date = @end_date,@start_date
    end
    @parent_categories = {}
    PARENT_CATEGORIES.each do |category|
      @parent_categories[category.titleize] = Category.names_to_ids(category).values
    end
    data = Admin.get_category_data(@start_date,@end_date,@parent_categories)
    if data[:stats].present? && data[:parent_stats].present?
      @stats = data[:stats]
      @parent_stats = data[:parent_stats]
      @sku_count = Admin.set_categorywise_sku_count
      @categories = Category.get_name_to_ids
    end
    if params[:download_report] == 'true'
      OrderMailer.sidekiq_delay(run_at: 1.minute.from_now).admin_category_stats_mailer(@start_date,@end_date,current_account.email)
      render json: {success: 'success'},status: 200
    end
  end

  def category_subreports
    id = params[:category_id].to_i
    start_date = params[:start_date].present? ? Date.parse(params[:start_date]) : (Date.today - 15.days)
    end_date = params[:end_date].present? ? Date.parse(params[:end_date]) : Date.today
    if start_date > end_date
      start_date,end_date = end_date,start_date
    end
    data_sets = TimeSeriesDataset.where('(date between ? and ?) and metric_id = ?',start_date,end_date,id).pluck(:data)
    unless data_sets.empty?
      stats = Hash.new(0)
      mobile = 'mobile'
      desktop='desktop'
      ga_hash = Hash.new(0)
      category = Category.find(id).name
      hash1 = {mobile: {},desktop: {}}.stringify_keys
      @stats = {'category' => category.titleize}
      if params[:parent_category] == 'true'
        data = Rails.cache.fetch("category_parent_subreport_data_#{start_date}_#{end_date}_#{id}") do
          category_ids = Category.names_to_ids(category).except!(category.downcase.gsub('-','_').to_sym)
          child_stats = {}
          category_ids.each do |name,category_id|
            child_stats[category_id] = {'category' => name.to_s.titleize}.merge(Admin.get_data(category_id,start_date,end_date))
          end
          stats['child_categories'] = category_ids
          data_sets.each{|data_set| stats.merge!(data_set['parent']){|k,o,n|  o.to_f + n.to_f }}
          stats['child_categories'] = category_ids
          stats['sku_count'] = Admin.set_categorywise_sku_count.values_at(*category_ids.values).sum(&:to_i)
          stats['inventory'] = Admin.get_total_inventory(category_ids.values)
          GoogleAnalyticsData.where('(date between ? and ?)',start_date,end_date).where(metric_id: category_ids.values,metric_type: "Category").pluck(:data_hash).each do |ga|
            hash1[mobile].merge!(ga[mobile]){|k,o,n| o+n}
            hash1[desktop].merge!(ga[desktop]){|k,o,n| o+n}
          end
          ga_hash['mobile_sessions']=((hash1[mobile]||{})['sessions']).to_i
          ga_hash['desktop_sessions']+=((hash1[desktop]||{})['sessions']).to_i
          stats['GA_page_views']+=((hash1[mobile]||{})['pageviews']).to_i+((hash1[desktop]||{})['pageviews']).to_i
          stats['GA_users']+=((hash1[mobile]||{})['users']).to_i+((hash1[desktop]||{})['users']).to_i
          stats['GA_sessions']+=ga_hash['mobile_sessions'] + ga_hash['desktop_sessions']
          stats['mobile_conversion_rate'] = ga_hash['mobile_sessions'] > 0 ? "#{((stats['mobile_&_apps_international_orders'] + stats['mobile_&_apps_domestic_orders'])/ga_hash['mobile_sessions'].to_f).round(5)}%" : '0%'
          stats['desktop_conversion_rate'] = ga_hash['desktop_sessions'] > 0 ? "#{((stats['desktop_international_orders'] + stats['desktop_domestic_orders'])/ga_hash['desktop_sessions'].to_f).round(5)}%" : '0%'
          total_intl_orders = stats['mobile_&_apps_international_orders'] + stats['desktop_international_orders']
          total_dom_orders = stats['mobile_&_apps_domestic_orders'] + stats['desktop_domestic_orders']
          stats['avg_international_order_value'] = total_intl_orders > 0 ? ((stats['mobile_&_apps_international_revenue'] + stats['desktop_international_revenue'])/(total_intl_orders)).round(2) : 0
          stats['avg_domestic_order_value'] = total_dom_orders > 0 ? ((stats['mobile_&_apps_domestic_revenue'] + stats['desktop_domestic_revenue'])/(total_dom_orders)).round(2) : 0
          stats['avg_order_value'] = stats['total_orders'] > 0 ? (stats['total_revenue']/stats['total_orders']).round(2) : 0
          stats['orders_per_session'] = stats['GA_sessions'] > 0 ? (stats['total_orders']/stats['GA_sessions'].to_f).round(5) : 0
          stats['orders_per_inventory'] =  stats['inventory'] > 0 ? (stats['total_orders']/stats['inventory'].to_f).round(5) : 0
          stats['top_selling_designs'] = Rails.application.routes.url_for(controller: 'store',action: 'catalog2',kind: category.downcase,host: DESKTOP_SITE_URL,sell_at: (end_date - start_date).to_i)
          {stats: stats,child_stats: child_stats}
        end
        @stats.merge!(data[:stats])
        @child_stats = data[:child_stats]
      else
        @stats.merge!(Admin.get_data(id,start_date,end_date))
      end
    end
    # render json: {stats: stats}
  end

  def vendorwise_inventory
    category_ids = params[:parent] == 'true' ? Category.find(params[:category_id]).cached_self_and_descendants_id : params[:category_id]
    data_hash = Admin.get_vendor_inventory(category_ids)
    data_hash = data_hash.sort_by{|k,v| v}.to_h
    render json: {data: data_hash}
  end

  def stats_calculations
    @start_date, @end_date = get_date
    s_distinct = 'DISTINCT designer_orders.order_id as order_id, payout'
    dos_state = ['pending', 'dispatched', 'completed', 'pickedup']
    w_date = 'orders.confirmed_at BETWEEN ? AND  ?', @start_date.beginning_of_day, @end_date.end_of_day
    dos = DesignerOrder.unscoped.select(s_distinct).joins(:order).where(:state => dos_state).where(w_date)

    s_columns = 'COUNT(orders.id) as total_orders, SUM(orders.total) as total_revenue, SUM((orders.shipping/orders.currency_rate)*orders.currency_rate_market_value) as total_shipping, SUM((orders.total/orders.currency_rate)*orders.currency_rate_market_value) as actual_revenue_inr, SUM(cod_charge) as total_cod_charges, SUM((orders.discount/orders.currency_rate)*orders.currency_rate_market_value) as total_discount' 
    w_order_id = "id IN (?)", dos.collect{|d| d[:order_id]}
    orders = Order.unscoped.select(s_columns).where(w_order_id).first

    s_date = "to_char(confirmed_at + interval '330 minutes', 'YYYY-MM-DD, DY') as confirmed_at_date"
    s_columns_date = s_columns + ', ' + s_date
    @orders_date = Order.unscoped.select(s_columns_date).where(w_order_id).group('confirmed_at_date').order('confirmed_at_date')

    s_paytype_columns = 'COUNT(orders.id) as total_orders, SUM(orders.total) as total_revenue, orders.pay_type'
    paytype_orders = Order.unscoped.select(s_paytype_columns).where(w_order_id).group(:pay_type)

    s_paytype_columns_date = s_paytype_columns + ', ' + s_date
    @paytype_orders_date = Order.unscoped.select(s_paytype_columns_date).where(w_order_id).group('confirmed_at_date').
                            group(:pay_type).group_by{|gpd| gpd[:confirmed_at_date]}

    number_of_days     = (@end_date - @start_date + 1).to_i
    @total_revenue     = orders[:total_revenue].to_i
    @actual_revenue_inr = orders[:actual_revenue_inr].to_i    
    @actual_payout = dos.inject(0){|sum,curr| sum + curr[:payout]}    
    @total_order_count = orders[:total_orders].to_i
    @shipping_revenue  = orders[:total_shipping].to_i
    @cod_charges  = orders[:total_cod_charges].to_i
    @discount = orders[:total_discount].to_i
    @gmv = @actual_revenue_inr + @discount
    @towards_order = @actual_revenue_inr - @shipping_revenue - @cod_charges
    @total_commission_earnings = @towards_order - @actual_payout
    @actual_earnings = @actual_revenue_inr - @actual_payout

    @avg_num_of_orders = @total_order_count/number_of_days
    @avg_order_value   = @total_revenue/@total_order_count if @total_order_count > 0
    @revenue_per_day   = @total_revenue/number_of_days
    paytype_orders.each do |po|
      if po.pay_type == "Credit/Debit Card/Net Banking"
        @ccavenue = po
      elsif po.pay_type == "PayPal"
        @paypal = po
      elsif po.pay_type == "Cash Before Delivery"
        @cbd = po
      elsif po.pay_type == COD
        @cod = po
      end
    end
  end



  def cod_summary
    @start_date, @end_date = get_date

    #@start_date = Date.parse("1/10/2014")
    #@end_date = Date.parse("15/10/2014")

    w_clause = 'shipments.shipment_type = ? AND shipments.created_at BETWEEN ? AND  ?', 'COD',@start_date.beginning_of_day, @end_date.end_of_day

    s_columns = 'COUNT(shipments.id) as total_orders, SUM(shipments.price) as total_revenue, shipment_state'

    @shipments_by_state = Shipment.forward.select(s_columns).where(w_clause).group(:shipment_state)

    s_date = "to_char(created_at + interval '330 minutes', 'YYYY-MM-DD, DY') as created_at_date"

    s_columns_date = s_columns + ',' + s_date
    @paytype_shipments_date = Shipment.unscoped.forward.select(s_columns_date).where(w_clause).group('created_at_date').
                            group(:shipment_state).group_by{|gpd| gpd[:created_at_date]}
  end



  def stats_summary
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
      @start_datetime = @start_date.beginning_of_day
    else
      @start_date = Date.today.beginning_of_month
      @start_datetime = @start_date.beginning_of_day
    end


    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.beginning_of_day
    elsif @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
      @end_date = Date.today
      @end_datetime = @end_date.end_of_day
    else
      @end_date = @start_date.end_of_month
      @end_datetime = @end_date.end_of_day
    end

    @orders = Order.includes(:designer_orders).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).references(:designer_orders).to_a
    @confirmed_orders = Order.where('state = ?', "confirmed").where('created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime)

    if @orders
      @total_revenue_cc = @orders.sum(&:total)

      @gharpay_orders = Order.where(:state => 'pending').where(:pay_type => GHARPAY).where('created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).to_a
      @total_revenue_gharpay = @gharpay_orders.sum(&:total)

      @sane_orders_email = @orders.collect {|o| o.email}
      @sane_orders_email_1 = @gharpay_orders.collect {|o| o.email }

      @sane_orders_phone = @orders.collect {|o| o.phone}
      @sane_orders_phone_1 = @gharpay_orders.collect {|o| o.phone }

      @sane_orders_email += @sane_orders_email_1
      @sane_orders_phone += @sane_orders_phone_1

      @confirmed_orders_email = @confirmed_orders.collect {|o| o.email}
      @confirmed_orders_phone = @confirmed_orders.collect {|o| o.phone}
    end


    @total_revenue = @orders.sum(&:total)
    @total_order_count = @orders.count

    @shipping_revenue = @orders.sum(&:shipping)

    @number_of_days = @end_date - @start_date
    @number_of_days = 1 if @number_of_days == 0

    @avg_num_of_orders = @total_order_count / @number_of_days.to_i
    @avg_order_value = @total_revenue / @total_order_count if @total_order_count > 0
    @revenue_per_day = @total_revenue / @number_of_days.to_i

    # Designer Order Stats

    # Get completed designer orders

    # Get pending designer orders

    # Get dispatched designer orders

    @total_dos = DesignerOrder.where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).to_a
    @dos_completed = DesignerOrder.where('designer_orders.state = ?', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).to_a
    @dos_pending = DesignerOrder.where('designer_orders.state = ?', 'pending').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime)
    @dos_dispatched = DesignerOrder.where('designer_orders.state = ?', 'dispatched').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime)

    @dos_paid = DesignerOrder.where('designer_orders.designer_payout_status = ?', 'paid').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime)

    @dos_unpaid = DesignerOrder.where('designer_orders.designer_payout_status <> ?', 'paid').where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime)

    @dos_returned = DesignerOrder.where('designer_orders.state = ?', 'buyer_returned').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).to_a

    @dos_international = DesignerOrder.includes(:order).where('lower(orders.billing_country) <> ?', 'india').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).references(:orders).to_a

    @aconvert = Order.tagged_with('aconvert').includes(:designer_orders).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).references(:designer_orders).to_a
    @yconvert = Order.tagged_with('yconvert').includes(:designer_orders).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).references(:designer_orders).to_a

    @summary = Array.new

    0.upto(@number_of_days) do |i|
      date = @start_date.advance(:days => i)
      @do_orders = DesignerOrder.where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', date.beginning_of_day, date.end_of_day)
      @c_orders = Order.where('state = ?', 'cancel').where('created_at BETWEEN ? AND  ?', date.beginning_of_day, date.end_of_day)
      @aconvert1 = Order.tagged_with('aconvert').includes(:designer_orders).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', date.beginning_of_day, date.end_of_day).references(:designer_orders).to_a
      @yconvert1 = Order.tagged_with('yconvert').includes(:designer_orders).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', date.beginning_of_day, date.end_of_day).references(:designer_orders).to_a
      @pending_orders = Order.where({:state => 'pending'}).where('pay_type <> ?', GHARPAY).where('created_at BETWEEN ? AND  ?', date.beginning_of_day, date.end_of_day).to_a

      if @c_orders && params[:dedup].blank?
        @total_revenue_lost = 0
        @already_included_canceled = Array.new
        @already_included_canceled_phone = Array.new
        @canceled_orders = Array.new
        @international_revenue_lost = 0
        @domestic_revenue_lost = 0
        @total_revenue_lost = 0
        @c_orders.each do |o|
          unless @sane_orders_email.include?(o.email) || @already_included_canceled.include?(o.email) || @sane_orders_phone.include?(o.phone) || @already_included_canceled_phone.include?(o.phone) || @confirmed_orders_email.include?(o.email) || @confirmed_orders_phone.include?(o.phone)
            @canceled_orders << [o.line_items.to_a.sum(&:sub_total), o]
            @already_included_canceled << o.email
            @already_included_canceled_phone << o.phone
            @total_revenue_lost += o.line_items.to_a.sum(&:sub_total)
            @international_revenue_lost += o.line_items.to_a.sum(&:sub_total) if o.international?
            @domestic_revenue_lost += o.line_items.to_a.sum(&:sub_total) unless o.international?
          end
        end
      end

      @summary << [date, @do_orders.to_a.count, @do_orders.to_a.sum(&:total), @canceled_orders.try(:count), @total_revenue_lost, @pending_orders.count, @pending_orders.sum(&:total), @international_revenue_lost, @aconvert1.sum(&:total), @domestic_revenue_lost, @yconvert1.sum(&:total)]
    end
  end


  def summary
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
      @start_datetime = @start_date.beginning_of_day
    else
      @start_date = Date.today.beginning_of_month
      @start_datetime = @start_date.beginning_of_day
    end


    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.beginning_of_day
    elsif @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
      @end_date = Date.today
      @end_datetime = @end_date.end_of_day
    else
      @end_date = @start_date.end_of_month
      @end_datetime = @end_date.end_of_day
    end

    @total_dos = DesignerOrder.where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed', 'pickedup').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).where('designer_orders.designer_payout_status <> ?', 'paid')
    @total_dos.to_a.sum(&:payout)

  end



  def rtv_pending 
    rtv_shipment_join_string = 'left outer join rtv_shipment_line_items rtvli on rtvli.line_item_id = line_items.id'

    line_items_to_be_rtv = LineItem.includes(designer_order: [:order,:rack_list] ,design: :designer).joins(:designer_order).joins(rtv_shipment_join_string).where('(designer_orders.state IN (?) OR qc_status = ?) AND (received = ? OR designer_orders.package_received_on IS NOT NULL) AND designer_orders.confirmed_at > ?', ['vendor_canceled','canceled','buyer_returned'], false,'Y',3.months.ago).order('designer_orders.confirmed_at ASC').group('line_items.id, designer_orders.id').having('max(rtvli.rtv_shipment_id) is null')
    
    @paginated_line_item = line_items_to_be_rtv.paginate(page: params[:page], per_page: 20)
  end


  def products_summary
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
      @start_datetime = @start_date.beginning_of_day
    else
      @start_date = Date.today.beginning_of_month
      @start_datetime = @start_date.beginning_of_day
    end

    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.end_of_day
    else
      if @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
        @end_date = Date.today
        @end_datetime = @end_date.end_of_day
      else
        @end_date = @start_date.end_of_month
        @end_datetime = @end_date.end_of_day
      end
    end

    @designers = Designer.where('name is not null').map{|i|[i.name.camelize,i.id]}
    @designers = @designers.sort_by {|name, id| name}.prepend(['All','All'])

    if params[:designer_id].present? && params[:designer_id] != 'All'
      @products_summary = LineItem.joins(:designer_order).where('designer_orders.designer_id = ?', params[:designer_id]).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('line_items.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).group('design_id').select('design_id, count(*) as item_count').having('count(*) > 1')
    else
      @products_summary = LineItem.joins(:designer_order).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed').where('line_items.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).includes(:design).group('design_id').select('design_id, count(*) as item_count').having('count(*) > 2')
    end
    @products_summary = @products_summary.collect {|design| [design, design.item_count.to_i]}
    @products_sumamry = @products_summary.sort_by! {|design, count| -count}

  end

  def order_sanity
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
      @start_datetime = @start_date.beginning_of_day
    else
      @start_date = Date.today
      @start_datetime = @start_date.beginning_of_day
    end

    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.end_of_day
    else
      if @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
        @end_date = Date.today
        @end_datetime = @end_date.end_of_day
      else
        @end_date = @start_date.end_of_month
        @end_datetime = @end_date.end_of_day
      end
    end
    @orders = Order.where('state = ?', 'sane').where('created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).preload(:events)
  end

  def inventory_by_order
    w_inter = 'lower(orders.country) <> ?', 'india'
    w_do_state = "designer_orders.state IN (?)", ['pending', 'dispatched', 'completed']
    w_order_state = 'orders.state = ?', 'sane'
    if params[:by_order]
      @orders = Order.eager_load(:events, :designer_orders => [[:line_items => [:design => :images]], :designer]).where(w_order_state).where(w_do_state).where(w_inter).paginate(:page => params[:page])
    else
      if (@designer_id = params[:designer_id]).present?
        @dos = DesignerOrder.select('designer_orders.id').joins(:order).where(w_order_state).where(w_do_state).where(w_inter).where(:designer_id => @designer_id)
        @dos = DesignerOrder.includes([:events, :order, [:line_items => [:design => :images]]]).where(:id => @dos.collect(&:id))
      end
      s_designer     = 'DISTINCT(designer_id) as designer_id'
      w_designer     = 'designer_orders.designer_id IS NOT NULL'
      @designers_all = DesignerOrder.unscoped.select(s_designer).includes(:designer).joins(:order).
                        where(w_order_state).where(w_inter).where(w_designer)
      @designers_map = @designers_all.collect { |des_od| [des_od.designer.name, des_od.designer_id]}.sort_by{|d| d[0]}
    end
  end

  def create_coupon
  end

  def pending_orders
    state                   = params[:state].present? ? params[:state] : 'pending'
    per_page_fetch          = params[:items_per_page].present? ? (params[:items_per_page].to_i < 200 ? params[:items_per_page] : 200) : 20
    dos,@vendor_stats,@designer_order_stat = Admin.get_pending_orders(state,params[:region],params[:type],false,params[:designer_id],params[:sla],params[:all])
    @designer_orders = dos.paginate(:page => params[:page], :per_page => per_page_fetch)
    @order_hash = @designer_orders.group_by {|d| d.designer_id}
  end

  def pending_orders_report
    SidekiqDelayGenericJob.perform_async("Admin", nil, "pending_orders_download_csv", params[:state], params[:region], params[:type], params[:download_csv], params[:designer_id], params[:sla], params[:all], {"#{current_account.class}": current_account.id})
    #Admin.sidekiq_delay(queue: 'low')
    #     .pending_orders_download_csv(
    #       params[:state],
    #       params[:region],
    #       params[:type],
    #       params[:download_csv],
    #       params[:designer_id],
    #       params[:sla],
    #       params[:all],
    #       current_account
    #     )
    render :nothing => true
  end

  def designer_reports
    @start_date, @end_date = get_date
    @designers = designer_list_w_all
    param_designer_id = params[:designer_id]
    #Report Part
    designers_info, issue_counts, design_count = get_designers_info(@start_date, @end_date, param_designer_id, params[:designer_state])
    designer_orders_stats = get_designer_report_stats(@start_date, @end_date,param_designer_id)
    report = create_designer_report(issue_counts, @start_date, @end_date, designer_orders_stats,designers_info,design_count)

    #Avoiding Divide By Zero Error
    date_diff = (((@end_date - @start_date).to_f)/30).ceil
    date_diff = 1 if date_diff == 0

    @designers_low_products_count = calculating_avgs_designer_report(report[0],date_diff,@start_date,@end_date)
    @designers_high_products_count = calculating_avgs_designer_report(report[1],date_diff,@start_date,@end_date)
    if param_designer_id.present? && param_designer_id != 'All'
      designer = designers_info.last
      @gst_detail = {gst_no: designer.try(:gst_no),gst_address: designer.try(:gst_address),taxpayer_trade_name: designer.try(:taxpayer_trade_name)}
      @designer_id = (param_designer_id).to_i 
    end
  end

  def create_issue_line_item
    if (line_item = LineItem.find_by_id(params[:line_item_id])) && (order = Order.find_by_id(params[:order_id]))     
      message = line_item.create_issue_for_item(order, current_account, params[:resolve], params[:resolve_message], params[:message], params[:rtv_quantity], params[:needs_replacement])
      order.check_items
    else
      message = 'Issue could not be created!'
    end
    redirect_to request.referer.present? ? :back :orders_url, notice: message
  end

  def get_designer_report_stats(start_date,end_date,designer)
    columns = 'designer_orders.designer_id,designer_orders.state as state,orders.notes as order_notes, (orders.confirmed_at) as confirmed_at1,orders.number as order_number,(designer_orders.created_at) as created_at1, (designer_orders.pickup) as pickup1'
    w_date_clause = 'designer_orders.created_at between ? and ?',start_date,end_date
    if designer == 'All'
      DesignerOrder.joins(:order).select(columns).where(w_date_clause).order(:designer_id)
    else
      DesignerOrder.joins(:order).select(columns).where(w_date_clause).where(designer_id: designer).order(:designer_id)
    end
  end

  def addons_for_order
    if params[:order_number].present?
      @order = Order.preload(line_items: [:line_item_addons,:design,:designer_order]).find_by_number(params[:order_number])
    end
  end

  def create_designer_report(issue_hash, start_date,end_date,designer_orders_stats,designers_info,design_count)
    #Creating hash for recording report stats
    designers_high_products_count = Array.new
    designers_low_products_count = Array.new
    #Pushing designer name,published products,designerid into the above two hashed based on designer count
    designers_info.each do |d|
      des_oos_count = issue_hash[d.id.to_s].present? ? issue_hash[d.id.to_s]['OOS'].to_i : 0
      des_qc_failed_count = issue_hash[d.id.to_s].present? ? issue_hash[d.id.to_s]['QC Failed'].to_i : 0
      des_critical_count = issue_hash[d.id.to_s].present? ? issue_hash[d.id.to_s]['critical order'].to_i : 0
      des_issues_count = issue_hash[d.id.to_s].present? ? issue_hash[d.id.to_s].values.sum : 0

      d_count = design_count[d.id.to_s].present? ? design_count[d.id.to_s][0][:design_count] : 0
      hash_value =  {id: d.id, phone: d.phone, pending_orders: 0, state_machine: d.state_machine,
        designer_sign_up_date: d.designer_sign_up_date, designer_last_sign_at_date: d.designer_last_sign_in,
        sla: 0, sla_data: Array.new, total_shipping_time_for_all_orders: 0, avg_dispatch_time: 0,
        avg_dispatch_time_data: Array.new, buyer_returned_data: Array.new, buyer_returned_count: 0,
        published_designs: d_count, designer_name: d.name, total_orders: 0,
        total_orders_pickup_date: 0, avg_orders: 0,total_issues: des_issues_count,critical_orders: des_critical_count, oos_count: des_oos_count, qc_failed_count: des_qc_failed_count}

      if (d_count).to_i<10
        designers_low_products_count[d.id] = hash_value
      else
        designers_high_products_count[d.id] = hash_value
      end
    end
    #Creating Report
    designer_orders_stats.each do |d|
      if d.designer_id.present?
        id = d.designer_id
        if designers_low_products_count[id].present?
          designers_low_products_count[id] = designer_report_calculation(designers_low_products_count[id],d)
        elsif designers_high_products_count[id].present?
          designers_high_products_count[id] = designer_report_calculation(designers_high_products_count[id],d)
        end
      end
    end
    return designers_low_products_count, designers_high_products_count
  end

  def mark_additional_payment_complete
    pay_type_attributes = {BANK_DEPOSIT =>'bank_deposit_txn_id',PAYPAL => 'paypal_txn_id', PAYU_MONEY => 'payu_mihpayid',PAY_WITH_AMAZON =>'amazon_order_id',PAYTM => 'paytm_txn_id','Razorpay' => 'razorpay_id'}    
    if (additional_payment = AdditionalPayment.find_by_id(params[:additional_payment_id])) && (order = Order.find_by_id(params[:order_id]))
      additional_payment.payment_gateway =pay_type_attributes[params[:pay_type]]
      additional_payment.send("#{params[:pay_type]}=",params[:transaction_id])
      additional_payment.save
      if additional_payment.charge_type == 'custom_addon'
        additional_payment.addon_payment_complete!
      else
        order.mark_addon_payment_complete(additional_payment.id)
      end
    end
    render json: {head: :ok}
  end

  def resend_addon_payment_link
    if (additional_payment_id = session["addons_link_#{params[:additional_payment_id].to_i}"]).present?
      session["addons_link_#{additional_payment_id}"] = true
      OrderMailer.send_addons_invoice(params[:order_id].to_i,additional_payment_id)
      render json: {status: 'Mail Sent Successfully'}
    else
      render json: {status: 'Mail Already Sent To Customer'}
    end
  end

  def add_addon_from_admin
    order = Order.find_by_id(params[:order_id])
    additional_payment = AdditionalPayment.create(order_id: order.id,payment_state: 'new')
    design_ids = []
    total = 0
    coupon_applied = order.coupon.try(:coupon_type) == 'STITOFF' ? true : false
    LineItem.where(id: params[:line_items_id].to_s.split(' ')).each do |line_item|
      addon_type_ids = params["addon_ids_#{line_item.id}"].to_s.split(' ').map(&:to_i)
      line_item.line_item_addons.each{|lia| lia.delete if addon_type_ids.include?(lia.addon_type_value.try(:addon_type).try(:id))}
      (0...params["count_of_ats_#{line_item.id}"].to_i).each do |at_id|
        atv_id = params["at-#{line_item.id}-#{at_id}"]
        addon_type_value =  AddonTypeValue.where(id: atv_id).first
        if addon_type_value.present? && addon_type_value.addon_type_value_group.name.try(:downcase) != 'open'
          effective_price = (params["custom_addon_price_#{atv_id}"].presence || addon_type_value.effective_price(line_item.design, order.country_code)).to_f
          snapshot_price = coupon_applied ? 0 : effective_price
          total += effective_price * line_item.quantity
          line_item.line_item_addons << LineItemAddon.new(addon_type_value_id: addon_type_value.id, snapshot_price: snapshot_price, status: 'unpaid',additional_payment_id: additional_payment.id)
          line_item.stitching_required = 'Y'
          design_ids << line_item.design_id
        end
      end
      line_item.save
    end
    @addon_post = true
    design_ids.uniq!
    if order.save && design_ids.present?
      additional_payment.total = total
      additional_payment.currency_rate_market_value = CurrencyConvert.countries_marketrate[order.country_code]
      additional_payment.save
      if total > 0 && order.state == 'sane'
        order.add_notes_without_callback("Payment Link Sent. Addons added to #{design_ids.join(',')}", 'payment', current_account)
        OrderMailer.sidekiq_delay
                   .send_addons_invoice(order.id,additional_payment.id)
      elsif order.state != 'sane'
        # order.sidekiq_delay.mark_addon_payment_complete(additional_payment.id)
        SidekiqDelayGenericJob.perform_async(order.class.to_s,order.id,"mark_addon_payment_complete",additional_payment.id)
        additional_payment.update_column(:payment_state,'added_to_order')
      else
        if coupon_applied
          order.add_notes_without_callback("Coupon Used. Addons added to #{design_ids.join(',')}", 'payment', current_account)
          additional_payment.update_column(:payment_state,'coupon_used')
        end
        # order.sidekiq_delay.mark_addon_payment_complete(additional_payment.id)
        SidekiqDelayGenericJob.perform_async(order.class.to_s,order.id,"mark_addon_payment_complete",additional_payment.id)
      end
      # order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      # order.sidekiq_delay(priority: -10).update_expected_dates
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "update_expected_dates") 
      
      redirect_to :back,notice: 'Addon added successfully'
    else
      additional_payment.delete
      redirect_to :back,notice: 'Error Occured'
    end
  end


  def designer_report_calculation(designer_stats,designer_order)
    #Report Calculations
    designer_stats[:total_orders] += 1 if designer_order.state == 'pending' or designer_order.state == 'completed' or designer_order.state == 'dispatched'
    confirmed_at_date = Date.parse(designer_order[:confirmed_at1].to_s) if designer_order[:confirmed_at1].present?

    #Calculating Pending Orders & Total Service Level Agreement(SLA) Violations
    if designer_order.state == "pending"
      designer_stats[:pending_orders] += 1
      checkdate = Date.today
    elsif designer_order.state == "completed" or designer_order.state == "dispatched"
      checkdate = Date.parse(designer_order[:pickup1].to_s) if designer_order[:pickup1].present?
    end 

    if checkdate.present? && confirmed_at_date.present? && (date_diff = (checkdate - confirmed_at_date).to_i) >= MIRRAW_DESIGNER_SLA
      designer_stats[:sla] += 1
      order_data = {order_state: designer_order.state, order_pickup_date: checkdate, order_confirmed_at: confirmed_at_date, diff: date_diff, order_no: designer_order.order_number}
      designer_stats[:sla_data] << order_data
    end
    #Totaling Up Dispatch Transaction Time For All Orders
    #Checking only dispatched and completed because only these are valid orders
    if designer_order.state == 'completed' or designer_order.state == 'dispatched'
      if designer_order[:pickup1].present? and designer_order[:confirmed_at1].present?
        date_diff = (checkdate - confirmed_at_date).to_i
        designer_stats[:total_shipping_time_for_all_orders] += date_diff
        designer_stats[:total_orders_pickup_date] += 1
        order_data = {order_state: designer_order.state, order_pickup_date: checkdate, order_confirmed_at: confirmed_at_date, diff: date_diff, order_no: designer_order.order_number}
        designer_stats[:avg_dispatch_time_data] << order_data
      end
    end
    if designer_order.state == 'buyer_returned'
      order_data = {order_no: designer_order.order_number, order_notes: designer_order.order_notes}
      designer_stats[:buyer_returned_count] += 1
      designer_stats[:buyer_returned_data] << order_data
    end
    designer_stats
  end

  def calculating_avgs_designer_report(designers_stats,date_diff,report_start_date,report_end_date)
    designers_stats.map do |designer|
      if designer.present?
        if designer[:total_orders] > 0
          if report_start_date > (Date.parse designer[:designer_sign_up_date].to_s)
            designer[:avg_orders] = designer[:total_orders]/date_diff
          else
            date_diff = (((report_end_date - Date.parse(designer[:designer_sign_up_date].to_s)).to_f)/30).ceil
            designer[:avg_orders] = designer[:total_orders]/date_diff if date_diff > 0
          end
        end
        designer[:avg_dispatch_time] = designer[:total_shipping_time_for_all_orders] / designer[:total_orders_pickup_date] if designer[:total_shipping_time_for_all_orders] > 0
        designer
      end
    end
    designers_stats
  end

  def get_designers_info(start_date, end_date, designer, state)
    #Getting total no of designs for designers*
    w_design_state_clause = {state: 'in_stock'}
    s_design_count_clause = 'count(id) as design_count, designer_id as d_id'
    g_design_designer_clause = 'designer_id'
    s_designer_clause = 'designers.id,name,designers.phone,state_machine,accounts.last_sign_in_at as designer_last_sign_in,accounts.created_at as designer_sign_up_date,gst_no,gst_address,taxpayer_trade_name'
    klass = current_account.remote_vendor? && current_account.accountable.designers.present? ? current_account.accountable.designers : Designer
    designers =  if state.present?
      klass.joins(:account).where(state_machine: state)
    else
      klass.joins(:account)
    end
    if designer.blank? || (designer == 'All')
      design_count =  Design.select(s_design_count_clause).where(w_design_state_clause).group(g_design_designer_clause).group_by{|d| d[:d_id]}
    else
      design_count = Design.select(s_design_count_clause).where(w_design_state_clause).where('designer_id = ?', designer).group(g_design_designer_clause).group_by{|d| d[:d_id]}
      designers = designers.where(:id => designer)
    end

    issue_hash = {}
    designers.joins(:designer_issues).where('designer_issues.created_at between ? and ?',start_date,end_date).group('designer_id','issue_type').select('designer_id,issue_type,count(designer_issues.id) as count_all').each do|di|
      (issue_hash[di.designer_id]||= {})[di.issue_type] = di.count_all.to_i
    end 

    designers = designers.select(s_designer_clause)
    return designers, issue_hash, design_count
  end

  def create_coupon_order
    return1 = Return.find_by_id(params[:return_id])
    if return1.payment_complete?
      params[:ajax_call] ? (render json: {coupon_code: 0, msg: 'Unable to create coupon as return payment is completed !'} and return) : (redirect_to :back, notice: 'Unable to create coupon as return payment is completed !')
    end
    if (return_values = Coupon.validate_and_create_coupon(coupon_params, params[:order_id], return1, account: current_account)).present?
      if params[:ajax_call]
        render json: return_values
      else
        redirect_to :back, :notice => 'Coupon Created - '+ return_values[:coupon_code]
      end
    else
      if params[:ajax_call]
        render json: {coupon_code: 0, msg: 'Unable to create a coupon. Please check coupon amount.'}
      else
        redirect_to :back, :notice => 'Error while creating coupon for order no '+ params[:order_id]
      end
    end
  end


  def tags_stats
    if(params[:start_date].present?) 
      unless Date.valid_date?(params[:start_date][:year].to_i,params[:start_date][:month].to_i,params[:start_date][:day].to_i) && Date.valid_date?(params[:end_date][:year].to_i,params[:end_date][:month].to_i,params[:end_date][:day].to_i)
        redirect_to admin_tag_stats_path, notice: 'Invalid date ' and return
      end
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
    else
      @start_date = Date.today.beginning_of_month
    end


    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.beginning_of_day
    elsif @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
      @end_date = Date.today
    else
      @end_date = @start_date.end_of_month
    end

    @orders = Order.unscoped.tagged_with(params[:tag]).select('count(distinct orders.id) as count_all, sum(distinct orders.total) as total_all').joins(:designer_orders).where(designer_orders: {state: ['pending', 'dispatched', 'completed', 'pickedup']}).where('orders.confirmed_at BETWEEN ? AND  ?', @start_date.beginning_of_day, @end_date.end_of_day)[0]

    @summary = Order.unscoped.tagged_with(params[:tag]).select("orders.confirmed_at::date as confirmed_date, count(distinct orders.id) as count_all, sum(distinct orders.total) as total_all, string_agg(distinct orders.number,', ') as numbers").joins(:designer_orders).where(designer_orders: {state: ['pending', 'dispatched', 'completed', 'pickedup']}).where('orders.confirmed_at BETWEEN ? AND  ?', @start_date.beginning_of_day, @end_date.end_of_day).group('orders.confirmed_at::date').paginate(page: params[:page], per_page: 30)
  end

  def international_dispatch_report
    ship_time = 8
    ship_time = params[:t].to_i if params[:t]
    @start_date, @end_date = get_date
    w_date_clause = 'created_at BETWEEN ? and ?', @start_date, @end_date.end_of_day
    w_inter = 'lower(orders.country) <> ?', 'india'
    # TOTAL ORDERS AND AVG SLA QUERY
    select_avg = 'COUNT(id) AS total_orders, round(AVG((DATE(pickup) - DATE(confirmed_at))),2) AS avg_st, round(AVG((DATE(items_received_on) - DATE(confirmed_at))), 2) AS avg_ipt'
    order_stats = Order.unscoped.select(select_avg).where('pickup IS NOT NULL').where(w_date_clause).where(w_inter)[0]
    @total_orders = order_stats[:total_orders].to_i
    @avg_st = order_stats[:avg_st]
    @avg_ipt = order_stats[:avg_ipt]
    select = 'orders.*, ((DATE(pickup) - DATE(confirmed_at))) AS st, (DATE(items_received_on) - DATE(confirmed_at)) as ipt'
    orders = Order.unscoped.select(select).where('pickup IS NOT NULL').where(w_date_clause).where(w_inter).order('st DESC')
    @orders_good_count, @orders_bad_data_count = 0, 0
    @orders_bad = []
    orders.each do |order|
      if order[:st].present?
        st = order[:st].to_i
        if st >= ship_time
          @orders_bad << order
        else
          @orders_good_count += 1
        end
      else
        @orders_bad_data_count += 1
      end
    end
    @orders_bad_count = @orders_bad.count
    @orders_bad_count_percent = ((@orders_bad_count.to_f/@total_orders.to_f) * 100).round(2)
    @orders_good_count_percent = ((@orders_good_count.to_f/@total_orders.to_f) * 100).round(2)
  end

  def domestic_dispatch_report(multi_report = false)
    if multi_report == false
      @start_date, @end_date = get_date
      @designers = designer_list_w_all
    end

    if params[:designer_id].present? and params[:designer_id] != 'All' and @designer.blank?
      w_d_clause = 'cached_slug = ? or id = ?', params[:designer_id], params[:designer_id].to_i
      @designer = Designer.where(w_d_clause).first
    end

    authorize! :designer_report, @designer

    ship_time = MIRRAW_DESIGNER_SLA
    ship_time = params[:t].to_i if params[:t]
    w_date_clause = 'designer_orders.created_at BETWEEN ? and ?', @start_date, @end_date.end_of_day
    w_date_clause_design = 'created_at BETWEEN ? and ?', @start_date, @end_date.end_of_day
    w_country = 'lower(orders.country) = ?', 'india'
    w_country = 'lower(orders.country) <> ?', 'india' if params[:region].present? and params[:region] == 'inter'
    w_pickup = 'designer_orders.pickup IS NOT NULL'
    select_avg = 'COUNT (designer_orders.id) AS total_orders, round(AVG((DATE(designer_orders.pickup) - DATE(designer_orders.confirmed_at))),2) AS avg_st'
    if @designer.present?
      designer_order_stats = DesignerOrder.unscoped.select(select_avg).joins(:order, :designer).where(w_pickup).where(w_date_clause).where(w_country).where(:designer_id => @designer.id).to_a.first
    else
      designer_order_stats = DesignerOrder.unscoped.select(select_avg).joins(:order).where(w_pickup).where(w_date_clause).where(w_country).to_a.first
    end
    @avg_st = designer_order_stats[:avg_st]
    @total_orders = designer_order_stats[:total_orders]

    select = 'designer_orders.pickup as pickup, designers.name as designer_name, designers.id as designer_id, orders.number as order_number, designer_orders.confirmed_at as confirmed_at, ((DATE(designer_orders.pickup) - DATE(designer_orders.confirmed_at))) AS st'
    if @designer.present?
      designer_orders = DesignerOrder.unscoped.joins(:order, :designer).select(select).where(w_pickup).where(w_date_clause).where(w_country).where(:designer_id => @designer.id).order('st DESC')
      @designer_designs = @designer.designs.where(w_date_clause_design).count
      @designer_return = @designer.designer_orders.where(w_date_clause).where(state: 'buyer_returned').count
    else
      designer_orders = DesignerOrder.unscoped.joins(:order, :designer).select(select).where(w_pickup).where(w_date_clause).where(w_country).order('st DESC')
    end
    @des_orders_bad, des_orders_bad_data = [],[]
    @des_orders_good_count = 0
    designer_orders.each do |des_order|
      if des_order[:st].present?
        st = des_order[:st].to_i
        if st >= ship_time
          @des_orders_bad << des_order
        else
          @des_orders_good_count += 1
        end
      else
        des_orders_bad_data << des_order
      end
    end
    @des_orders_bad_count = @des_orders_bad.count
    @des_orders_bad_count_percent = ((@des_orders_bad_count.to_f/@total_orders.to_f) * 100).round(2)
    @des_orders_good_count_percent = ((@des_orders_good_count.to_f/@total_orders.to_f) * 100).round(2)
    @des_orders_bad_data_count = des_orders_bad_data.count
  end

  def create_designer_issue
    order_id = params[:order_id]
    design_id = params[:design_id]
    issue_type = params[:issue_type]
    # Check params
    if order_id.present? and design_id.present? and issue_type.present?
      # Check Designer Order
      line_item = LineItem.includes(:order).where(design_id: design_id, orders: {id: order_id}).first
      des_order = line_item.try(:designer_order)
      if des_order.present?
        order = des_order.order
        if DesignerIssue.create_issue(line_item, issue_type)
          variant_available = false
          if issue_type == 'OOS'
            if line_item.variant_id.present?
              variant_available = line_item.id
            else
              # line_item.design.sidekiq_delay(priority: -5).mark_out_of_stock
              line_item_design = line_item.design 
              SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(line_item_design.class.to_s,line_item_design.id,"mark_out_of_stock")
            end
          end
          # Add Notes and tags
          event = order.add_notes_without_callback("#{design_id} #{issue_type}", 'replacement', current_account)
          order.add_tags_skip_callback(issue_type)
          tags = order.tag_list.reject{|tag| tag.include?('convert-mkt')}.join(',')
          line_item.add_into_scan("Issue Created - #{issue_type}", current_account.id)
          render :json => {:mess => 'Issue for order ' + order.number + ' design id ' + design_id.to_s + ' created', notes: event, time: event.event_timestamp.strftime('%d, %b %y'), :tags => tags, variant_available: variant_available}
        else
          render :json => {:error => 'Issue for order ' + order.number + ' design id ' + design_id.to_s + ' already exist'}
        end
      else
        render :json => {:error => 'Error finding design in order'}
      end
    elsif (line_item_id = params[:oos_line_item_id]).present?
      if (line_item = LineItem.find_by_id line_item_id).present?
        type = params[:oos_type] == 'variant' ? line_item.variant : nil
        # line_item.design.sidekiq_delay.mark_out_of_stock(type)
        line_item_design = line_item.design 
        SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(line_item_design.class.to_s,line_item_design.id,"mark_out_of_stock", type.present? ? {type.class.to_s => type.id} : type)
        render :json => {:mess => "#{params[:oos_type]} is marked out of stock"}
      else
        render :json => {:error => 'Item not found !'}
      end
    else
      render :json => {:error => 'Please check input'}
    end
  end

  def create_claim_design
    order = Order.find_by_id(params[:order_id])
    line_item = LineItem.find_by_id(params[:line_item])
    design_id = params[:design_id]
    designer_issue = DesignerIssue.where(line_item_id: params[:line_item].to_i).first
    unless designer_issue.claim_mark
      designer_issue.update_column(:claim_mark,true)
      line_item.update_column(:claim_flag,false)
      line_item.add_into_scan('Marked For Claim', current_account.id)
      order.add_notes_without_callback("#{design_id} Marked for claim", 'replacement', current_account)
    end
    redirect_to :back
  end

  def view_claim_requests
    issue_type = params[:issues].present? && params[:issues] != 'All' ? {issue_type: params[:issues]} : nil
    @claim_products = DesignerIssue.unscoped.joins(:order).joins{claim_requests.outer}.preload(:order,:design=> [:designer,:images],:claim_requests=>[:design=>[:designer,:images]]).where("claim_mark = ? and designer_issues.state=? and orders.state=?",true,'pending','sane').where(issue_type).order('claim_requests.designer_issue_id is not null desc').paginate(page: params[:page], per_page: 10)
  end

  def designs_under_review
    multi_design_state_change(params[:design_ids], params[:state_change], params[:reason]) if params[:design_ids].present? and params[:state_change].present?
    state = 'review'
    state = params[:state] if params[:state].present?
    includes_block = [:images, :designer, :designable, :dynamic_size_charts, [categories: :dynamic_size_charts]]
    
      @designs = Design.includes(includes_block)
                 .where(state: state)
                 .where('designer_batch_id ISNULL OR designs.modified_design_data = ?', true)
      @designs = @designs.where(designer_id: params[:designer_id]) if params[:designer_id].present? && params[:designer_id] != 'All'
      @start_date = params[:start_date]
      @end_date = params[:end_date]
      if @start_date.present? && @end_date.present?
        @designs = @designs.where('designs.created_at::date BETWEEN ? AND ?', @start_date, @end_date)
      end
      @designs = @designs.paginate(page: params[:page], per_page: 30)

    category_names = @designs.collect(&:category_ids).reject(&:blank?).uniq.collect{|c| "dynamic_size_chart_category_#{c.join(',')}"}.to_a
    RequestStore.cache_preload(*category_names) if category_names
    @designers = designer_list_w_all(filter_designers:true)
  end

  def tailoring_marked_paid
    tailor = Tailor.where(name: params[:tailor_name]).first
    if params[:tailoring_numbers].present?
      tailoring_numbers = params[:tailoring_numbers].collect{|key, value| value.to_i if value.present?}
      if params[:commit] == 'Submit'
        tailoring_data = TailoringInfo.where(id:tailoring_numbers)
        tailoring_data.where('tailor_name1 =?',params[:tailor_name]).update_all(paid_status_tailor1: 'Y',payout_tailor1_paid_timestamp: Time.current)
        tailoring_data.where('tailor_name2 =?',params[:tailor_name]).update_all(paid_status_tailor2: 'Y',payout_tailor2_paid_timestamp: Time.current)
        redirect_to admin_tailor_payout_url, notice: "Tailoring payment is successfully paid to #{params[:tailor_name]} tailor "
      elsif params[:commit] == 'Payout'
        respond_to do |format|
            format.html {redirect_to admin_tailor_payout_path(ids:tailoring_numbers,tailor_name: params[:tailor_name],start_date:params[:start_date] , end_date:params[:end_date])}
        end
      elsif params[:commit] == 'Payout Pending'
        tailoring_data = TailoringInfo.where(id:tailoring_numbers)
        tailoring_data.where('tailor_name1 =?',params[:tailor_name]).update_all(paid_status_tailor1: 'pending')
        tailoring_data.where('tailor_name2 =?',params[:tailor_name]).update_all(paid_status_tailor2: 'pending')
        redirect_to admin_tailor_payout_url, notice: "Tailoring payment for #{params[:tailor_name]} tailor is in pending state"
      end
    else
      redirect_to admin_tailor_payout_url
    end
  end

  def tailor_payout
    @integration_status = 'new'
    @start_date = params[:start_date].present? ? (DateTime.parse(params[:start_date]).to_date).strftime("%A, %d %B, %Y") : (DateTime.now.beginning_of_day-1.months).strftime("%A, %d %B, %Y")
    @end_date = params[:end_date].present? ? (DateTime.parse(params[:end_date]).to_date + 1.day).strftime("%A, %d %B, %Y")  : (DateTime.now.end_of_day + 1.day).strftime("%A, %d %B, %Y")    
    @tailor = Tailor.where(name: params[:tailor_name]).first
    if params[:ids].present?
      tailoring_data = TailoringInfo.where(id:params[:ids])
      tailoring_materials1 = tailoring_data.where('tailor_name1 =?',params[:tailor_name])
      tailoring_materials2 = tailoring_data.where('tailor_name2 =?',params[:tailor_name])
    elsif params[:payout_state].present?
      tailoring_data = TailoringInfo.joins(:order).where('orders.state NOT IN (?) ',['cancel', 'cancel_complete']).where('orders.created_at BETWEEN ? AND  ?', @start_date,@end_date)
      payout_state = params[:payout_state] == 'Paid' ? 'Y' : 'pending'
      tailoring_materials1 = tailoring_data.where('tailor_name1 =?',params[:tailor_name]).where(paid_status_tailor1: payout_state)
      tailoring_materials2 = tailoring_data.where('tailor_name2 =?',params[:tailor_name]).where(paid_status_tailor2: payout_state)
    else
      tailoring_orders = TailoringInfo.joins(:order).where('orders.state !=? ','canceled').where('orders.created_at BETWEEN ? AND  ?', @start_date,@end_date)
      if tailoring_orders.present?
        tailoring_materials1 = tailoring_orders.where('tailor_name1 =?',params[:tailor_name]).where('paid_status_tailor1 IS NULL OR paid_status_tailor1 NOT IN (?)',['Y', 'pending']).where('material1_received_status IS NOT NULL AND material1_received_status =?',true)
        tailoring_materials2 = tailoring_orders.where('tailor_name2 =?',params[:tailor_name]).where('paid_status_tailor2 IS NULL OR paid_status_tailor2 NOT IN (?)',['Y', 'pending']).where('material2_received_status IS NOT NULL AND material2_received_status =?',true)
      end
    end
    if tailoring_materials1.present? || tailoring_materials2.present?
      tailoring_payout_calculation(@tailor, tailoring_materials1,tailoring_materials2)
      @tailoring_info = (tailoring_materials1 + tailoring_materials2).paginate(page: params[:page], per_page: 40)
    end
  end

  def tailoring_payout_calculation(tailor,tailoring_materials1,tailoring_materials2)
    @tailoring_payout_total = 0 
    material1 = material2 = []
    material1 = tailoring_materials1.select([:tailoring_material1, :line_item_quantity]).map {|e| {material: e.tailoring_material1, line_item_quantity: e.line_item_quantity} } if tailoring_materials1.present?
    material2 = tailoring_materials2.select([:tailoring_material2, :line_item_quantity]).map {|e| {material: e.tailoring_material2, line_item_quantity: e.line_item_quantity} } if tailoring_materials2.present?
    tailoring_materials =  material1 + material2
      tailoring_materials.each do |tailoring_material|
        case tailoring_material[:material]
        when 'Saree'
          @tailoring_payout_total += (tailor.saree_rate.to_i * tailoring_material[:line_item_quantity])
        when 'Padded Blouse'
          @tailoring_payout_total += (tailor.padded_blouse_rate.to_i* tailoring_material[:line_item_quantity])
        when 'Simple Blouse'
          @tailoring_payout_total += (tailor.simple_blouse_rate.to_i* tailoring_material[:line_item_quantity])
        when 'Anarkali'
          @tailoring_payout_total += (tailor.anarkali_rate.to_i* tailoring_material[:line_item_quantity])
        when 'lehenga'
          @tailoring_payout_total += (tailor.lehenga_rate.to_i* tailoring_material[:line_item_quantity])
        when 'Gown'
          @tailoring_payout_total += (tailor.gown_rates.to_i* tailoring_material[:line_item_quantity])
        when 'Lehenga + Choli'
          @tailoring_payout_total += (tailor.lehenga_choli_rates.to_i* tailoring_material[:line_item_quantity])
        when 'lehenga + padded choli'
          @tailoring_payout_total += (tailor.lehenga_padded_choli_rates.to_i* tailoring_material[:line_item_quantity])
        when 'lehenga+simple choli'
          @tailoring_payout_total += (tailor.lehenga_simple_choli_rates.to_i* tailoring_material[:line_item_quantity])
        when 'FNP'
          @tailoring_payout_total += (tailor.fnp_rates.to_i* tailoring_material[:line_item_quantity])
        end
      end
  end

  def tailoring_info_page
    @start_date,@end_date = get_date('month')
    check_date = ((@start_date.to_date + 6.month) >= @end_date.to_date) && (@start_date <= @end_date)    
    tailor_name, tailor_id = JSON.parse(params[:tailor_id]) if params[:tailor_id].present?
    @tailoring_infos = TailoringInfo.get_tailoring_info(@start_date,@end_date, params[:received_status], tailor_id, params[:payout], params[:batch_number], false)
    @tailors = Tailor.get_tailor(false)
    if params[:commit] == 'Mail CSV' && check_date
      if @tailoring_infos.present?
        SidekiqDelayGenericJob.set(queue: 'low').perform_async("TailoringInfo", nil, "export_tailoring_info_csv", @start_date, @end_date, {"#{current_account.class}": current_account.id}, params[:received_status], tailor_id, params[:payout], params[:batch_number])
        #TailoringInfo.sidekiq_delay(queue: 'low')
        #             .export_tailoring_info_csv(
        #               @start_date,
        #               @end_date,
        #               current_account,
        #               params[:received_status],
        #               tailor_id,
        #               params[:payout],
        #               params[:batch_number]
        #             )
        @notice = 'Mail csv in process'
        redirect_to admin_tailoring_info_path, notice: @notice
      else
        @notice = 'No data'
      end
    end
    @notice = 'Please Select Date Difference of 6 month or less for mail CSV' if !check_date && params[:commit] == 'Mail CSV'
    @tailoring_infos = @tailoring_infos.paginate(page: params[:page], per_page: 30)
  end

  def logistic_info_page
    start_date = (params[:start_date].present? ? Date.parse(params[:start_date]) : Time.now - 90.days).beginning_of_day
    end_date = (params[:end_date].present? ? Date.parse(params[:end_date]) : Time.now).end_of_day
    check_date = ((start_date.to_date + 90.days) >= end_date.to_date) && (start_date <= end_date)        
    where_clause = params[:shipper_id].present? ? "shipper_id = #{params[:shipper_id]}" : ''
    w_do_state = "designer_orders.state IN (?)", ['pickedup', 'dispatched', 'completed']
    @logistic_infos = DesignerOrder.joins(:order).where(pickup: start_date..end_date).where(where_clause).where(w_do_state)
    @shippers = Shipper::ALL_SHIPPERS
    if params[:commit] == 'Mail CSV' && check_date
      if @logistic_infos.present?
        DesignerOrder.sidekiq_delay(queue: 'high')
                     .export_logistic_info_csv(
                       @logistic_infos.pluck(:id),
                       current_account.email
                     )
        @notice = 'CSV File will be emailed shortly....'
        redirect_to admin_logistic_info_path, notice: @notice
      else
        @notice = 'No data'
      end
    end
    @notice = 'Please Select Date Difference of 90 days or less for mail CSV' if !check_date && params[:commit] == 'Mail CSV'
    @logistic_infos = @logistic_infos.paginate(page: params[:page], per_page: 30)
  end

  def oos
    @hide_designer_list = false                        #for Form
    slas = [2, 5, 10]                                  #No of days ago
    @issues={}                                         #issues hash
    includes_block = [order: :events,design: [:images, :designer]]
    @issue_list = ['All','QC Failed','OOS','wrong_received','Damaged']
    @designer_issues = DesignerIssue.preload(includes_block).joins(:order).where('orders.state=? AND lower(orders.country) != ?','sane','india').where('designer_issues.state=?','pending')
    @designer_issues_count = @designer_issues.count
    urgent_orders = Order.where(id: @designer_issues.pluck('order_id')).tagged_with('urgent').pluck(:id)
    @urgent_designer_issues = @designer_issues.where(order: {id: urgent_orders}) if urgent_orders.present?
    slas.each do |sla|
      key = "Received #{sla} Days Ago"
      value = sla.days.ago
      @issues[key] = { count: @designer_issues.where("designer_issues.created_at<?", value).count, sla: sla }
    end
    if params[:sla].present?
      @designer_issues = @designer_issues.where("designer_issues.created_at<?",params[:sla].to_i.days.ago)
    end
    if params[:issue_type].present? && params[:issue_type]!='All'
      @issue_selected = params[:issue_type]
      @designer_issues = @designer_issues.where(issue_type: params[:issue_type])
    end
    if params[:designer_id].present? && params[:designer_id] != 'All'
      @designer_id = params[:designer_id]
      @designer_issues = @designer_issues.where(designer_id: params[:designer_id])
    end
    @designers = @designer_issues.unscoped.joins(:designer).select('designers.id as designer_id,designers.name as designer_name').group('designers.id').collect{|di| [di['designer_name'],di['designer_id']] }.prepend(['All','All'])
    @designer_issues = @designer_issues.paginate(page: params[:page], per_page: 40)
  end

  def potential_order_dispatch
    if params['order_type'].present?
      OrderMailer.sidekiq_delay(queue: 'high')
                 .send_potential_orders_for_dispatch(
                   current_account.email,
                   params['order_type']
                 )
      redirect_to :back, notice: "Mail sent successfully"
    end
  end

  def designer_issues(multi_report = false)
    if multi_report == false
      @start_date, @end_date = get_date
      @designers = designer_list_w_all
    end

    if params[:designer_id].present? and params[:designer_id] != 'All' and @designer.blank?
      w_d_clause = 'cached_slug = ? or id = ?', params[:designer_id], params[:designer_id].to_i
      @designer = Designer.where(w_d_clause).first
    end

    authorize! :designer_report, @designer

    w_clause = 'designer_issues.created_at BETWEEN ? and ?', @start_date, @end_date.end_of_day

    # Count Of Issues
    s_count_clause = 'count(designer_issues.id) as count_all, designers.name as designer_name, designer_issues.issue_type as issue_type1, designer_issues.designer_id'
    g_clause = 'designer_issues.designer_id, designer_issues.issue_type, designers.name'
    o_count_clause = 'count_all DESC'

    # List Of Issues
    s_clause = 'designer_issues.*,designers.name as designer_name,designs.title as design_title, orders.confirmed_at as order_confirmed_at, orders.total as order_total, orders.number as order_number'
    j_clause = :designer, :order, :design
    o_clause = 'designer_id, order_id DESC, created_at DESC'

    if @designer.present?
      @designer_issues = DesignerIssue.unscoped.select(s_clause).joins(j_clause).where(w_clause).where(:designer_id => @designer.id).order(o_clause)
      @designer_issues_count = DesignerIssue.unscoped.select(s_count_clause).joins(:designer).where(w_clause).where(:designer_id => @designer.id).group(g_clause).order(o_count_clause)
    else
      @designer_issues = DesignerIssue.unscoped.select(s_clause).joins(j_clause).where(w_clause).order(o_clause)
      @designer_issues_count = DesignerIssue.unscoped.select(s_count_clause).joins(:designer).where(w_clause).group(g_clause).order(o_count_clause)
    end
  end

  def designer_report
    @start_date, @end_date = get_date('month')
    @designers = promise { designer_list_w_all }
    w_d_clause = 'cached_slug = ? or id = ?', params[:designer_id], params[:designer_id].to_i
    @designer = Designer.where(w_d_clause).first
    redirect_to request.referer.present? ? :back : :root, notice: 'Vendor does not exist' and return if (!@designer && current_account.admin?)
    authorize! :designer_report, @designer
    designer_issues(true)
    domestic_dispatch_report(true)
  end

  def designers
    @columns_visible = ['name', 'phone', 'street', 'city', 'state', 'state_machine', 'gst_no', 'production_type']
    klass = current_account.remote_vendor? && current_account.accountable.designers.present? ? current_account.accountable.designers : Designer
    @designers = if params[:search].present?
      @designer_owners = DesignerOwner.preload(:account).map{|i| [i.name, i.id]}
      klass.preload(owner: :account).search(params[:search]).order('id asc').paginate(per_page: 20, page: params[:page].presence || 1)
    end
  end

  def approve_designer_gst
    response={error:'unable to approve GST number'}
    if (designer = Designer.find_by_id(params[:designer_id])).present? && designer.update_column(:approved_on, DateTime.now)
      response = {success:"approved"}
    end
    render json: response
  end

  def designers_show
    @designer = Designer.find(params[:designer_id])
  end

  def designers_edit
    @designer = Designer.find(params[:designer_id])
  end

  def health
    @des_sla = MIRRAW_DESIGNER_SLA
    @ord_sla = MIRRAW_INTERNATIONAL_SLA
    @des_sla = params[:d].to_i if params[:d].present?
    @ord_sla = params[:o].to_i if params[:o].present?

    # Revenue Block
    @start_date, @end_date = get_date
    @end_date = @end_date.end_of_day
    w_total_clause = 'designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched', 'completed'
    w_date_clause = 'designer_orders.created_at BETWEEN ? AND  ?', @start_date, @end_date
    s_clause = 'SUM(total) as total_revenue, COUNT(id) as total_do, SUM(payout) as total_payout'
    @total_dos_stat = DesignerOrder.unscoped.select(s_clause).where(w_total_clause).where(w_date_clause).first
    @total_dos_b_stat = DesignerOrder.unscoped.select(s_clause).where(:state => 'buyer_returned').where(w_date_clause).first

    # Shipping Good Orders
    good_order_states = ['sane', 'complete', 'confirmed', 'pickedup', 'dispatched']
    w_o_date_clause = 'orders.created_at BETWEEN ? AND ?', @start_date, @end_date
    s_o_clause = 'SUM(shipping) as total_shipping'
    @shipping = Order.unscoped.select(s_o_clause).where(:state => good_order_states).where(w_o_date_clause).first

    #SLA Clauses
    w_pickup = 'designer_orders.pickup IS NOT NULL'
    s_avg_clause = 'count(designer_orders.id) as total_orders, round(AVG((DATE(designer_orders.pickup) - DATE(orders.confirmed_at))),2) AS avg_st'
    w_sla_bad = '(DATE(designer_orders.pickup) - DATE(orders.confirmed_at)) >= ?', @des_sla
    w_sla_good = '(DATE(designer_orders.pickup) - DATE(orders.confirmed_at)) < ?', @des_sla
    w_inter = 'lower(orders.country) <> ?', 'india'

    # SLA India
    w_india = 'lower(orders.country) = ?', 'india'
    @sla_india = DesignerOrder.unscoped.joins(:order).select(s_avg_clause).where(w_date_clause).where(w_pickup).where(w_india).first
    @sla_india_good = DesignerOrder.unscoped.joins(:order).select(s_avg_clause).where(w_date_clause).where(w_pickup).where(w_india).where(w_sla_good).first
    @sla_india_bad = DesignerOrder.unscoped.joins(:order).select(s_avg_clause).where(w_date_clause).where(w_pickup).where(w_india).where(w_sla_bad).first

    # SLA Designer
    s_avg_clause_des = s_avg_clause + ', designers.name as designer_name'
    g_sla_des = 'designers.name'
    o_clause = 'total_orders DESC'
    @sla_designer = DesignerOrder.unscoped.joins(:order, :designer).select(s_avg_clause_des).where(w_date_clause).where(w_pickup).where(w_sla_bad).order(o_clause).group(g_sla_des).limit(10)

    # SLA International Dispatched
    s_sla_inter_dis_clause = 'COUNT(id) AS total_orders, round(AVG((DATE(pickup) - DATE(confirmed_at))),2) AS avg_st, round(AVG((DATE(items_received_on) - DATE(confirmed_at))), 2) AS avg_ipt'
    w_sla_inter_dis_date_clause = 'created_at BETWEEN ? and ?', @start_date, @end_date
    w_sla_inter_dis_pick_clause = 'pickup IS NOT NULL'
    @sla_inter_disp = Order.unscoped.select(s_sla_inter_dis_clause).where(:state => good_order_states).where(w_sla_inter_dis_pick_clause).where(w_sla_inter_dis_date_clause).where(w_inter).first
    w_sla_inter_dis_good_clause = 'DATE(pickup) - DATE(confirmed_at) < ?', @ord_sla
    @sla_inter_disp_good = Order.unscoped.select(s_sla_inter_dis_clause).where(:state => good_order_states).where(w_sla_inter_dis_pick_clause).where(w_sla_inter_dis_date_clause).where(w_inter).where(w_sla_inter_dis_good_clause).first
    w_sla_inter_dis_bad_clause = 'DATE(pickup) - DATE(confirmed_at) >= ?', @ord_sla
    @sla_inter_disp_bad = Order.unscoped.select(s_sla_inter_dis_clause).where(:state => good_order_states).where(w_sla_inter_dis_pick_clause).where(w_sla_inter_dis_date_clause).where(w_inter).where(w_sla_inter_dis_bad_clause).first

    # Designer Issue Block
    w_di_date_clause = 'created_at BETWEEN ? AND ?', @start_date, @end_date
    s_di_clause = 'count(id) as total_issues'
    @total_designer_issues = DesignerIssue.unscoped.select(s_di_clause).where(w_di_date_clause).first
    s_di_clause = 'count(id) as total_issues, issue_type as issue_type1'
    g_di_clause = 'issue_type'
    o_di_clause = 'total_issues DESC'
    @total_designer_issues_type = DesignerIssue.unscoped.select(s_di_clause).where(w_di_date_clause).order(o_di_clause).group(g_di_clause)

    designer_issues = @total_designer_issues_type.select {|di| di[:issue_type1]}
    s_di_designers_clause = 'count(designer_issues.id) as total_issues, designer_issues.issue_type as issue_type1, designers.name as designer_name'
    g_di_designers_clause = g_di_clause + ', designers.name'
    @total_designer_issues_type_designers = DesignerIssue.unscoped.joins(:designer).select(s_di_designers_clause).where(w_di_date_clause).order(o_di_clause).group(g_di_designers_clause).group_by{|di| di[:issue_type1]}
  end

  def inventory

  end

  def designers_summary
    if params[:date].blank?
      @end_date = Date.today.beginning_of_month
    else
      @end_date = create_date({:day => 1, :month => params[:date][:month], :year => params[:date][:year]})
    end
    start_date    = @end_date - 2.months

    @start_month = start_date.strftime("%b-%Y")
    @mid_month = (start_date + 1.month).strftime("%b-%Y")
    @end_month = @end_date.strftime("%b-%Y")

    w_date = 'orders.confirmed_at BETWEEN ? AND  ?', start_date.beginning_of_day, @end_date.end_of_day
    s_des_earning = 'designer_orders.designer_id, SUM(designer_orders.total) as total_revenue'
    s_des_earning += ", to_char(orders.confirmed_at + interval '330 minutes', 'Mon-YYYY') as period_month"

    @des_earning  = DesignerOrder.unscoped.select(s_des_earning).includes(:designer).joins(:order).
                    sane_orders.where(w_date).group(:designer_id, 'period_month').order('total_revenue DESC').
                    group_by{|des_od| des_od.designer_id}
  end

  def ready_for_dispatch 
    w_region_check, w_warehouse_id_check, w_hours_filter, w_stitching_check = [], [], [], []
    if params[:start_hour].present? && params[:end_hour].present?
      hours_params = [params[:start_hour].to_i, params[:end_hour].to_i].sort
      w_hours_filter = ['ready_for_dispatch_at between ? and ?', hours_params[1].hours.ago, hours_params[0].hours.ago]
    end
    if params[:region].present?
      w_region_check = params[:region] == 'international' ? ['orders.country <> ?', 'India'] : ['orders.country = ?', 'India']
    end
    if params[:warehouse_id].present?
      w_warehouse_id_check = "designer_orders.warehouse_address_id = " + params[:warehouse_id].to_s
    end
    if params[:stitch_check].present?
      w_stitching_check = if params[:stitch_check] == 'Yes' 
        ['orders.other_details @> hstore(?,?)','stitching_order','true']
      else
        "not orders.other_details ? 'stitching_order' or orders.other_details @> 'stitching_order => false'"
      end
    end
    oom_orders_number = OutOfMirraw::ReadyForDispatch.out_of_mirraw_query.uniq
    @orders = Order.unscoped.includes(:tags, :shipment_buckets, line_items: [designer_order: :rack_list]).joins(:designer_orders).where(w_region_check).where(state: 'ready_for_dispatch').where(w_hours_filter).where(w_stitching_check).where(w_warehouse_id_check).group('orders.id').order(:ready_for_dispatch_at)
    @shipment_state_checked      =[]
    shipment_errors              =[]
    @orders_line_items_invoicing =[]
    @oom_orders= []
    all_orders, cargo_item       =[], false
    combined_orders = (@orders + oom_orders_number).uniq
    combined_orders.each do |order|
      shipment_errors << order if (order.tags.any? {|t| t.name == 'shipment_error'}) && !oom_orders_number.include?(order)
      @orders_line_items_invoicing << order if (order.line_items.any? {|l| l.sent_to_invoice.present?})
      @oom_orders << order if oom_orders_number.include?(order)
      all_orders << order
      cargo_item = true if !cargo_item && order.shipment_buckets.present?
    end
    @orders_line_items_invoicing = @orders_line_items_invoicing.to_a.uniq
    @oom_orders_count = @oom_orders.length
    @shipment_errors_count = shipment_errors.length
    @sent_for_invoice = @orders_line_items_invoicing - shipment_errors - oom_orders_number
    @sent_for_invoice_count = @sent_for_invoice.length
    @excluded_orders = all_orders -  @orders_line_items_invoicing - shipment_errors - oom_orders_number
    @excluded_orders_count = @excluded_orders.length
    orders_list = []
    show_flag = false
    if params[:mail_rfd_data].present?
      Admin.sidekiq_delay
           .mail_rfd_data(
             params,
             w_region_check,
             w_hours_filter,
             w_stitching_check,
             current_account.email
           )
      flash[:notice] = 'RFD Report Will be Mailed To You Shortly.'
    end
    if params[:All].nil? 
      if params[:"Shipment Error"].present?
        orders_list = shipment_errors
        @shipment_state_checked << "Shipment Error"
        show_flag = true
      end
      if params[:"Exclude Shipment Error & sent for invoice"].present?
        orders_list = orders_list + @excluded_orders
        @shipment_state_checked << "Exclude Shipment Error & sent for invoice"
        show_flag = true
      end
      if params[:"Sent for Invoicing"].present?
        orders_list = orders_list + @sent_for_invoice
        @shipment_state_checked << "Sent for Invoicing"
        show_flag = true
      end
      if params[:"OOM Delay"].present?
        orders_list = orders_list + @oom_orders
        @shipment_state_checked << "OOM Delay"
        show_flag = true
      end
      unless show_flag
        orders_list = all_orders
      end
    elsif 
      orders_list = all_orders
    end
    @orders_list_count = orders_list.length
    @urgent_ready_for_dispatch = []
    orders_list.each do |order|
      @urgent_ready_for_dispatch << order if (order.tags.any? {|t| t.name == 'urgent'})
    end
    @order_count = orders_list.to_a.uniq.length
    @warehouse_ids = WarehouseAddress.all.pluck(:id)
    @orders = orders_list.sort_by(&:ready_for_dispatch_at).uniq.paginate(page: params[:page],per_page: 50)
    if cargo_item
      ActiveRecord::Associations::Preloader.new.preload(@orders, [line_items: [variant: :option_type_values]]);nil
    end
  end

  def ipending_list
    if params[:order_type] == "All Items Received" && params[:selected_date].present?
      @orders = Order.includes(:tags, :line_items, :events).where('lower(country) <> ?', 'india').where('orders.confirmed_at::date =?',params[:selected_date].to_date)
      @orders = Order.where(id: @orders.collect(&:id))
      @orders_count = @orders.length
      @orders = @orders.where('orders.items_received_on::date between ? AND ?',params[:selected_date].to_date, params[:selected_date].to_date + params[:days_passed].to_i.days )
      @orders_received_count = @orders.length  
    else
      @orders = Order.where(state: 'sane').where('lower(country) <> ?', 'india')
      if params[:order_type] == "All Items Received" 
        params[:order_type] = "All"
      end
      @orders = Order.includes(:tags, :line_items, :events).where(id: @orders.collect(&:id))
      if params[:selected_date].present?
        @orders = @orders.where('orders.confirmed_at<=?',params[:selected_date].to_date - params[:days_passed].to_i.days)
      else
        @orders = @orders.where('orders.confirmed_at<=?',params[:days_passed].to_i.days.ago)
      end
      @orders_count = @orders.length
      @orders_stitching = @orders.joins(:line_items).where('line_items.stitching_required =?', "Y").uniq
      if params[:order_type] == "Stitching Orders"
        @orders = @orders_stitching
      elsif params[:order_type] == "Non-Stitching Orders"
        @orders = @orders - @orders_stitching
      end
      @non_stitching_orders = @orders_count - @orders_stitching.count
    end
    @orders = @orders.uniq.paginate(page: params[:page], per_page: 50)
  end

  def report_list
    @integration_status = 'new'
  end

  def stitching_done_orders
    order_confirmed = 'orders.confirmed_at '+  (params[:sort].present? && %w(ASC DESC).include?(params[:sort]) ? params[:sort] : 'DESC')
    select_clause   = 'orders.id,name,email,total,number,confirmed_at,notes'
    @orders = Order.unscoped.select(select_clause).preload(:tags,:events,designer_orders: [:line_items,:rack_list]).where('created_at::date >= ?',1.month.ago.to_date).where(state: 'sane').where('other_details @> hstore(?,?)','stitching_done','true').order(order_confirmed).paginate(page: params[:page].presence, per_page: 30, total_entries: params[:total_entries])
  end

  def stitching_pending_orders
    @integration_status = 'new'
    @air,@remove_addon,@fnp,@product_code = params[:air],params[:remove_addon]=='1',params[:fnp] == '1',params[:product_code]
    measurement_received_condition = params[:measurement_received] == 'Yes' ? 'line_items.measuremnet_received_on IS NOT NULL' : 'line_items.measuremnet_received_on IS NULL' if params[:measurement_received].present?
    all_item_received_condition = @air == 'True' ? 'orders.items_received_status = true' : 'orders.items_received_status = false' if @air.present?
    having_condition = 'count(line_items.stitching_required) > count(line_items.stitching_sent)'
    having_condition += " AND position('addon' in string_agg(tags.name,',')) = 0 " if @remove_addon
    line_item_issue_condition = "(line_items.issue_status = 'N' AND LOWER(line_items.issue_resolve_message) = ?) OR line_items.issue_status IS NULL",'resolve by replacement'
    fnp_condition = "lower(tags.name) IN ('fnp')" if @fnp
    product_code_condition = "substring(designer_orders.rack_code from 1 for #{@product_code.length}) = ?", @product_code if @product_code.present?
    @orders = Order.unscoped.
              joins(:tags,designer_orders: :line_items).
              where("orders.state = ? and designer_orders.state IN (?) and orders.created_at::date > ? ",'sane', ['pending','dispatched','critical','completed'],6.months.ago.to_date).
              where(all_item_received_condition).
              where(fnp_condition).
              where(product_code_condition).
              where(line_item_issue_condition).
              where(measurement_received_condition).
              group('orders.id').
              having(having_condition).
              order('orders.confirmed_at ASC').
              preload(:tags,designer_orders: [:line_items,:rack_list]).
              paginate(page: params[:page], per_page: 40, total_entries: params[:total_entries])
  end

  def sent_stitching_list
    @integration_status = 'new'
    sla = params[:sla].present? ? params[:sla].to_i : 4
    slas = [4, 5, 6, sla]
    items = LineItem.unscoped.where("stitching_sent IS NOT NULL").where("stitching_done IS NULL")
    @stitching_sent = {}
    slas.each do |sla|
      key = "Sent #{sla} Days Ago"
      value = sla.days.ago.beginning_of_day()..sla.day.ago.end_of_day()
      @items = items.where(stitching_sent_on: value)
      @stitching_sent[key] = { count: items.where(stitching_sent_on: value).count, sla: sla }
    end
    @items = @items.includes(:designer_order => :order).paginate(page: params[:page], per_page: params[:items_per_page])
  end

  def upload_freshdesk
    @integration_status = 'new'
    if request.post? && params[:csv_file].present? && current_account.present?
      file_text = File.read(params[:csv_file].path)
      file_text = StringModify.string_utf8_clean_without_space(file_text) unless file_text.is_utf8?
      filedata = CSV.parse(file_text)
      Admin.sidekiq_delay(queue: 'low')
           .get_order_data(filedata,current_account.email)
      redirect_to :back, notice: 'File will be emailed to you shortly.'
    end
  end

  def not_sent_stitching_list
    @integration_status = 'new'
    sla = params[:sla].present? ? params[:sla].to_i : 1
    slas = [1, 2, 3, sla]
    items = LineItem.unscoped.where("measuremnet_confirmed IS NOT NULL").where("stitching_sent IS NULL")
    @measuremnet_confirmed = {}
    slas.each do |sla|
      key = "Not Sent #{sla} Day Ago"
      value = sla.days.ago.beginning_of_day()..sla.day.ago.end_of_day()
      @items = items.where(measuremnet_received_on: value)
      @measuremnet_confirmed[key] = { count: items.where(measuremnet_received_on: value).count, sla: sla }
    end
    @items = @items.includes(:designer_order => :order).paginate(page: params[:page], per_page: params[:items_per_page])
  end

  def optional_pickups
    if params[:optional_pickup].present?
      records_by_date = Date.parse(params[:optional_pickup][:date_select])
      @optional_pickups = OptionalPickup.includes(:designer,shipment: [:shipper,:order]).where(pickup_date: records_by_date.beginning_of_day..records_by_date.end_of_day).paginate(page: params[:page], per_page: 20)
    else
      @optional_pickups = OptionalPickup.includes(:designer,shipment: [:shipper,:order]).order('pickup_date DESC').paginate(page: params[:page], per_page: 20)
    end
  end

  def stitching_items
    @integration_status = 'new'
    @selects = []
    @selects << {form_key: :QC, attribute: :qc_done}
    @selects << {form_key: :FM, attribute: :fabric_measured_on}
    @selects << {form_key: :MC, attribute: :measuremnet_confirmed}
    @selects << {form_key: :SS, attribute: :stitching_sent}
    @selects << {form_key: :SR, attribute: :stitching_done}
    not_states = ['complete','completed', 'cancel_complete', 'canceled', 'dispatched', 'cancel', 'pickedup']
    order_ids = Order.where(items_received_status: true).
              where{state << not_states}.international_orders.pluck(:id)

    orders_by_tag = Order.where(id: order_ids).tagged_with('stitching').pluck('orders.id')

    item_ids_by_addon = LineItem.joins(:designer_order).
                where(designer_orders: {order_id: order_ids}).stitching.
                pluck('line_items.id')

    item_ids_by_tag = LineItem.where(stitching_required: 'Y').joins(:designer_order).
                 where(designer_orders: {order_id: orders_by_tag}).
                 pluck('line_items.id')

    item_ids = (item_ids_by_addon + item_ids_by_tag).uniq
    @items = LineItem.includes(designer_order: :order).where(id: item_ids)
    if params[:tags].present?
      orders_ids = Order.where(id: [order_ids + orders_by_tag]).tagged_with(params[:tags])
      items_ids = @items.joins(:designer_order).where(designer_orders: {order_id: orders_ids}).
                pluck('line_items.id')

      @items = LineItem.includes(designer_order: :order).where(id: items_ids)
    end
    @selects.each do |select|
      key = select[:form_key]
      attribute = select[:attribute]
      if params[key].present?
        if params[key] != '0'
          w_clause = [:FM].include?(key) ? "#{attribute} IS NOT NULL" :
          "#{attribute} = 'Y'"
        else
          w_clause = [:FM].include?(key) ? "#{attribute} IS NULL" :
          "#{attribute} <> 'Y' OR #{attribute} IS NULL"
        end
        @items = @items.where(w_clause)
      end
    end
    @items = @items.order('orders.confirmed_at desc, orders.number').
      paginate(page: params[:page], per_page: params[:items_per_page])
  end

  def upload_vendor_payouts
    if request.post?
      batch = VendorPayoutBatch.new(file_name: params[:file].original_filename)
      begin
        directory = Fog::Storage.new({
          provider: 'AWS',
          aws_access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
          aws_secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
          region: ENV.fetch('AWS_REGION')
        }).directories.new(key: ENV.fetch('S3_BUCKET'))        

        filename  = "VendorPayoutBatch/#{batch.id}/#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(2) +'.xlsx'}"
        # upload that file
        file =  directory.files.create(
          :key    => filename,
          :body   => params[:file].read,
          :public => true
        )
        batch.url = file.public_url
        batch.save!
      rescue NoMethodError
        error = params[:file].blank? ? 'CSV file not uploaded' : ''
        redirect_to :back, :notice => error
      rescue => error
        redirect_to :back, :notice => error.message
      end
      SidekiqDelayGenericJob.perform_async("#{batch.class}", batch.id, "upload_vendor_payouts", batch.url, current_account.email)
      #batch.sidekiq_delay
      #     .upload_vendor_payouts(batch.url, current_account.email)
      flash.now[:notice] = "Successfully Uploaded VendorPayoutsBatch No. #{batch.id} "
    end
  end

  def change_qty_of_designs
    wishlist_designs = Wishlist.where('created_at > ?', 1.month.ago).wished.where('design_id NOT IN (SELECT DISTINCT(design_id) FROM variants)')
                        .pluck(:design_id)
    cart_designs = LineItem.joins(:cart).where('app_source = ? AND carts.used = ? AND line_items.created_at > ?', 'Android', false, 1.month.ago)
                    .where('design_id NOT IN (SELECT DISTINCT(design_id) FROM variants)').pluck(:design_id)
    @wishlist_ids = wishlist_designs.flatten.inject(Hash.new(0)) {|h,i| h[i] += 1; h }.sort_by(&:last).reverse
    .paginate(:page => params[:page], :per_page => 30)
    @cart_ids = cart_designs.flatten.inject(Hash.new(0)) {|h,i| h[i] += 1; h }.sort_by(&:last).reverse
    .paginate(:page => params[:page], :per_page => 30)
    @modified_designs = Design.where('previous_qty > ?', 0).select([:id, :previous_qty])
    .paginate(:page => params[:page], :per_page => 30)
  end

  def change_qty_of_designs_update
    if params[:reset] && params[:design_ids].present?
      flash[:notice] = "Reset complete!" if reset_qty_of_designs?(params[:design_ids].split(/\r?\n|,/))
    elsif params[:new_qty].present? && (params[:new_qty].to_i) > 0 && params[:design_ids].present?
      new_qty = params[:new_qty].to_i
      design_ids = params[:design_ids].split(/\r?\n|,/)
      designs = Design.where(:id => design_ids)
      designs.each do |design|
        if design.quantity > new_qty
          prev_qty = design.previous_qty + design.quantity - new_qty
          design.update_attributes(quantity: new_qty, previous_qty: prev_qty)
        end
      end
      flash[:notice] = "Update Complete!"
    end
    redirect_to change_qty_of_designs_path
  end


  def designer_invoices
    @payout_versions = (3.month.ago.to_date..Date.today+3.months).map{ |date| date.strftime("%^b/%Y") }.uniq
    if request.xhr?
      invoice_id,payout_version = params[:designer_invoice_id],nil
      if invoice_id.present? && params[:commit].present?
        if params[:commit] == 'Approved'
          payout_version = params[:payout_version]
          payout_type = params[:payout_type]=="International" ? "INT/" : "DOM/"
          payout_version = "#{payout_type}#{payout_version}"
        end
        DesignerInvoice.change_invoice_status(invoice_id,params[:comment],params[:commit],payout_version)
        response = {status: params[:commit]}
        response[:payout] = "Payout : #{payout_version}" if params[:commit] == 'Approved'
      else
        response = {error: 'Invoice not found'}
      end
      render json: response
    elsif request.post?
      if params[:invoices].present?
        invoice_id = params[:invoices]
        payout_version = "INT/#{@payout_versions[3]}"
        commit = params[:commit] == 'Approve All' ? 'Approved' : (params[:commit] == 'Reject All' ? 'Rejected' : nil)
        DesignerInvoice.sidekiq_delay(queue: 'high')
                       .change_invoice_status(
                         invoice_id,params[:reject_all_comment],
                         commit,payout_version
                       )
        notice = 'Invoices will reflect changes within 5 minutes.'
      else
        notice = 'Please select atleast one invoice !'
      end
      redirect_to :back, :notice => notice
    else
      start_date       = Date.parse(params[:start_date]) if params[:start_date].present?
      end_date         = Date.parse(params[:end_date]) if params[:end_date].present?
      if params[:commit] == "Download report"
        DesignerInvoice.sidekiq_delay(queue: 'high')
                       .download_designer_invoice_report(
                         params[:invoice_status],
                         params[:designer_id],
                         params[:order_number],
                         start_date,end_date,
                         current_account.email
                       )
        redirect_to :back, notice: 'Report would be emailed to your email id.'
      else
        @invoices = DesignerInvoice.get_invoices(params[:invoice_status],params[:designer_id],params[:order_number],start_date,end_date).preload(:designer,:payment_order).paginate(:page => params[:page], :per_page => 30)
      end
    end
  rescue => e
    if request.xhr?
      render json: {error: e.message}
    else
      redirect_to request.referer.present? ? :back : admin_designer_invoices_path, notice: e.message
    end
  end
  
  def report_page_shipment_time
    shipper_req = Shipper.select('name,id').where('lower(name) IN (?)', SHIPPER_LIST_INTERNATIONAL.map(&:downcase))
    @shipper_detail= {}
    shipper_req.each do |shipper|
     @shipper_detail[shipper.id] = shipper.name
    end
    if params[:report_select].to_s == "Month"
      start_date = Date.parse("#{params[:date][:month]}/#{params[:year_select]}")
      end_date = start_date.end_of_month
      shipments_time = Shipment.unscoped.joins(:order).where('shipments.created_at BETWEEN ? AND ?',start_date,end_date).where(shipments: {shipper_id: @shipper_detail.keys}).where('shipments.delivered_on is not null and shipments.shipment_state = ?','delivered').group('lower(country)','shipper_id').average("delivered_on::date - shipments.created_at::date")
    else
      shipments_time = Shipment.unscoped.joins(:order).where(shipments: {shipper_id: @shipper_detail.keys}).where('shipments.delivered_on is not null and shipments.shipment_state = ? and shipments.created_at > ?','delivered', 1.year.ago.to_date).group('lower(country)','shipper_id').average("delivered_on::date - shipments.created_at::date")
    end
    @country_wise_report= {}
    shipments_time.each do |key,value|
      (@country_wise_report[key[0]]||={})[key[1]]=value.to_f.round(2)
    end
    @country_wise_report.delete('india')
  end

  def enable_skip_qc_for_designs
    if request.post? && params[:design_ids].present?
      design_ids = params[:design_ids].split(',').map(&:strip)
      invalid_ids = []
      design_ids.each do |id|
        unless (/^\d+$/).match(id)
          invalid_ids << id
          design_ids = design_ids - [id]
        end
      end
      valid_ids = Design.where('id in (?)',design_ids).pluck(:id)
      invalid_ids += (design_ids - valid_ids.map(&:to_s))
      notice = ''
      if params[:qc] == 'Skip QC'
        Design.where(id: valid_ids).update_all(skip_qc: true)
        notice = 'Entered Design Ids skipped for Quanlity Check process.'
        notice = "#{valid_ids} skipped for quality check process." if valid_ids.present? && valid_ids.length < 200
        notice += "<br> #{invalid_ids} design ids not found." if invalid_ids.present?
        flash[:notice] = notice.html_safe
      else
        Design.where(id: valid_ids).update_all(skip_qc: false)
        notice = 'Entered Design Ids enabled for Quanlity Check process.'
        notice = "#{valid_ids} enabled for quality check process" if valid_ids.present? && valid_ids.length < 200
        notice += "<br> #{invalid_ids} design ids not found." if invalid_ids.present?
        flash[:notice] = notice.html_safe
      end
      redirect_to admin_enable_skip_qc_path
    end
  end

  def mark_order_urgent
    if params[:order_id].present?
      order = Order.where(id: params[:order_id]).first
      order.add_tags_skip_callback('urgent')
      order.add_notes_without_callback("Marked Urgent",'dispatch',current_account)
      render json: {notice: "Order Marked As Urgent"}
    else
      render json: {notice: "Order not present"}
    end
  end

  def check_for_unique_transaction_id
    orders = Order.select('number').where("#{params[:attribute_name]} = ?",params[:txn_id].to_s)
    count = orders.length
    if count == 1
      status = orders.first.number == params[:order_number] ? 'OK' : 'REPEAT'
    else  
      status = count == 0 ? 'OK' : 'REPEAT'
    end
    render json: {status: status}
  end

  def get_transaction_id_for_duplicate_order
    source_order_number =  params[:source_order_number]
    order = Order.where(number: source_order_number.strip.upcase).first
    if order.present?
      duplicate_orders = Order.find_child_duplicate_orders(order)
      if duplicate_orders.include?(params[:order_number])
        payment_details = order.get_payment_transaction_id
        txn_attr = payment_details.keys.first
        if txn_attr != 'None'
          transaction_attribute = "#{payment_details.keys.first}::#{payment_details.values.first}"
          status = 'OK'
        else 
          status = 'FAULT' 
          error_msg = 'Check Payment Gateway details'
        end
      else
        status = 'FAULT'
        error_msg = 'Wrong Source Order'
      end  
    else
      status = 'FAULT'
      error_msg = 'Order Not Present'
    end
    render json: {status: status,errors: error_msg,transaction_id: transaction_attribute, parent_order_id: order.id}
  end

  def adjustment_creation_panel
    if params[:adjustment_csv_file].present?
      begin
        filename  = "payout/adjustments/#{Time.now.strftime('%m_%d_%y')}_#{SecureRandom.hex(6)}_#{current_account.name}.csv"
        AwsOperations.create_aws_file(filename,params[:adjustment_csv_file])
        PayoutManagement
          .sidekiq_delay(queue: 'low')
          .generate_adjustment_from_csv(filename,current_account.email)
        redirect_to :back, notice: 'Adjustment Creation is scheduled'
      rescue NoMethodError => error
        error = params[:adjustment_csv_file].blank? ? 'CSV file not uploaded' : error.message
        redirect_to :back, notice: error
      rescue => error
        redirect_to :back, notice: error.message
      end
    end
  end   

  def get_notes_bifurcated
    if params[:order_id].present?
      order = Order.where(id: params[:order_id]).first
      render json: {notice: order.get_notes_by_department(params[:department])}
    else
      render json: {error: 'Order not found'}
    end
  end

  def mark_ready_for_dispatch
    Order.sidekiq_delay(queue: 'high').mark_orders_ready_for_dispatch
    render :json => {:message => 'Task initiated to change order state.'}
  end

  def sales_register
    @integration_status = 'new'
    @headers = ['Index','Sales state','Event Date','Event','Event Amount','Number','Created At','Name','Email','Address','Country','Phone','Pay Type','Currency Rate','Market Rate','Paid (INR)','Prev. Shipping Cost','Order state','Prev. Total (INR)','Addon Charges','Effective Designer order id','Prev. Designer Order Total','Effective Line Item Cost (INR)','Effective Total (FRC)','Amount INR','Trans. ID','Gateway']
    @order_data = [:created_at,:name,:email,:street,:country,:phone,:pay_type,:currency_rate,:currency_rate_market_value,:paid_amount]
    @snapshot_values = ['order_shipping','order_state','order_total','order_mirraw_addon_charges','designer_order_id','designer_order_total','line_item_effective_price']
    if (@order_number = params[:order_number]).present?  
      order = Order.find_by_number(@order_number)
      @sales_records = order.present? ? SalesRegister.where(order_id: order.id).order('created_at').preload(:order) : nil
    else
      @region = params[:region].presence || 'International'
      orders_region = @region == 'International' ? "LOWER(orders.country) <> 'india'": "LOWER(orders.country) = 'india'"
      @start_date =  params[:sales_start_date].present? ? (DateTime.parse(params[:sales_start_date]).to_date.beginning_of_day) : (DateTime.now).to_date.beginning_of_day
      @end_date = params[:sales_end_date].present? ? (DateTime.parse(params[:sales_end_date]).to_date.end_of_day)  : (DateTime.now).to_date.end_of_day
      @sales_records = SalesRegister.joins(:order).where('sales_registers.created_at BETWEEN ? and ?',@start_date,@end_date ).where(orders_region).order('sales_registers.created_at DESC').preload(:order)
    end
    if params[:commit] == 'Download Report' 
      file = SalesRegister.download_sales_register_report(@sales_records,@headers,@order_data,@snapshot_values) 
      send_data file, filename: "sales_register_#{@start_date}_to_#{@end_date}.csv" if file.present?
    end
    @sales_records = @sales_records.paginate(page: params[:page],per_page: 50) if @sales_records.present?
  end

  def payout_management
    @integration_status = 'new'
    is_weekly = params[:weekly] == 'weekly'
    @payout_versions = if is_weekly
      (3.month.ago.to_date..Date.today+3.months).map{|i| [i.cweek,i]}.uniq{|i| i[0]}.map{|k,i| i.strftime("#{k}/%^b/%Y")}
    else
      (3.month.ago.to_date..Date.today+3.months).map{ |date| date.strftime("%^b/%Y") }.uniq
    end
    @all_versions = PayoutManagement.order('created_at DESC').pluck(:payout_version).uniq.first(25)
    if request.post?
      if params[:commit] == 'Generate Payout'
        freeze_payout = params[:freeze_payout]
        start_date = params[:payout_start_date]
        end_date = params[:payout_end_date]
        next_payout_version = params[:next_payout_version]
        payout_type = params[:payout_type]=="International" ? "INT/" : "DOM/"
        next_payout_version = "#{payout_type}#{next_payout_version}"
        duplicate_payout_version = PayoutManagement.where(payout_version:next_payout_version ).count > 0 ? true : false
        notice = "DUPLICATE PAYOUT VERSION : PLEASE SELECT NEW VERSION"
        unless duplicate_payout_version
          @pending_payouts = 
            PayoutManagement.sidekiq_delay(queue: 'low')
                            .calculate_pending_payouts_international(
                              start_date, end_date,current_account.email,
                              next_payout_version,payout_type,
                              freeze_payout, is_weekly
                            )
          notice = "Payout Calculation In Process. File will be mailed to #{current_account.email} ..."
        end
        redirect_to :back,notice: notice
      elsif params[:commit] == 'Create Banksheet'
        payout_version = params[:payout_version]
        payout_designer_ids = params[:payout_numbers].values
        PayoutManagement.sidekiq_delay(queue: 'low')
                        .create_payout_banksheet(
                          payout_version,payout_designer_ids,
                          current_account.email, 
                          false
                        )
        redirect_to :back,notice: "Banksheet will be emailed shortly...."
      elsif params[:commit] == 'Get Payout Details'
        @last_payout_version = params[:payout_version]
        @last_payout_calculated = PayoutManagement.where(payout_version: @last_payout_version).preload(:designer)
        @last_payout = @last_payout_calculated.last
      end      
    end
  end

  def payout_management_save_records
    if params[:save_payout_csv_file].present?
      records_to_enter = []
      records_to_enter = CSV.read(params[:save_payout_csv_file].path)
      records_to_enter.delete_at(0) 
      PayoutManagement
        .sidekiq_delay(queue: 'low')
        .create_new_records(records_to_enter,current_account.email)
      redirect_to :back, notice: "Approved Payouts Creation Is Intialized ..."
    end 
  end

  def payout_management_update_paid_records
    if params[:paid_payout_csv].present?
      filename = "/paid_payout_details/#{params[:paid_payout_csv].original_filename}_#{SecureRandom.hex(6)}"
      AwsOperations.create_aws_file(filename,params[:paid_payout_csv])
      PayoutManagement
        .sidekiq_delay(queue: 'low')
        .update_paid_payout_details(filename,current_account.email)
      redirect_to :back,notice: "Payout Updating In Process...."
    end
  end

  def catalog_update
    if request.post?
      if params[:catalog_update_csv].present? && params[:catalog_update_csv].content_type.include?('csv')
        filename = "design_catalog_update/#{SecureRandom.hex(6)}_#{params[:catalog_update_csv].original_filename}"
        AwsOperations.create_aws_file(filename,params[:catalog_update_csv], false)
        Design.sidekiq_delay(queue: 'high')
              .update_catalog(element: filename, email: current_account.email)
        notice = 'Design Updating In Process....'
      elsif params[:designer_id].present?
        designers_id = params[:designer_id].split(/\s*,\s*/)
        if Designer.where(id: designers_id).count == designers_id.size
          Design.sidekiq_delay(queue: 'high')
                .update_catalog(
                  element: designers_id,
                  email: current_account.email,
                  catalog: params[:catalog]
                )
          notice = 'Designer Updating In Process....'
        else
          notice = 'Please check the input one or more designer is not exist!'
        end
      else
        notice = 'The file has not been uploaded properly please, try again.'
      end
      redirect_to :back,notice: notice
    end
  end

  def localytics_push
    @scheduled_jobs = Delayed::Job.where("handler LIKE '%method_name: :send_audience_notification%'")
    @audience_ids = SystemConstant.get('AUDIENCE_IDS')
    gon.audience_ids = @audience_ids
  end

  def push_data_to_localytics
    notice = 'Notification Scheduled!'
    if params[:type].strip.present? && params[:image].strip.present? && params[:title].strip.present? && params[:main_title].strip.present? && params[:key].strip.present? && params[:body].strip.present? && params[:value].strip.present? && params[:campaign_key].strip.present? && params[:datetime].present? && params[:datetime] > Time.zone.now
      AdminController
        .sidekiq_delay_until(Time.zone.parse(params[:datetime]))
        .send_audience_notification(params) && false
    else
      notice = 'Please Fill All Fields Properly and Try Again.'
    end
    flash[:notice] = notice
    redirect_to admin_localytics_push_path
  rescue
    flash[:notice] = 'Input Invalid, Please Try Again.'
    redirect_to admin_localytics_push_path
  end

  def stitching_priority_list
    @integration_status = 'new'
    if request.post?
      Admin.sidekiq_delay(queue: 'critical')
           .generate_stitching_priority_list(
             current_account.email,
             params[:count]
           )
      redirect_to :back, notice: "Priority List will be mailed to #{current_account.email}"
    end
  end

  def app_rank_details
    @integration_status = 'new'
    show_status = ACCESSIBLE_EMAIL_ID['scraper_portal_access'].include?(current_account.email)
    redirect_to root_path, notice: 'You do not have access to this page' unless show_status
    keyword_limit_const = SystemConstant.where(name: 'APP_RANK_SEARCH_KEYWORD').first
    keyword_limit_const.update_attribute(:value, '200') if keyword_limit_const.updated_at.to_date != Date.today
    get_app_rank = params[:commit] == 'Get App Rank'
    @keyword_limit = keyword_limit_const.value.to_i
    @batches =  AppRankingKeyword.pluck(:ranking_batch).uniq
    @mirraw_apps = Mirrawapp.all
    if params[:keywords_file].present? && get_app_rank
      keywords = []
      keywords = CSV.read(params[:keywords_file].path)
      options = {email: current_account.email,project: "designers_collection",spider: "appstore_rank", region: 'US'.presence || 'US', batch_code: DateTime.current.strftime("BATCH-%d-%b-%y-%H-%M")}
      keywords = keywords[0..@keyword_limit] if @keyword_limit < keywords.length && get_app_rank
      AppRankingKeyword
        .sidekiq_delay
        .send_app_rank_crawler_request(keywords,options,get_app_rank,nil)
      redirect_to :back, notice: 'Request Sent For Fetching Data' 
    elsif params[:commit] == 'Download Report' && params[:batches].present?
      AppRankingKeyword
        .sidekiq_delay.download_report(current_account.email,params[:batches])
    end 
  end

  def app_rank_batchwise
    batch = params[:ranking_batch]
    keywords =  AppRanking.includes(:mirrawapp,:app_ranking_keyword).where(mirrawapp_id: params[:app_id]).where('app_ranking_keywords.autosuggest_parent_id is null').order('app_rankings.created_at desc').references(:mirrawapp,:app_ranking_keyword)
    app_data = []
    app_ranks = {}
    keywords.group_by{|key| key.app_ranking_keyword.keyword}.each do |k,v| 
      v.each.with_index do |a,index| 
        (app_ranks[k]||={})[a.app_ranking_keyword.ranking_batch] = a.ranks['US'].to_i
      end
    end
    app_ranks.map{|k,v| app_data << {name: k,data: v}}
    render json: app_data.first(5)
  end

  def send_email
    body_text = params[:cktext_body].try(:first)
    attachments = {}
    max_size,attachment_size = 20.megabytes, 0
    to_emails = []
    to_emails = params[:to_type] == 'Customer' ? params[:customer_emails] : params[:designer_emails]
    if params[:attachment].present? && params[:attachment][:file].present?
      params[:attachment][:file].each do |f| 
        attachments[f.original_filename] = f.read
        attachment_size += f.size
      end
    end
    if to_emails.present? && params[:from].present? && body_text.present? && attachment_size <=  max_size
      emails = {'to_email'=> to_emails.join(','),'from_email_with_name'=> params[:from]} 
      emails['cc_email'] = params[:cc_email].present? ? "#{params[:cc_email]},#{current_account.email}" : current_account.email
      OrderMailer.sidekiq_delay
                 .report_mailer(params[:subject],body_text,emails,attachments)
      order = Order.find_by_number(params[:order_number])
      order.add_notes_without_callback("Sent Mail to #{params[:to_type]} : #{params[:subject]}", 'support',current_account) if order.present?
      notice_text = 'Mail Sent Successfully.'
    else
      notice_text = 'Mail Could Not Be Sent.'
    end
    redirect_to :back,notice: notice_text
  end

  def vendor_performance
    duration       = params[:time_period].to_i > 0 ? params[:time_period].to_i : 30
    if params[:designer_id].present?
      @designer   = Designer.find_by_id params[:designer_id]
      @designs_with_defect = LineItem.select("design_id,COUNT(scope_events.id) as count,string_agg(distinct scope_events.name,' , ') as events").preload(design: [:reviews,:images]).joins(:designer_order,:scope_events).where(designer_orders: {designer_id: @designer.id}).where('designer_orders.state IN (?) and designer_orders.confirmed_at between ? and ?',['dispatched','completed','buyer_returned','pickedup'],60.days.ago.beginning_of_day,10.days.ago.end_of_day).group(:design_id).limit(10).sort_by{|l| l.count}.reverse!
      @metric_definitions = MetricDefinition.where(visible_to_admin: true,duration: duration)
      @total_orders = DesignerOrder.unscoped.where(designer_id: @designer.id).where(confirmed_at: 1.month.ago.beginning_of_day..Date.today.end_of_day).group(:state).count
    else
      @params        = params

      if params[:commit] == 'Download Report' && current_account.present?
        metrics = JSON.parse(params['metrics']);
        rating = Admin.get_parameter_query('designers.average_rating',params[:designer_min_rating],params[:designer_max_rating])
        sales = Admin.get_parameter_query('metric_values.denominator_count',params[:designer_min_sales],params[:designer_max_sales])

        Admin.sidekiq_delay(queue: 'critical')
             .download_vendor_report(
               duration,
               metrics,
               rating,
               sales,
               current_account.email
             )
        @notice = 'Report would be sent to your email address.'
      else
        @metrics     = Hash[MetricDefinition.select('name,id').where(visible_to_admin: true,duration: duration).order(:id).map{|m| [m.id,m.name]}]
        rates        = @metrics.values

        @designers   = Rails.cache.fetch("designers_name_id_and_rating", expires_in: 6.hours) do
          Hash[Designer.select('id,name,average_rating,cached_slug').map{|d| [d.id,d]}]
        end
        unless Rails.cache.exist?("vendor_perf_#{duration}")
          designer_ids = DesignerOrder.unscoped.where('state <> ?','new').where{pickup >= (duration + 15).days.ago.beginning_of_day}.uniq.pluck(:designer_id)
          @all_designer_metrics = MetricValue.get_all_designers_metrics(designer_ids,rates,duration)
        end
        @top_selling_designers = Rails.cache.fetch('top_sell_designers_name_orders',expires_in: 6.hours) do
          Hash[DesignerOrder.unscoped.select('designer_orders.designer_id,COUNT(distinct designer_orders.id) as count').where(state: ['dispatched','completed','buyer_returned','rto']).where{pickup >= duration.days.ago.beginning_of_day}.group('designer_orders.designer_id').order('2 desc').limit(20).map{|d| [d.designer_id,d.count]}]
        end
        @top_designer_metrics = Rails.cache.fetch("top_selling_designers_#{duration}",expires_in: 6.hours) do
          MetricValue.get_all_designers_metrics(@top_selling_designers.keys,rates,duration)
        end
      end
    end
    respond_to do |format|
      request.xhr? ? format.js : format.html
    end
  end

  def compare_designer_performance
    if params[:designers].present?
      @metrics_definitions = MetricDefinition.where(id: params[:metric_id])
      @metric_values = promise {@metrics_definitions.first.metric_values.where(actor_id: params[:designers].keys,actor_type: 'Designer').where(generated_on: (@metrics_definitions.first.duration || 30).days.ago.to_date..Date.today).select('actor_id,generated_on,value').order(:generated_on).group_by(&:actor_id).map{|actor_id,metric_value| {name: params[:designers][actor_id.to_s],data: metric_value.map{|m| [m.generated_on,m.value]}}}}
      respond_to do |format|
        format.json {render json: @metric_values}
      end
    end
  end

  def duplicate_inventory
    @designers = designer_list_w_all
    @designers = (@designers - ['All','All']).to_h
    @design_clusters = DesignCluster.preload(in_stock_winner: [show_cluster_design: :master_img], other_clusters_order_wise: [show_cluster_design: [:categories, :master_img, property_values: :property]])
    if params[:design_id].present?
      @design_clusters = @design_clusters.where(design_id: params[:design_id]).paginate(page: params[:page], per_page: 20)
      @count_of_clusters = DesignCluster.where(cluster_id: @design_clusters.collect(&:cluster_id).compact).group(:cluster_id).count
    elsif params[:cluster_id].present?
      @design_clusters = @design_clusters.select('cluster_id, count(*) as count_all').where(cluster_id: params[:cluster_id]).group(:cluster_id).paginate(page: params[:page], per_page: 10)
    elsif params[:designer_id].present?
      @design_clusters = @design_clusters.joins(:design).select('cluster_id, count(*) as count_all').where(designs: {designer_id: (@designers.key(params[:designer_id].try(:downcase)).presence || params[:designer_id])}).group(:cluster_id).paginate(page: params[:page], per_page: 10)
      @count_of_clusters = DesignCluster.where(cluster_id: @design_clusters.collect(&:cluster_id).compact).group(:cluster_id).count
    else
      @total_duplicate_inventory = Rails.cache.fetch(:cluster_report_designable_wise,expires_in: 1.day) do
        DesignCluster.select('designable_type, count(distinct(design_clusters.cluster_id)) as clusters, count(design_clusters.design_id) as designs').joins(:design).group('designs.designable_type')
      end
      @design_clusters = DesignCluster.where(state_multiplier: 1).paginate(page: params[:page], per_page: 50)
    end
  end

  def schedule_for_dedup
    notice = 'Please upload file in correct format !'
    if params[:data_file].present?
      file_text = File.read(params[:data_file].path)
      design_winner_hash = {}
      CSV.parse(file_text, {headers: true}) do |row|
        design_winner_hash[row[0].to_i] = (row[1].to_i.nonzero? || nil) if row[0].to_i > 0
      end
      if design_winner_hash.present?
        DesignCluster.sidekiq_delay(queue: 'critical').create_clusters({design_id: design_winner_hash.keys.push(*design_winner_hash.values.compact).uniq}, design_winner_hash)
        notice = 'Designs will be processed and notified on current email shortly !'
      end
    end
    redirect_to duplicate_inventory_path, notice: notice
  end

  def duplicate_inventory_csv_download
    request = {
      name: "Download Cluster Report",
      method: 'download_cluster_report',
      params: [params[:cluster_id]],
      file_type: 'csv',
      class: 'DesignCluster',
      account: {current_account.class.to_s => current_account.id, "is_object?" => true}
    }
    render json: DelayedFile.create_delayed_file(request)
  end

  def designer_duplicate_inventory
    if request.xhr? && params[:method_type].to_sym == :similar
      current_design = Design.find_by_id(params[:design_id])
      @current_design_id = current_design.id
      @similar_image_results = current_design.get_similar_from_mongo(designer_id: current_design.designer_id)
      #@similar_image_results = @similar_image_results.find_all{|design| design.designer_id == current_design.designer_id}
      @similar_image_results.push *current_design
      @similar_image_results.sort_by!(&:created_at)
      render file: '/admin/duplicate_design', formats: :js, content_type: 'text/javascript'
    elsif request.post? && params[:method_type].to_sym == :remove
      response = {status: 403, message: 'Could not be Update'}
      if params[:design_ids].is_a?(Array)
        Design.where(id: params[:design_ids]).update_all(state:'reject',reject_on: "#{Time.now.to_s}", notes: Design.find_by_id(params[:design_ids]).try(:notes).to_s+'Duplicate_inventory')
        response = {status: 200, message: 'successfully rejected!'}
      end
      render json: response
    elsif (@designer = Designer.find(params[:method_type])).present?
      @elements = @designer.designs.published.where('similar_count > 0').order('similar_count DESC').paginate(page: params[:page], per_page: 10)
      render 'duplicate_inventory'
    end
  end

  def cost_estimation
    start_date = params[:start_date_estimation]
    end_date   = params[:end_date_estimation]
    redirect_to shipments_add_payment_details_path, notice: 'Difference between start date and end date should be less than month' and return unless start_date.to_date + 1.month >= end_date.to_date  && start_date <= end_date
    Order.sidekiq_delay.get_order_estimation_report(start_date, end_date)
    redirect_to shipments_add_payment_details_path,notice: 'Report generation in process'
  end

  def generate_irn_commercial_invoice
    if params[:report_type] == 'Commission Report'
      start_date = params[:start_date_report]
      end_date   = params[:end_date_report]
      CommissionIrnJob.sidekiq_delay.perform(start_date,end_date)
      redirect_to shipments_add_payment_details_path,notice: 'IRN Generation in progress,please wait for few minutes.......'
    else
      redirect_to shipments_add_payment_details_path,notice: 'Report type is blank'
    end     
  end


  def update_cost_estimation_report
    file = "update_cost_estimation/#{Date.current.strftime('%Y%m%d%H%M%S')}.csv"
    AwsOperations.create_aws_file(file, params[:csv_file])
    Order.sidekiq_delay(queue:'critical')
         .update_cost_estimation_report(
           file, params[:type],
           current_account.email
         )
    redirect_to shipments_add_payment_details_path,notice: 'Report generation in process'
  end

  def review_tailoring_feedback
    if request.xhr?
      if review = Review.where(id: params[:review_id]).first
        if params[:review_action] == 'accept'
          review.update_columns(approved_for_tailor: true, modified_review: params[:comment].strip)
        else
          review.update_column(:approved_for_tailor, false)
        end
        render json: {status: 200}
      else
        render json: {error: 'review not found'}
      end
    else
      select_clause = "distinct reviews.*, orders.number as order_number, string_agg(tailors.name,' | ') as tailor_name, string_agg(tailoring_infos.tailoring_material, ' | ') as tailoring_material"
      @reviews = Review.select(select_clause).joins(survey_answers: :survey_question, order: [line_items: [tailoring_info: :tailor]]).where(survey_questions: {id: [9,10]}).where('reviews.design_id = line_items.design_id and reviews.approved_for_tailor is null').group('reviews.id, survey_questions.id, survey_answers.id, orders.id, line_items.id').preload([design: :images], :designer).order(created_at: :desc).paginate(page: params[:page], per_page: 20)
    end
  end

  def invalid_address
    @orders = Order.unscoped.joins(:tags).where('tags.name = ?', "Invalid Address").where('orders.country <> ?', 'India').
    paginate(page: params[:page],per_page: 50)
  end

  def update_user_phone
    if current_account && current_account.admin?
      @user_account = User.find_by_id(params['user_id']).try(:account)
      @user_account.assign_attributes(dial_code: params['user_dial_code'], phone: params['user_phone'])
      if @user_account.changed?
        @user_account.save
        Account.sidekiq_delay(queue: 'high')
               .send_pwd_reset_link_via_sms(@user_account.id) if @user_account.errors.blank? && params['send_reset_password_sms'] == '1'
      end
      respond_to do |format|
        format.js { render file: '/admin/update_user_phone.js', :content_type => 'text/javascript'}
      end
    end
  end

  private
  ## include actions which layouts to be rendered in admin layout.
  def action_array
    ['designer_reports', 'designers_summary', 'inventory', 'domestic_dispatch_report', 'international_dispatch_report', 'designer_issues', 'add_designs_to_collection', 'add_designs_to_category','add_designs_to_featured_products','canceled', 'fb_publish_new', 'stats', 'pending_orders', 'ready_for_dispatch', 'campaign_stats','payout_management','ipending_list','stitching_pending_orders','rtv_pending', 'localytics_push','report','coupon_dashboard', 'designers', 'designers_show', 'designers_edit', 'designers_update','category_stats', 'category_subreports','sales_register','rack_check_page','upload_freshdesk','potential_order_dispatch','tailoring_info_page','addons_for_order','app_rank_details','vendor_performance','view_claim_requests','duplicate_inventory','designer_duplicate_inventory','catalog_update','sales_pending','shipment_bill_no', 'cost_estimation', 'designer_invoices','stitching_done_orders', 'review_designer_batches', 'designs_under_review', 'tags_stats', 'download_bulk_edit', 'upload_vendor_payouts','review_tailoring_feedback','inventory_by_order','availability_update_new','manage_pages','report_list','not_sent_stitching_list','sent_stitching_list','upload_designs_size_chart','promotions','invalid_address', 'get_tally_report', 'logistic_info_page','cache_clear','enable_design_video']
  end

  def coupon_params
    params.require(:coupon).permit(:name,:code,:start_date,:end_date,:flat_off,:percent_off,:limit,:advertise,:min_amount,:designer_id,:coupon_type,:use_count,:use_count, :coupon_reason, :notified)
  end

  def determine_layout
    action_name == "designer_report" ? "seller" : ( action_array.include?(action_name) ? "admin" : "application")
  end

  def designer_list_w_all(all_option:true,filter_designers:false)
    cache_key = filter_designers ? :all_filtered_designer_names_with_id : :all_designer_names_with_id
    if current_account.remote_vendor? && current_account.accountable.designers.present? 
      return current_account.accountable.designers.where('name is not null').order(:name).pluck(:id,'lower(name)').prepend(['All','All'])
    end
    designers = Rails.cache.fetch(cache_key,expires_in: 1.day) do
      query = Designer.where('name is not null').order(:name)  
      query = query.ready_for_review if filter_designers
      query.pluck(:id,'lower(name)').prepend(['All','All'])
    end
  end

  def issue_list_w_all(all_option = true)
    issue = DesignerIssue.select('issue_type,id').order(:issue_type).map{|i|[i.issue_type.camelize,i.id]}.uniq
    issue = issue.prepend(['All','All']) if all_option == true
  end

  def multi_design_state_change(designs_id, state_change, notes)
    values = if state_change == 'approved'
      {state: 'sold_out', last_sold_out: Time.now}
    elsif state_change == 'delete' && current_account.admin?
      Design.where(id: designs_id).update_all(quantity: 0)
      Variant.where(id: designs_id).update_all(quantity: 0)
      {state: 'delete_by_mirraw', deleted_on: Time.now}
    elsif state_change == 'reject'
      {state: 'reject', reject_on: Time.now}
    end
    Design.where(id: designs_id).update_all(values)
    DesignersController
      .sidekiq_delay
      .reindex_discontinued_designs(nil, designs_id, state_change, notes)
  end

  def get_design_ids
    design_ids = params[:design_ids].split(/\r?\n|,/)
    design_ids.reject!{|id| id.empty?}
    designs = {invalid_designs: [], design_ids: []}
    if design_ids.present?
      design_ids.each do |design|
        if ((design.strip =~ /^\d+$/) == 0)
          designs[:design_ids] << design if Design.where(id: design).first.present?
        elsif design.strip.include?('www')
          ## Fetching design cached_slug from design url to find design_id
          /.*\/designers\/(.*)\/designs\/(.*)/.match(design.strip)
          if (valid_design = Design.where(cached_slug: $2).first).present?
            designs[:design_ids] << valid_design.id
          else
            designs[:invalid_designs] << design
          end
        else
          designs[:invalid_designs] << design
        end
      end
    end
    designs
  end

  def get_campaign_stats(orders)
    medium_names, campaign_names = [], []
    campaign_hash = Hash.new
    orders.each do |order|
      order_multichannel = order.multi_channel_marketing.split('-----').last(2)
      order_medium = order_multichannel.first
      order_campaign = order_multichannel.last
      if order_medium.include?('Medium:') && order_campaign.include?('Campaign:')
        order_medium_list = order_medium.slice!("Medium: ")
        order_medium_name = order_medium.split(',')

        order_campaign_list = order_campaign.slice!("Campaign: ")
        order_campaign_name = order_campaign.split(',')

        if params[:campaign_name].present? #Data for single campaign
          campaign_hash[order.number] = order_campaign_name
        else
          campaign_names << order_campaign_name
        end
        medium_names << order_medium_name
        campaign_hash_temp = Hash.new
        order_campaign_name.zip(order_medium_name).each do |campaign, medium|
          campaign_hash_temp[campaign] = medium
        end
        campaign_hash[order.number] = campaign_hash_temp
      end
    end
    campaign_names = campaign_names.flatten.uniq
    medium_names = medium_names.flatten.uniq
    campaign_names.push(* params[:campaign_name]) #Data for single campaign
    campaign_details = Hash.new
    campaign_names.each do |campaign_name|
      domestic_orders_revenue, int_orders_revenue, dom_count, int_count= 0,0,0,0
      campaign_orders, campaign_medium = [], []
      orders.each do |order|
        if campaign_hash[order.number].present? && campaign_hash[order.number].keys.include?(campaign_name)
          if order.billing_country.downcase == 'india'
            domestic_orders_revenue += order.total
            dom_count +=1
          else
            int_orders_revenue += order.total
            int_count += 1
          end
          campaign_orders.push(order.number)
          campaign_medium.push(campaign_hash[order.number][campaign_name])
        end
      end
      total_revenue = domestic_orders_revenue + int_orders_revenue
      total_orders_count = dom_count + int_count
      campaign_details[campaign_name] = {
        total_revenue: total_revenue,
        total_orders_count: total_orders_count,
        domestic_orders_revenue: domestic_orders_revenue,
        domestic_orders_count: dom_count,
        int_orders_revenue: int_orders_revenue,
        int_orders_count: int_count,
        order_numbers: campaign_orders,
        campaign_mediums: campaign_medium
      }
    end
    return campaign_details, medium_names
  end

  def reset_qty_of_designs?(design_ids)
    designs = Design.where(:id => design_ids)
    designs.each do |design|
      new_qty = design.previous_qty + design.quantity
      design.update_attributes(quantity: new_qty, previous_qty: 0)
    end
    true
  end

  def self.send_audience_notification(params)
    LocalyticsNotification.audience_notification(
      [] << get_payload(params),
      params[:campaign_key].strip,
      params[:app_source] == 'Android' ? params[:app_source] : "Android-#{params[:app_source]}"
    )
  end

  def self.get_payload(params)
    {
      target: params[:audience].to_i,
      alert: get_push_msg(params).stringify_keys,
      android: {extra: get_push_extra(params).stringify_keys}.stringify_keys
    }
  end

  def self.get_push_msg(params)
    {
      title: params[:title],
      body: params[:body]
    }
  end

  def self.get_push_extra(params)
    {
      bundleFrom: "localytics",
      type: params[:type],
      PushImageHandler: params[:image],
      key: params[:key],
      value: params[:value],
      notificationMainTitle: params[:main_title],
      title: params[:title],
      ignore_threshold: params[:ignore_threshold]
    }
  end
end
