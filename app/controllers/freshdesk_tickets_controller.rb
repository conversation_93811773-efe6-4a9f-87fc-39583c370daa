class FreshdeskTicketsController < ApplicationController
  skip_before_filter :verify_authenticity_token, only: :create_update_webhook

  def create_update_webhook
    ticket_params = params['freshdesk_webhook']
    if Base64.decode64(request.headers['Authorization'].sub("Basic ","")).include?(FRESHDESK_API_KEY)
      if(ticket_params['ticket_id']).present?
        FreshdeskTicket.create_update_ticket(ticket_params)
      end
    else
      raise 'Authentication error'
    end
    render json: {head: :ok}
  rescue => e
    ExceptionNotify.sidekiq_delay.notify_exceptions('Freshdesk Ticket not updated in system',  e.message, { error: e.inspect, ticket_id: ticket_params['ticket_id'] })
    render json: {head: :ok}
  end

end
