module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def split_designers_based_on_orders_count(designers_stats,count=10)
    designers_with_orders_greater_than_count = Array.new
    designers_with_orders_less_than_count = Array.new
    designers_stats.each do |designer|
      if designer.present?
        if designer[:avg_orders]>count+1
          designers_with_orders_greater_than_count << designer
        else
          designers_with_orders_less_than_count << designer
        end
      end
    end
    return designers_with_orders_greater_than_count, designers_with_orders_less_than_count
  end

  # Custom product classification
  # Jewellery -> J
  # Women Apparel ( this should be a parent category to Sarees, Salwars, <PERSON><PERSON><PERSON>, <PERSON>is etc. ) -> S
  # Bags -> B
  # Everythign else -> O  ( for other)
  def product_type(design)
    if design.designable_type.present? && design.designable_type != 'Other'
      case design.designable_type
      when 'SalwarKameez', 'Lehenga', 'Saree'
        return 'apparel'
      when 'Jewellery'
        return 'jewellery'
      when 'Bag'
        return 'bag'
      when 'HomeDecor'
        return 'other'
      end
    else
      category_list = Hash.new
      category_list['jewellery'] = ['jewellery', 'bridal-sets']
      category_list['bag'] = ['bags']
      category_list['apparel'] = ['sarees', 'bridal-sarees', 'bridal-lehengas', 'lehengas', 'kurtas-and-kurtis', 'salwar-kameez', 'dresses', 'men-kurtas', "shorts","shawls","skirts","tunics","tops","pyjamas","stole-and-dupattas"]
      design_root_categories_names = design.category_parents_name.collect{|c| c.downcase}
      category_list.each do |product_classification, category_names|
        if (design_root_categories_names & category_names).present?
          return product_classification
        end
      end
      return 'other'
    end
  end

  def designer_options(object)
    options = {}
    options['designer[state_machine]'] = object.state_enum.map &:to_s
    options
  end

  def get_attributes(model)
    attrs =  model.accessible_attributes.to_a
    unless ["accounts_admin"].include?(current_account.role.try(:name))
      model::BANK_DETAILS.each do |detail|
        attrs.delete(detail)
      end
    end
    attrs
  end

  def get_attributes_hash(model, action_type='show')
    attr_hash = Hash[model.columns_hash.collect{|column_name,column_details| [column_name,column_details.type] }]
    model.accessible_attributes.to_a.each do |a_attr|
      unless attr_hash[a_attr].present?
        attr_hash[a_attr] = :file
      end
    end
    do_not_edit_values = model.protected_attributes.to_a
    do_not_edit_values.push(*%w(domestic_transaction_rate transfer_model_rate)) if action_type == 'edit'
    do_not_edit_values.each do |p_attr|
      attr_hash.delete(p_attr)
    end

    attr_hash.delete_if {|key,value| key.to_s.include?("_file_name")}
    attr_hash.delete_if {|key,value| key.to_s.include?("_content_type")}
    attr_hash.delete_if {|key,value| key.to_s.include?("_file_size")}
    attr_hash.delete_if {|key,value| key.to_s.include?("_updated_at")}
    attr_hash.delete_if {|key,value| key.to_s.include?("_created_at")}

    unless ["accounts_admin"].include?(current_account.role.try(:name))
      model::BANK_DETAILS.each do |detail|
        attr_hash.delete(detail)
      end
    end
    attr_hash
  end

  def qc_rate_options
    base_option = [['All', 'All']]
    rate_options = [0, 25, 50, 75, 100].map do |rate|
      ["#{rate}%", (rate/100.0).to_s]
    end
    base_option + rate_options
  end
end
