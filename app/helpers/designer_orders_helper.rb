module DesignerOrdersHelper
  def ga_product_array(items_container)
    if items_container.line_items.count == 1
      items_container.line_items.first.design.id
    else
      products = []
      items_container.line_items.each do |item|
        products << item.design.id.to_s
      end
      products
    end
  end
  
  def ga_product_price(items_container)
    if items_container.line_items.count == 1
      items_container.line_items.first.design.effective_price
    else
      price = []
      items_container.line_items.each do |item|
        price << item.design.effective_price
      end
      price
    end
  end
  
  def self.number_to_words (n)
    words = ''

    units = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen']
    tens = ['', 'ten', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety']

    ['', 'hundred', 'thousand', 'lakh', 'crore'].each do |group|
      if ['', 'thousand', 'lakh'].include?(group)
        n, digits = n / 100, n % 100
      elsif group == 'hundred'
        n, digits = n / 10, n % 10
      else
        digits = n
      end
      if (1...20).include?(digits)
        words = units[digits] + ' ' + group + ' ' + words
      elsif (20...100).include?(digits)
        ten_digit, unit_digit = digits / 10, digits % 10
        words = tens[ten_digit] + ' ' + units[unit_digit] + ' ' + group + ' ' + words
      elsif digits >= 100
        words = number_to_words (digits) + ' crore ' + words
      end
    end
    
    (words + "only").titleize
  end

  def checking_for_addons?(order)
    addons = ['stitching','addon','sconfirm','tailoring']
    tags = order.tags.map(&:name).any?{|t| addons.include?(t)}
  end

  def encrypt_string(current_account_id, design_id, order_id)
    data = "#{current_account_id}.#{design_id}.#{order_id}.#{DateTime.now}"
    cipher = OpenSSL::Cipher::AES.new(128, :CBC)
    cipher.encrypt
    cipher.key = Mirraw::Application.config.qc_image_key
    encrypted = cipher.update(data) + cipher.final
    return Base64.urlsafe_encode64(encrypted)
  end
end
