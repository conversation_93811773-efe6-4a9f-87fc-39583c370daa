class ReturnMailer < ActionMailer::Base
  track user: lambda { Account.find_by email: message.to.try(:first) }

  helper <PERSON><PERSON><PERSON><PERSON>
  def initiate_return(return1)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @return = return1
    @order = @return.order
    mail(:from => from_email_with_name,
         :to => "#{@order.email}, <EMAIL>",
         :subject => "Return Request: Order #{@order.number} - Mirraw.com")
  end

  def return_ticket_process_flow(return1, state, dos_state=nil)
    @return = return1
    @order = return1.order

    state = 'open_cancelled' if dos_state == 'Canceled' and state == 'open'
    @state = state
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    case state
    when 'open_cancelled'
      subject = "Cancellation Request for Order Number: #{@order.number} Received."
      return_label = @return.get_return_label
      attachments["Return_Label_#{@return.id}.pdf"] = return_label if return_label.present?
    when 'open'
      subject = "Return Request for Order number: #{@order.number} Received."
      return_label = @return.get_return_label
      attachments["Return_Label_#{@return.id}.pdf"] = return_label if return_label.present?
    when 'pending'
      subject = "Return Request for Order number: #{@order.number} Pending on approval."
    when 'approved'
      subject = "Return Request for Order number: #{@order.number} approved."
    when 'rejected'
      subject = "Return Request for Order number: #{@order.number} rejected."
    when 'payment complete'
      subject = "Return Request for Order number: #{@order.number} completed."
    when 'processing'
      subject = "Return Request for Order number: #{@order.number} is processed."
    when 'image approval'
      subject = "Images are verified."
    when 'bounced'
      subject = "Refund Request for Order Number: #{@order.number} Bounced."
    when 'item reject'
      subject = "Item removed from Refund Process"
    end
    mail(from: from_email_with_name,
         to: "#{@order.email}",
         subject: subject)
  end

  def send_tracking_info_to_vendor(rdo)
    @rdo = rdo
    order = rdo.return.order
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    rdo.line_items.collect(&:designer_order).flatten.uniq.each do |designer_order|
      if designer_order && (designer_order.designer_payout_status == "unpaid" || designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?) && designer_order.ship_to == 'mirraw'
        designer_order.generate_credit_note
        attachments["Credit_note_#{order.number}_#{designer_order.id}.pdf"] = {
          mime_type: 'application/pdf',
          content: open(designer_order.credit_note_url).read
        }
      end
    end
    mail(:from => from_email_with_name,
         :to => "#{@rdo.designer.email}, <EMAIL>",
         :subject => "Request for Return for Order #{@rdo.return.order.number} - Mirraw.com")
  end

  def inform_refund_details_pending(name, email, order_no)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = name + '<' + email + '>'
    subject = "Requesting Information for processing refund - Mirraw.com"
    @order_no = order_no
    mail(:from => from_email_with_name, :to => to_email_with_name,
         :subject => subject)
  end
  
  def inform_items_not_returned(name, email, order_number, pending_items_rdo, received_items_rdo)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = name + '<' + email + '>'
    subject = 'Pending return of items for : Order ' + order_number + ' - Mirraw.com'
    if pending_items_rdo.present? and received_items_rdo.present?
      subject = ' Received partial return of items : Order '+ order_number + ' - Mirraw.com'
    elsif pending_items_rdo.blank? and received_items_rdo.present?
      subject = 'Received return of items for : Order ' + order_number + ' - Mirraw.com'
    end 
    @pending_items_rdo = pending_items_rdo
    @received_items_rdo = received_items_rdo
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end
  
  def enquiry_items_received(rdos)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = rdos.first.designer.name + '<' + rdos.first.designer.email + '>'
    subject = 'Enquiry about returned items - Mirraw.com'
    @rdos = rdos
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end
  
  def inform_vendor_possible_returns(name, email, items, number)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = name + '<' + email + '>'
    subject = 'Buyer wishes to return items - Order '+ number + ' - Mirraw.com'
    @items = items
    @order = Order.where(number: number).first
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end
  
end