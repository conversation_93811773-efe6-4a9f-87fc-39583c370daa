
# == Schema Information
#
# Table name: images
#
#  id                 :integer         not null, primary key
#  kind               :string(255)
#  design_id          :integer
#  created_at         :datetime
#  updated_at         :datetime
#  photo_file_name    :string(255)
#  photo_content_type :string(255)
#  photo_file_size    :integer
#  photo_updated_at   :datetime
#

class Image < ActiveRecord::Base
  has_paper_trail

  default_scope { order("images.id").where('images.photo_file_name is NOT NULL') }
  belongs_to :design
  belongs_to :item
  include SidekiqHandleAsynchronous
  include UpdateUnbxd
  attr_accessor :processed
  # Right now, images with white background is not activted. To use that change # to > in small style
  #

  before_destroy :send_to_unbxd
  has_attached_file :photo, :styles => {
    :thumb => "50x50#",
    :small => "225x257#",
    :long => '337x507#',
    :small_m => "225^",
    :small_mo => "151x173#",
    :large => "350x400>",
    :large_m => "350x400>",
    :zoom => "800x1100",
    :thumb_webp => ["50x50#", :webp],
    :small_webp => ["225x257#", :webp],
    :long_webp => ['337x507#', :webp],
    :small_m_webp => ["225^", :webp],
    :small_mo_webp => ["151x173#", :webp],
    :large_webp => ["350x400>", :webp],
    :large_m_webp => ["350x400>", :webp],
    :zoom_webp => ["800x1100", :webp],
    :long660 => ['400x600#', :webp],
    :luxe_webp => ['1000x1200#', :webp]
    },
    s3_host_alias:  if ['1', '2'].include?(ENV.fetch('AKAMAI')) && ENV.fetch('PREFIX_AKAMAI_ASSETS_URL')
                      # AKAMAI=2 if fetch other model not from akamai but image from akamai
                      Proc.new {|a| ENV['PREFIX_AKAMAI_ASSETS_URL']+"0.mirraw.com" }
                    else
                      if Rails.env.production?
                        Proc.new {|a| ENV['PREFIX_MIRRAW_ASSETS_URL']+"0.mirraw.com" }
                      else
                        ENV['CLOUDFRONT_URL']
                      end
                    end,
    :convert_options => {
      :thumb => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1",
      :small => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1 -background white -flatten +matte -gravity center",
      :long => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1 -background white -flatten +matte -gravity center",
      :small_m => "+profile -strip -sampling-factor 4:2:0 -quality 30 -interlace Plane -units PixelsPerInch -density 1x1 -background white -flatten +matte -gravity center",
      :small_mo => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1 -background white -flatten +matte -gravity center",
      :large => "+profile -strip -sampling-factor 4:2:0 -interlace Plane -units PixelsPerInch -density 1x1",
      :large_m => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1 -background white -flatten +matte -gravity center",
      :zoom => "+profile -strip -sampling-factor 4:2:0 -quality 70 -interlace Plane -units PixelsPerInch -density 1x1",
      :thumb_webp => "-quality 70 -define webp:method=6",
      :small_webp => "-quality 70 -define webp:method=6",
      :long_webp => "-quality 70 -define webp:method=6",
      :small_m_webp => "-quality 30 -define webp:method=6",
      :small_mo_webp => "-quality 70 -define webp:method=6",
      :large_webp => "-define webp:method=6",
      :large_m_webp => "-quality 70 -define webp:method=6",
      :zoom_webp => "-quality 70 -define webp:method=6",
      :long660 => "-quality 60 -define webp:method=6",
      :luxe_webp => "-quality 60 -define webp:method=6"
    }
  validates_attachment_presence :photo
  validates_attachment_size :photo, :less_than => 5.megabytes
  validates_attachment_content_type :photo, :content_type => /image/
  validate :image_dimensions, if: :photo_updated_at_changed?
  process_in_background :photo, priority: 4

  before_post_process :randomize_file_name
  after_post_process :do_not_process_orignal

  after_validation :log_errors, :if => Proc.new {|m| m.errors}

  before_save :original_photo_processing_check, :save_dimensions

  after_save :process_original, unless: Proc.new { self.processed.present? }
  after_save :send_to_unbxd
  after_save :reprocess_long, if: :kind_changed?
  # input params - none
  #
  # Returns Boolean
  def original_photo_processing_check
    self.original_photo_processing = self.processed || ( self.photo_updated_at_changed? ? false : true )
    true
  end

  # input params - none
  #
  # Decides image to display
  #
  # Returns String
  def original_display
    self.original_photo_processing ? self.photo.url(:original) : '/processing.jpg'
  end

  def zoom_display
    self.original_photo_processing ? self.photo.url(:zoom) : '/processing.jpg'
  end

  # input params - none
  #
  # Optimizes original image and uploads to s3
  #
  # Returns nil
  def process_original
    self.optimize_original_image_file_s3 unless self.original_photo_processing
  end

  handle_asynchronously :process_original, priority: 9

  # input params - none
  #
  # optimize s3 image and store back to s3
  #
  # Returns nil
  def optimize_original_image_file_s3
    aws_access_key_id = Mirraw::Application.config.aws[:access_key_id]
    aws_secret_access_key = Mirraw::Application.config.aws[:secret_access_key]
    aws_bucket = Mirraw::Application.config.aws[:bucket]

    s3_bucket = AWS::S3.new(:access_key_id => aws_access_key_id, :secret_access_key => aws_secret_access_key).buckets[aws_bucket]

    if (s3_image_object = s3_bucket.objects[self.photo.path]).exists?
      image_optim = ImageOptim.new(:pngout => false, :svgo => false)
      optimized_image_data = image_optim.optimize_image_data(s3_image_object.read)
      unless optimized_image_data.nil?
        file_object = StringIO.new(optimized_image_data)
        file_object.class.class_eval { attr_accessor :original_filename, :content_type }
        file_object.original_filename = 'image.jpeg'
        file_object.content_type = self.photo_content_type

        options = {:content_type => self.photo_content_type, :expires => 10.years.from_now.httpdate, :cache_control => 'max-age=315576000', :metadata => {:img_optim => 'Y'}, :acl => 'public-read'}
        s3_image_object.write(file_object.read, options)
      end
      self.original_photo_processing = true
      self.save
    end
  end

  def self.check_for_dropbox_link(link)
    result = link
    result = link.gsub(/dl=0/, 'dl=1') if link.match('dl=0').present?
    result
  end

  def self.save_size_chart_images(csv)
    csv.each_with_index do |row, index|
      if index > 0
        design = Design.where(id: row[0]).try(:first)
        if design && row[1].present? && row[1].match(/(http|https):\/\//)
          image = design.images.where(kind: 'size_chart')
          if image.first.nil?
            design.images.build(kind: 'size_chart', photo: self.check_for_dropbox_link(row[1]))
            design.save
          else
            image_temp = image.first
            image_temp.update_attributes(design.images.build(kind: 'size_chart', photo: self.check_for_dropbox_link(row[1])).attributes)
          end
        end
      end
    end
  end

  private    
    def log_errors
      Rails.logger.debug self.errors.full_messages.join("\n")
    end

    def send_to_unbxd
      update_unbxd self.design_id if self.design_id.present?
    end

    def randomize_file_name
      if self.new_record?
        extension = File.extname(photo_file_name).downcase
        self.photo.instance_write(:file_name, "#{SecureRandom.hex(16)}#{extension}")
      end
    end

    def reprocess_long
      if photo_updated_at <= LONG_DIV_RUN_DATE && (self.design.categories.pluck(:id) & LONG_DIV_CATEGORY_IDS).present?
        self.processed = true
        self.photo.reprocess! :long
        self.update_column(:photo_processing, false)
      end
    end

    def do_not_process_orignal
      self.processed=true #no to trigger process orignal here
    end

    def save_dimensions
      if (image_file = photo.queued_for_write[:original]) && photo_updated_at_changed?
        dimensions = Paperclip::Geometry.from_file(image_file)
        self.height, self.width = dimensions.try(:height), dimensions.try(:width)
      end
    end

    def image_dimensions
      if self.design_id.present? && (image_file = photo.queued_for_write[:original])
        dimensions = Paperclip::Geometry.from_file(image_file)
        errors.add(:image, "Width of Image must be be greater than 800px") if dimensions.width < 800
        errors.add(:image, "Height of Image must be greater than 1100px") if dimensions.height < 1100
      end
    end
end
