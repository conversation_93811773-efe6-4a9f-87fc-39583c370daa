class MainMenu < ActiveRecord::Base
  # Scopes for different app types
  scope :mirraw, -> { where(app_name: [nil, ""]) }
  scope :luxe, -> { where(app_name: 'luxe') }
  scope :visible, -> { where(is_hidden: false) }
  scope :ordered, -> { order(:position) }

  # Associations
  has_many :menus, dependent: :nullify
  
  # Validations
  validates :title, presence: true
  validates :position, presence: true, uniqueness: { scope: [:country, :app_source] }

  # Include link type identification (similar to Menu model)
  include IdentifyLinkType
  before_save :identify_link

  # Class method to get main menus with their menus for a specific country
  def self.with_menus_by_country(country)
    includes(:menus).visible.ordered
      .where("(main_menus.country ILIKE '%#{country}%' OR main_menus.country IS NULL OR main_menus.country='')")
      .where("(main_menus.app_source IS NULL OR main_menus.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR main_menus.app_source='')")
  end

  # Instance method to get visible menus for this main menu
  def visible_menus
    menus.where(hide: false).order(:position)
  end

  # Method to check if main menu has any visible menus
  def has_visible_menus?
    visible_menus.exists?
  end
end
