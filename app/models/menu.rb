class Menu < ActiveRecord::Base
  scope :mirraw, -> { where(app_name: [nil,""]) }
  scope :luxe, -> { where(app_name: 'luxe') }

  # Associations
  belongs_to :main_menu
  has_many :menu_columns
  has_many :menu_tabs
  has_many :tabs, through: :menu_tabs
  accepts_nested_attributes_for :menu_columns
  #attr_accessible :link, :position, :title, :menu_columns, :hide, :country, :app_source, :menu_photo1, :menu_photo2, :menu_link1, :menu_link2

  has_attached_file :menu_photo1, styles: {main: "320x190"},
  convert_options:{main: '+profile -strip -sampling-factor 4:2:0 -quality 80 -interlace Plane -background white -flatten +matte -gravity center'}
  validates_attachment_content_type :menu_photo1, content_type: /image/

  has_attached_file :menu_photo2, styles: {main: "320x190"},
  convert_options:{main: '+profile -strip -sampling-factor 4:2:0 -quality 80 -interlace Plane -background white -flatten +matte -gravity center'}
  validates_attachment_content_type :menu_photo2, content_type: /image/

  include IdentifyLinkType
  before_save :identify_link

  def self.menu_column_and_item_by_hide_appsource_country(country)
    Menu.mirraw.includes(:menu_columns => :menu_items).where(hide: false, menu_columns: {hide: false}, menu_items: {hide: false}).
    where("(menus.country ILIKE '%#{country}%' OR menus.country IS NULL OR menus.country='')AND(menus.app_source IS NULL OR menus.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menus.app_source='')AND(menu_columns.country ILIKE '%#{country}%' OR menu_columns.country IS NULL OR menu_columns.country='')AND(menu_columns.app_source IS NULL OR menu_columns.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menu_columns.app_source='')AND(menu_items.country ILIKE '%#{country}%' OR menu_items.country IS NULL OR menu_items.country='')AND(menu_items.app_source IS NULL OR menu_items.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menu_items.app_source='')").
    order(:position)
  end

end
