class PayoutManagement < ActiveRecord::Base
  scope :latest_payout,->{order('payout_managements.created_at DESC')}
  belongs_to :designer

  extend DynamicTemplateMethod
  dynamic_template_fields :payout_version ,:payout_amount ,:adjustments_amount ,:invoice_status ,:paid ,:paid_date ,:payout_type ,:created_at ,:updated_at 
  
  def self.calculate_pending_payouts_international(start_date,end_date,email,next_payout_version,payout_type,freeze_payout, is_weekly)
    designers_orders = []
    check_previous_freezed_records = DesignerOrder.where(designer_payout_notes: "Payout For :#{next_payout_version}").count
    if check_previous_freezed_records == 0
      raw_designer_order_query = DesignerOrder.unscoped.select('designer_orders.id,payout,designer_id,gst_tax,gst_status,tcs_tax,tds_tax').where(created_at: Date.parse(start_date).beginning_of_day..Date.parse(end_date).end_of_day,state: 'completed').where('designer_payout_status <> (?)','paid').where{(designer_payout_notes == '')|(designer_payout_notes == nil)}.
        where(is_weekly ? {designer_id: WEEKLY_PAYOUT_VENDOR_IDS} : ['designer_id not in (?)', WEEKLY_PAYOUT_VENDOR_IDS])
      if payout_type == "INT/"
        final_query = raw_designer_order_query.joins(:order).where(designer_orders: {ship_to: ['mirraw', nil]}).where("(orders.state IN (?) OR (orders.state = 'partial_dispatch' and designer_orders.created_at > ?))", ['dispatched', 'complete'], Date.new(2024, 4, 1))
      elsif payout_type == "DOM/"
        query_string = 'ship_to <> (?) AND orders.state IN (?)'
        states = ['dispatched','sane','complete']
        final_query = raw_designer_order_query.joins(:order).where(query_string, 'mirraw',states) 
      end
    else
      final_query = DesignerOrder.unscoped.where(designer_payout_notes: "Payout For :#{next_payout_version}")
    end  
    designers_pending_payouts = Hash.new(0)
    designer_order_ids = Hash.new
    final_query.find_in_batches(batch_size: 1000) do |designers_orders|
      designers_orders.each do |d|
        designers_pending_payouts[d.designer_id] += (payout_type == "INT/" && d.gst_status != 'release') ? (d.payout - d.gst_tax).round(2) : (payout_type == "DOM/" ? (d.payout - d.tcs_tax - d.tds_tax) : d.payout)
        (designer_order_ids[d.designer_id]||=[]) << d.id
      end
    end
    designer_ids = designers_pending_payouts.keys
    designer_gst_release, designer_gst_release_ids,  = Hash.new(0), Hash.new
    if payout_type == "INT/"
      DesignerOrder.select('designer_orders.id,payout,designer_id,gst_tax').joins(:order).where(designer_id: designer_ids ,designer_orders: {state: ['completed','dispatched','buyer_returned'], gst_status: 'process'}).where('(ship_to = ? or ship_to is null) and designer_payout_notes is not null','mirraw').find_each do |dos|
        designer_gst_release[dos.designer_id] += dos.gst_tax
        (designer_gst_release_ids[dos.designer_id]||=[]) << dos.id
      end
    end
    designers_adjustments = Adjustment.where(designer_id: designer_ids,status: 'unpaid').where('adjustments.notes <> ?','Negative Commission').where{(payout_consideration != "YES") | (payout_consideration == nil)}.joins('left outer join orders on adjustments.order_id = orders.id left outer join designer_orders on designer_orders.id = adjustments.designer_order_id')
    if payout_type == 'INT/'
      designers_adjustments = designers_adjustments.where('designer_orders.ship_to <> ? OR designer_orders.ship_to is null OR adjustments.order_id is null','customer')
    else
      designers_adjustments = designers_adjustments.where(designer_orders: {ship_to: 'customer'})
    end
    # total adjustment for each designer
    # adjustment ids for each designer
    adjustment_amounts = Hash.new(0)
    adjustment_ids = Hash.new()
    designers_adjustments.find_each do |a|
      adjustment_amounts[a.designer_id] += a.amount
      (adjustment_ids[a.designer_id]||=[])<< a.id
    end
    adjustments_to_be_marked_paid = Array.new
    # to calculate final payout for each designer
    designers_pending_payouts.each do |designer_id,value|
      if adjustment_amounts[designer_id].present?
        designers_pending_payouts[designer_id] += adjustment_amounts[designer_id]        
        adjustments_to_be_marked_paid << designer_id   
      end
    end
    # find ids of adjustment which needs to be marked as paid.    
    adjustment_ids.delete_if{|key,value| adjustments_to_be_marked_paid.exclude? key} 
    #freeze designer orders for current payout
    if (freeze_payout && check_previous_freezed_records == 0)
      DesignerOrder.where(id: designer_order_ids.values.flatten).update_all(designer_payout_notes: "Payout For :#{next_payout_version}")
      Adjustment.where(id: adjustment_ids.values.flatten).update_all(payout_notes: "Payout For :#{next_payout_version}")
    end
    PayoutManagement.export_new_records(designers_pending_payouts,adjustment_amounts,adjustment_ids,designer_order_ids,email,next_payout_version,designer_gst_release,designer_gst_release_ids)
  end

  def self.export_new_records(designers_pending_payouts,adjustment_amounts,adjustment_ids,designer_order_ids,email,next_payout_version,designer_gst_release,designer_gst_release_ids)
    payout_records = []
    records_for_csv_file = []
    # Designer Invoices status
    designer_invoices_status = DesignerInvoice.select('designer_id,status').where(designer_id: designers_pending_payouts.keys,payout_version: next_payout_version)
    designer_invoice_details = {}
    designer_invoices_status.each do|d| 
      current_invoice_status = designer_invoice_details[d.designer_id]
      unless current_invoice_status.present? && current_invoice_status == "Approved"
        status = d.status.nil? ? "Pending" : d.status
        designer_invoice_details[d.designer_id] = status  
      end
    end
    designer_names = []
    Designer.where(id:designers_pending_payouts.keys).collect{|m| designer_names[m.id] = m.name }
    #Create Individual Payout records
    designers_pending_payouts.each do |designer_id, payout|
      designer_invoice_status = designer_invoice_details[designer_id].present? ? designer_invoice_details[designer_id] : "NOT UPLOADED"
      final_payout = (payout + designer_gst_release[designer_id].to_f).round(2)
      records_for_csv_file << [next_payout_version,designer_id,designer_names[designer_id],final_payout,adjustment_amounts[designer_id],'','',false,designer_invoice_status, designer_gst_release[designer_id], designer_gst_release_ids[designer_id].to_a.join(', ')]
    end
    dir = '/tmp/payout_files/'
    FileUtils.mkdir_p(dir) unless File.directory?(dir)
    payment_file = File.new("#{dir}payout_file",'w+')
    CSV.open("#{dir}payout_file", "wb") do |csv|
      csv << ['payout_version','designer_id','designer_name','final_payout','adjustments_amount','adjustment_id','designer_order_ids','paid','invoice_status','gst_release_amount', 'gst_release_ids','payout_notes']
      records_for_csv_file.each do |record|
        csv << record
      end
    end
    order_data_sheet_link = PayoutManagement.export_order_data(designer_order_ids.values.flatten)
    account_name = email.split("@")[0].tr('.','_')
    payout_file_path = "/pending_payout_details/payout_csv_#{account_name}_#{SecureRandom.hex(6)}.csv"
    AwsOperations.create_aws_file(payout_file_path,payment_file)
    link = AwsOperations.get_aws_file_path(payout_file_path)
    OrderMailer.payout_reports(link,"Payout Confirmation File:#{next_payout_version}",email).deliver
    OrderMailer.payout_reports(order_data_sheet_link,"Orders Data Sheet:#{next_payout_version}",email).deliver
  end

  def self.create_new_records(payout_records,email)
    if payout_records.present?
      records = []
      adjustment_ids = []
      payout_records.each do |pc|
        payout_type =  pc[0].include?('INT/') ? 'International' : 'Domestic'
        dos_ids = DesignerOrder.where(designer_id: pc[1], designer_payout_notes: "Payout For :#{pc[0]}").where('designer_payout_status <> (?)','paid').pluck(:id).map{|i| "#{i}"}.join(",")
        adj_ids = Adjustment.where(designer_id: pc[1], payout_notes: "Payout For :#{pc[0]}").pluck(:id).map{|i| "#{i}"}.join(",")
        payout_management = PayoutManagement.where(payout_version: pc[0],designer_id: pc[1],payout_amount: pc[3],adjustments_amount: pc[4],adjustment_ids: adj_ids,designer_order_ids: dos_ids,paid: false,invoice_status: pc[8],payout_notes: pc[11],payout_type: payout_type, gst_release_amount: pc[9], gst_release_ids: pc[10]).first_or_initialize
        records << payout_management
        DesignerOrder.where(id: pc[10].to_s.split(',').map(&:to_i)).update_all(gst_status: 'freeze')
        payout_management.payout_summary_url =  payout_summary(payout_management)
        adjustment_ids << adj_ids.split(',').map(&:to_i)
      end
    designer_ids = records.map{|r| r.designer_id}
    update_column = :payout_amount ,:adjustments_amount ,:adjustment_ids ,:designer_order_ids ,:invoice_status ,:payout_notes , :gst_release_amount, :gst_release_ids
    import_response = PayoutManagement.import records, validation: false, on_duplicate_key_update: {conflict_target: [:id], columns: update_column}
    Adjustment.where(id: adjustment_ids.flatten).update_all(payout_consideration: "YES")
    PayoutManagement.sidekiq_delay(queue: 'critical').create_payout_banksheet(records[0].payout_version,designer_ids,email)
    PayoutManagement.sidekiq_delay(queue: 'critical').generate_payout_comparison_sheet(designer_ids,records[0].payout_type,email)
    PayoutManagement.sidekiq_delay(queue: 'critical').save_payout_ids_in_designer_orders(import_response.ids)
    Adjustment.sidekiq_delay(queue: 'critical').generate_adjustments_report(adjustment_ids.flatten, records[0].payout_type, email)
    end
  end

  def self.save_payout_ids_in_designer_orders(payout_ids)
    payout_managements = PayoutManagement.where(id: payout_ids).select('id, gst_release_ids, designer_order_ids')
    payout_managements.each do |payout_management|
      DesignerOrder.where(id: payout_management.gst_release_ids.to_s.split(',')).update_all(release_payout_management_id: payout_management.id)
      DesignerOrder.where(id: payout_management.designer_order_ids.to_s.split(',')).update_all(payout_management_id: payout_management.id)
    end
  end

  def self.update_paid_payout_details(filename,email)
    filepath = AwsOperations.get_aws_file_path(filename)
    designer_ids = {}
    payout_version, paid_date = nil,nil
    designer_order_ids_string, adjustments_ids_string = ""
    # designer_id, utr_number, amount, paid_date, payout_version
    CSV.new(open(filepath), {:headers => true, :header_converters => :symbol}).each do |line|
      if line[0].present? && line[1].present? && line[2].present? && line[3].present? 
        payout_version  = line[4] if payout_version.nil?
        designer_ids[line[0]] = [line[1],line[2],line[3]]
        paid_date = line[3] if paid_date.nil?
      end
    end
    release_ids = []
    PayoutManagement.preload(:designer).where(designer_id: designer_ids.keys,payout_version: payout_version,paid: false).find_each do |designer_payout|
      designer_order_ids_string = "#{designer_order_ids_string},#{designer_payout.designer_order_ids}"
      adjustments_ids_string = "#{adjustments_ids_string},#{designer_payout.adjustment_ids}"
      release_ids << designer_payout.gst_release_ids.to_s.split(', ')
      designer_email     = designer_payout.designer.email
      payout_summary_url = designer_payout.payout_summary_url
      DesignerMailer.sidekiq_delay.paid_payout_report(designer_email, payout_summary_url)
    end
    release_ids.flatten!
    designer_order_ids = designer_order_ids_string.to_s.split(",").map!(&:to_i)
    adjustment_ids = adjustments_ids_string.to_s.split(",").map!(&:to_i)
    payout_date = DateTime.parse(paid_date)
    DesignerOrder.where(id: designer_order_ids).update_all(designer_payout_status: 'paid',designer_payout_date: payout_date,designer_payout_notes: "#{payout_version }")
    DesignerOrder.where(id: release_ids).update_all(gst_status: 'release') if release_ids.present?
    Adjustment.where(id: adjustment_ids).update_all(status: 'paid',payout_date: payout_date, payout_uploaded_on: Time.current)
    designer_ids.each do |d|
      PayoutManagement.where(designer_id: d[0],payout_version: payout_version).update_all(utr_number: d[1][0],paid: true, paid_date: DateTime.parse(d[1][2]),payout_notes: "Amount : #{d[1][1]}")
    end
    # PayoutManagement.generate_monthly_payout_invoice(payout_date.to_s,email) if payout_version.include?('DOM/')
  end

  def self.create_payout_banksheet(payout_version,designer_ids,email,check_invoice = true)
    check_invoice_condition = check_invoice ? "LOWER(payout_managements.invoice_status) = 'approved'" : ''
    payout_data = PayoutManagement.where(payout_version: payout_version,designer_id: designer_ids).where(check_invoice_condition)
    records = []
    reporting_data = []
    link = ""
    if payout_data.present?
      dir = "tmp/banksheet/"
      FileUtils.mkdir_p(dir) unless File.directory?(dir)
      banksheet_path = "#{dir}banksheet.csv"
      payout_data.each do |data|
        add_to_banksheet, sheet_format_array = PayoutManagement.bank_format_data_generation(data)
        if add_to_banksheet
          records << sheet_format_array
        else
          reporting_data << sheet_format_array
        end  
      end
      header =  ['Transaction Type (N<200000 R>200000)','Beneficiary Code','BeneficiaryAccountNumber','Instrument Amount','Beneficiary Name','Drawee Location', 
                'Print Location','Bene Address 1','Bene Address 2','Bene Address 3','Bene Address 4','Bene Address 5','Instruction Reference Number','Customer Reference Number (Narration)','Payment details 1','Payment details 2','Payment details 3','Payment details 4','Payment details 5','Payment details 6','Payment details 7','Cheque Number','Transaction date dd/mm/yyyy','MICR Number','BeneIFSCode','Bene Bank Name','Bene Bank Branch Name','Beneficiary email id']
      CSV.open(banksheet_path, "w+") do |csv|
        csv << header
        records.each do |record|
          csv << record
        end
      end
      aws_banksheet_file_path = "/banksheet/banksheet_#{Date.today.strftime("%d_%m_%y")}_#{SecureRandom.hex(6)}.csv"
      AwsOperations.create_aws_file(aws_banksheet_file_path,File.new(banksheet_path))
      link = AwsOperations.get_aws_file_path(aws_banksheet_file_path)
      OrderMailer.payout_reports(link,"Banksheet for #{payout_version}",email,{faulty_data: reporting_data}).deliver
    elsif
      OrderMailer.payout_reports(link,"Banksheet for #{payout_version} : No invoices were approved",email).deliver  
    end
  end

  def self.bank_format_data_generation(data)
    designer = data.designer
    acc_name = designer.account_holder_name.to_s
    bank_name = designer.bank_name.to_s
    barnch_name = designer.branch.to_s
    ifsc_code = designer.ifsc_code.strip.to_s
    account_number = '="'+ "#{designer.account_number.to_s}" + '"' #to keep leading zeros in csv file
    amount = data.payout_amount.to_s
    add_to_banksheet = [acc_name,ifsc_code,account_number].any?{|word| word.blank?} ? false : true
    add_to_banksheet = false if ifsc_code.length != 11
    # If payout amount is less than 0 then mark all designer orders and adjustments related to it as paid.
    # because bank will never accept negative amount transfer 
    if amount.to_f < 0
      add_to_banksheet = false
      designer_order_ids_string = "#{data.designer_order_ids}"
      adjustments_ids_string = "#{data.adjustment_ids}"
      designer_order_ids = designer_order_ids_string.to_s.split(",").map!(&:to_i)
      adjustment_ids = adjustments_ids_string.to_s.split(",").map!(&:to_i)
      DesignerOrder.where(id: designer_order_ids).update_all(designer_payout_status: 'paid',designer_payout_date: DateTime.now(),designer_payout_notes: "#{data.payout_version }")
      Adjustment.where(id: adjustment_ids).update_all(status: 'paid',payout_date: DateTime.now(), payout_uploaded_on: Time.current)
      PayoutManagement.where(designer_id: designer.id,payout_version: data.payout_version).update_all(paid: true, paid_date: DateTime.now())
      #Create negative adjusment. If final payout < 0 (Example: Payout=1500,adustment = -2000)
      Adjustment.where(amount: amount,designer_id: designer.id,notes: "Adjustment from payout_version: #{data.payout_version}",status: 'unpaid').first_or_create
    end
    transaction_type = ifsc_code.upcase.include?("HDFC") ? 'I' : 'N'
    sheet_format_array = Array.new(28)  #28 column in hdfc bank sheet.
    sheet_format_array[0] = transaction_type
    sheet_format_array[2] = account_number
    sheet_format_array[3] = amount
    sheet_format_array[4] = acc_name
    customer_reference = "#{designer.id}_#{data.payout_version}".length <= 20 ? "#{designer.id}_#{data.payout_version}" : ""
    sheet_format_array[13] = customer_reference
    sheet_format_array[22] = Date.today.strftime("%d/%m/%Y")
    sheet_format_array[24] =ifsc_code
    return add_to_banksheet,sheet_format_array
  end

  def self.generate_payout_comparison_sheet(designer_ids,payout_type,email)
    payout_history = PayoutManagement.latest_payout.select('payout_amount as payout_amount,designer_id as designer_id,payout_version as payout_version').where(designer_id: designer_ids,payout_type: payout_type).group_by(&:designer_id)
    designer_names = Hash[Designer.select('name as name,id as designer_id').where(id: designer_ids).map { |u| [u.designer_id, u.name] }]
    #historical payout of every designer 
    payout_comparison = {}
    payout_history.each do |key,value|
      payout_comparison[key] = Hash.new()
      value.each_with_index do |data,index|
        current_amount = data.payout_amount
        previous_amount = value[index+1].present? ? value[index+1].payout_amount : current_amount 
        percent_change = (((current_amount.to_f - previous_amount.to_f)/previous_amount.to_f)*100).round(2)
        (payout_comparison[key]['payout_versions']||=[]) << data.payout_version
        (payout_comparison[key]['payout_change']||=[]) << "#{current_amount}(#{percent_change}%)" 
      end
    end
    if payout_comparison.present?
      dir = "tmp/comparison_sheet/"
      FileUtils.mkdir_p(dir) unless File.directory?(dir)
      comparison_sheet_path = "#{dir}payout_comparison.csv"
      CSV.open(comparison_sheet_path, "w+") do |csv|
        payout_comparison.each do |designer_id,data|
          csv << [" "]+payout_comparison[designer_id]['payout_versions']
          csv << [designer_names[designer_id.to_s]] + payout_comparison[designer_id]['payout_change']
          csv << []
        end
      end
      aws_comparisonsheet_file_path = "/comparison_sheet/comparison_sheet_#{Date.today.strftime("%d_%m_%y")}_#{SecureRandom.hex(6)}.csv"
      AwsOperations.create_aws_file(aws_comparisonsheet_file_path,File.new(comparison_sheet_path))
      link = AwsOperations.get_aws_file_path(aws_comparisonsheet_file_path)
      OrderMailer.payout_reports(link,"Designer Payout Comparison Sheet",email).deliver
    end  
  end

  def self.export_order_data(designer_order_ids)
    dir = "tmp/order_data_sheet/"
    FileUtils.mkdir_p(dir) unless File.directory?(dir)
    order_data_sheet_path = "#{dir}orders_data.csv"
    CSV.open(order_data_sheet_path, "w+") do |csv|
      csv << ["designer_id","designer_name","designer_order_id","order_number","designer_payout_status",
              "designer_order_state","order_state","designer_order_total","designer_order_discount",
              "payout","transaction_rate","order_created_at","order_total","orders_discount",
              "paid_amount","addon_charges","mirraw_shipping_cost",'pay_type','gst_status','gst_tax','tcs_tax','tds_tax']
    end
    DesignerOrder.preload(:order,:designer).where(id: designer_order_ids).find_in_batches(batch_size: 1000) do |designer_orders|
      CSV.open(order_data_sheet_path, "a+") do |csv|
        designer_orders.each do |dso|
          csv << [dso.designer.id,dso.designer.name,dso.id,dso.order.number,dso.designer_payout_status,
                  dso.state,dso.order.state,dso.total,dso.discount,
                  (dso.ship_to == 'mirraw' ? dso.payout : (dso.payout.to_f - dso.tcs_tax.to_f - dso.tds_tax.to_f).round(2)),dso.transaction_rate,dso.order.created_at,dso.order.total,dso.order.discount,
                  dso.order.paid_amount,dso.order.mirraw_addon_charges,dso.mirraw_shipping_cost,dso.order.pay_type,dso.gst_status, dso.gst_tax, dso.tcs_tax, dso.tds_tax]
        end  
      end
    end
    aws_order_data_sheet_path = "/order_data_sheet/order_data_sheet_#{Date.today.strftime("%d_%m_%y")}_#{SecureRandom.hex(6)}.csv"
    AwsOperations.create_aws_file(aws_order_data_sheet_path,File.new(order_data_sheet_path))
    link = AwsOperations.get_aws_file_path(aws_order_data_sheet_path)
  end

  def self.generate_adjustment_from_csv(filename,email)
    file_url = AwsOperations.get_aws_file_path(filename)
    adjustment_details, rejected_adjustments = {},[]
    CSV.new(open(file_url), headers: :first_row).each do |row|
      order_number  = row[0].to_s.strip.upcase
      designer_id   = row[1].to_s.strip
      designer_name = row[4].to_s.strip 
      amount        = row[2].to_s.strip.to_i
      notes         = row[3]
      (adjustment_details[order_number]||=[]) << [order_number,designer_id,designer_name,amount,notes]
    end
    adjustment_details.each_slice(250) do |adjustment_details_grp|
      new_adjustments = []
      orders = Order.where(number: adjustment_details_grp.collect(&:first)).preload(:designer_orders)
      adjustment_details_grp.each do |order_number,adjustment_entries|
        order = orders.find{|o| o.number == order_number }
        if order.present?
          designer_ids = order.designer_orders.collect(&:designer_id).uniq
          adjustment_entries.each do |single_adjustment|
            designer_id = single_adjustment[1].to_i
            #if designer_ids.include?(designer_id) && EXCLUDE_VENDOR_FOR_ADJUSTMENTS.map(&:to_i).exclude?(designer_id)
            if designer_ids.include?(designer_id)
              dos = order.designer_orders.find{|dos| dos.designer_id == designer_id}
              new_adjustments <<  Adjustment.new(amount: single_adjustment[3].to_i,designer_id: designer_id,
                                                 status: 'unpaid',order_id: order.id,notes: "#{single_adjustment[4]} For Order #{order_number}", designer_order_id: dos.try(:id))
            else
              rejected_adjustments << single_adjustment
            end
          end
        else
          adjustment_entries.map{|record| rejected_adjustments << record}
        end
      end
      Adjustment.import new_adjustments
    end
    if rejected_adjustments.present?
      file = CSV.generate do |csv|
        csv << ["Following adjustments were not created"]
        rejected_adjustments.each do |data|
          csv << data
        end
      end
      attachments = {attachment: file}
    else
      attachments = {}
    end
    OrderMailer.payout_reports("#","Adjustment Report",email,attachments).deliver
  end

  def self.generate_monthly_payout_invoice(payout_date,email)
    # payout_date = DateTime.parse(payout_date)
    # invoice_ids = []
    # totals = DesignerOrder.unscoped.joins(:line_items, :order).select("line_items.designer_order_id as id,designer_orders.designer_id, sum(COALESCE(designer_orders.total,0))/count(line_items.id) as total, sum(COALESCE((line_items.vendor_selling_price * line_items.quantity),0)) - (sum(COALESCE(designer_orders.discount,0))/count(line_items.id)) as total1").where(designer_payout_date: payout_date.beginning_of_day..payout_date.end_of_day).where(order: {country: "India"}).where(designer_payout_status: "paid").where('commission_invoice_id IS NULL').group('designer_orders.designer_id, line_items.designer_order_id').group_by(&:designer_id)
    # totals.each do |key,value|
    #   ids = value.collect(&:id)
    #   total = value.sum{|x| [x.total.to_i, x.total1.to_i].max}
    #   commission_invoice = CommissionInvoice.create(designer_id: key, total: total)
    #   invoice_ids << commission_invoice.id
    #   DesignerOrder.unscoped.where(id: ids).update_all(commission_invoice_id: commission_invoice.id)
    #   commission_invoice.generate_payout_invoice(payout_date,payout_date)
    # end
    # com_invoices = CommissionInvoice.where(id: invoice_ids)
    # file =  CSV.generate do |csv|
    #   csv << ['invoice_id','invoice_url','designer_id','total']
    #   com_invoices.each do |ci|
    #     csv << [ci.id,ci.invoice_url,ci.designer_id,ci.total]
    #   end  
    # end
    # <AUTHOR> <EMAIL>'} 
    # OrderMailer.report_mailer("Commission Invoice Data For Payout Date : #{payout_date}",'Please Find Attachment.',emails,{'Commission Invoice Data.csv'=>file}).deliver  
  end

  def self.get_gst_tax(cgst_sgst,cgst,sgst,igst,amount_payable = 0.0)
    igst_tax,sgst_tax,cgst_tax,net_commission = 0,0,0,0.0
    if cgst_sgst
      net_commission = amount_payable/(1+ cgst + sgst)
      cgst_tax       = (net_commission * cgst).round(2)
      sgst_tax       = (net_commission * sgst).round(2)
    else
      net_commission = amount_payable/(1+ igst)
      igst_tax       = (net_commission * igst).round(2)
    end
    return [net_commission.round(2),cgst_tax,sgst_tax,igst_tax]
  end

  private
  def self.payout_summary(payout_management)
    file  = Tempfile.new(['payout_management','.csv'])
    today = Date.current.strftime('%d/%m/%Y')
    CSV.open(file.path, "wb") do |csv|
      header = ["Invoice Number", "Invoice Date", "Reference No", "Taxable Value", "GST Amount"]
      header += payout_management.payout_type == "International" ? ["Total Invoice Value", "GST HOLD", "GST Release", "Paid Value"] : ["Selling Price", "TCS", "TDS", "Commission", "Fedex Mirraw Cost", "Final Payout"]
      header += ["Paid Date", "Notes"]
      csv << header
      DesignerOrder.where(id: payout_management.designer_order_ids.to_s.split(",")).preload(:payment_order).each do |dos|
        line_items = LineItem.where(designer_order_id: dos.id)
        total, taxable_total, gst_total, tcs_total, tds_total, gst_rate, commission_total= 0.0,0.0,0.0,0.0,0.0,0.0,0.0
        line_items.each do |item|
          hsn_code,gst_rate = item[:purchase_hsn_code],item[:purchase_gst_rate]
          if item.order.international?
            _,gst_rate = item.find_hscode_gst(item[:vendor_selling_price], hsn_code.to_s, false)
            total += item[:vendor_selling_price]
            product_taxable_value = (item[:vendor_selling_price]/(1+gst_rate/100.0)).round(2)
            gst_total += (item[:vendor_selling_price] - product_taxable_value).round(2)
            product_gst = product_taxable_value*(gst_rate/100.0).round(2)
            tcs_value = (product_taxable_value * 0.5/100.0).round(2)
            tcs_total += tcs_value
            tds_value = (product_taxable_value * 0.1/100.0).round(2)
            tds_total += tds_value
            taxable_total += product_taxable_value
            commission = (item[:vendor_selling_price] * dos.transaction_rate / 100).round(2)
            commission_total += commission
          else
            _,gst_rate = item.find_hscode_gst(item.price(RETURN_NORMAL), hsn_code.to_s, false)
            total += item.price(RETURN_NORMAL)
            product_taxable_value = (item.price(RETURN_NORMAL)/(1+gst_rate/100.0)).round(2)
            gst_total += (item.price(RETURN_NORMAL) - product_taxable_value).round(2)
            product_gst = product_taxable_value*(gst_rate/100.0).round(2)
            tcs_value = (product_taxable_value * 0.5/100.0).round(2)
            tcs_total += tcs_value
            tds_value = (product_taxable_value * 0.1/100.0).round(2)
            tds_total += tds_value 
            taxable_total += product_taxable_value
            commission = (item.price(RETURN_NORMAL) * dos.transaction_rate / 100).round(2)
            commission_total += commission
          end
        end 
        final_payout = (total - commission_total.to_f - dos.mirraw_shipping_cost.to_f - tcs_total.to_f - tds_total.to_f).round(2)
        taxable_value = payout_management.payout_type == "International" ? (dos.payout.to_f - dos.gst_tax.to_f).round(2) : taxable_total
        row = [dos.invoice_number, dos.pickup.try(:strftime, "%d/%m/%Y"), dos.payment_order.try(:number), taxable_value, gst_total]
        if payout_management.payout_type == "Domestic"
          # commission = (dos.total - dos.payout - dos.mirraw_shipping_cost.to_f).round(2)
          # final_pay = (dos.total - commission.to_f - dos.mirraw_shipping_cost.to_f - dos.tcs_tax.to_f - dos.tds_tax.to_f).round(2)
          # row += [dos.total, dos.tcs_tax, dos.tds_tax, commission, dos.mirraw_shipping_cost, final_pay, today, "Normal Payment"]
          row += [total, tcs_total, tds_total, commission_total, dos.mirraw_shipping_cost, final_payout, today, "Normal Payment"]
        else
          payout_vlaue = dos.gst_status == "release"  && payout_management.gst_release_ids.split(',').map(&:to_i).include?(dos.id)  ? [0, dos.gst_tax, dos.payout] : [dos.gst_tax, 0, taxable_value]
          row += [dos.payout, *payout_vlaue, today, "GST HOLD and Normal Payment"]
        end
        csv << row
      end
      if payout_management.payout_type == "International"
        DesignerOrder.preload(:payment_order).where(id: payout_management.gst_release_ids.to_s.split(", ")).each do |dos|
          csv << [dos.invoice_number, dos.pickup.try(:strftime, "%d/%m/%Y"), dos.payment_order.try(:number), 0, 0, 0, 0, dos.gst_tax, dos.gst_tax, today, "GST Release"]
        end
      end
      Adjustment.where(id: payout_management.adjustment_ids.to_s.split(",")).preload([return_designer_order: :designer_order], :order).each do |adj|
        row = Array.new
        rdo = adj.return_designer_order
        dos = rdo.try :designer_order
        row += dos ? [dos.invoice_number, dos.pickup.try(:strftime, "%d/%m/%Y"), dos.order.number] : Array.new(3)
        if payout_management.payout_type == "Domestic"
          row += [(adj.amount / 1.18).round(2), ((adj.amount / 1.18) * 0.18).round(2), 0, 0, 0, 0, 0, adj.amount]
        elsif rdo && dos && dos.gst_status == "release"
          row += [(adj.amount + rdo.gst_tax).round(2), (-1 * rdo.gst_tax).round(2), adj.amount, 0, 0, adj.amount]
        elsif rdo
          row += [(adj.amount + rdo.gst_tax).round(2), (-1 * rdo.gst_tax).round(2), adj.amount, (-1 * rdo.gst_tax).round(2), 0, (adj.amount + rdo.gst_tax)]
        else
          row += [(adj.amount / 1.18).round(2), (adj.amount - adj.amount / 1.18).round(2), adj.amount, 0, 0, adj.amount]
        end
        row += [today, adj.notes]
        csv << row
      end
    end
    aws_path = "payout_summary/#{payout_management.payout_version}/#{payout_management.designer_id}.csv"
    AwsOperations.create_aws_file(aws_path,file)
    AwsOperations.get_aws_file_path(aws_path)
  end
end
