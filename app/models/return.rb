require 'file_operation'

class Return < ActiveRecord::Base
  belongs_to :order
  belongs_to :user
  belongs_to :coupon
  has_many :refunds
  has_many :return_designer_orders
  has_many :reverse_shipments, through: :return_designer_orders
  has_many :tickets
  has_many :open_tickets, ->{ open_tickets }, class_name: 'Ticket'
  has_many :discount_line_items
  has_many :wallet_transactions
  attr_accessible :account_holder_name, :coupon_id, :account_number, :courier_name, :customer_phone_number, :adjustment, :bank_name, :order_number, :branch, :ifsc_code, :notes, :pay_type, :status, :total, :state, :coupon_code, :reason, :tracking_id, :origin_city, :designer_name, :product_id, :discount, :completed_on, :type_of_refund, :product_charge, :stitching_charge, :instruction_to_accounts, :coupon_gen_date, :did_you_try_retaining_customer, :shipping, :agent_info, :user_paypal_email,:refund_transaction_id, :user_id, :order_id, :service_type, :support_reason
  attr_accessor :other_bank_name,:transaction_id, :dos_state
  has_many :line_items, :through => :return_designer_orders
  has_many :designs, through: :line_items
  accepts_nested_attributes_for :return_designer_orders

  before_save :update_total_state
  after_create :update_state_for_return

  has_shortened_urls

  include JuspayApi
  include PayuApi
  include MobileGenericSidekiqConcern

  RETURN_REASON_ARRAY = ["Wrong Product", "Damaged Product", "Print /Design /Color Mismatch"]


  def create_credit_note_for_return
    shipment_hash = get_shipment_details(self.courier_name)
    shipment = self.order.shipments.where(shipment_state: 'delivered',designer_order_id: nil).first
    product_details,items_price_total = shipment.package_details(shipment,shipment_hash["item"],self.class.to_s)
    return_credit_note = ShipmentDelivery::CreditNoteInvoice.new(product_details,self.order.number,shipment.number,items_price_total,self.class.to_s,self.id)
    return_credit_note.generate_credit_note_invoice
  end

  class << self
    def run_callbacks_delayed(id)
      Return.find_by_id(id).try(:touch)
    end

    def get_return_voucher
      date = Time.current
      month = date.strftime("%m")
      year = date.strftime("%y")
      uniq_constant = SystemConstant.where(name: 'UNIQ_RETURN_VOUCHER').first
      voucher_no = uniq_constant.value.to_i
      uniq_constant.value = (voucher_no + 1)
      uniq_constant.save!
      if %w(01 02 03).include?(month)
        date = "MIR/#{voucher_no}/#{month}/#{year.to_i-1}#{year}"
      else
        date = "MIR/#{voucher_no}/#{month}/#{year}#{year.to_i+1}"
      end
      date
    end

    def send_initiate_return_mail(id)
      rtn = Return.find(id)
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("ReturnMailer", nil, "initiate_return", {"#{rtn.class}": rtn.id})
      #ReturnMailer.sidekiq_delay.initiate_return(rtn)
    end

    def verify_return_details(numbers,email)
      all_returns,row,attachments = [],[],{}
      row += ['Order Number','Order_State','Order Total','Order Paid','Order created at','No of returns','Order_Pay_type','Transaction ID','Designer Order Status','Shipment Status','Product_State','Internal Discount','Internal Discount amount']
      rtn_attrs = %w(id discount pay_type state type_of_refund agent_info reason notes created_at pending_on account_holder_name bank_name branch)
      row += rtn_attrs + ['IFSC Code','Account Number','Coupon_code','Used','Expired','Applied_On','Order Notes','Reasons for not valid']
      all_returns << row
      Order.where(number: numbers).preload(:export_shipments,:events,[outbound_coupons: :coupon_used_on],returns: [:coupon,[discount_line_items: [line_item: :designer_order]],line_items: [designer_order: :shipment]]).find_in_batches(batch_size: 400) do |orders_grp|
        orders_grp.each do |order|
          order_reasons,order_detail = order.return_order_details
          li_status,dos_status = if order.pay_type == COD
            [['buyer_return'],['buyer_returned']]
          else
            [['buyer_return','cancel'],['buyer_returned','canceled','vendor_canceled','rto']]
          end

          order.returns.each do |return_order|
            row,reasons=[],[]
            reasons << order_reasons
            reasons = return_order.check_if_return_is_correct(reasons)
            status_update,reasons = return_order.validate_designer_order_states(order,reasons,(order.pay_type == COD ? true : false),false)
            coupons_data = order.get_return_coupon_data(order.outbound_coupons.select{|c| c.return_id == return_order.id})

            row += (order_detail + status_update)
            row += return_order.attributes.values_at(*rtn_attrs)
            row += ['="'+return_order.ifsc_code.to_s+'"','="'+return_order.account_number.to_s+'"',coupons_data['code'],coupons_data['used'],coupons_data['expired'],coupons_data['apply'],order.notes,reasons.flatten.join(".\n")]
            all_returns << row
          end
        end
      end
      file = CSV.generate do |csv|
        all_returns.each{ |row| csv << row}
      end
      attachments["Order_and_return_verification_Data_#{Time.current.strftime('%d %b %y')}.csv"] = file
      OrderMailer.report_mailer("Order and Returns verification Data #{Time.current.strftime('%d %b %y')}",'Please check following csv file',{'to_email'=>email,'from_email_with_name'=> "Mirraw.com <<EMAIL>>"},attachments).deliver
    end

    def read_file(file)
      if file.content_type == "text/csv"
        file_text = File.read(file.path)
      else
        xlsx = Roo::Spreadsheet.open(file.path, extension: :xlsx)
        file_text = xlsx.to_csv
      end
      file_text = StringModify.string_utf8_clean_without_space(file_text) unless file_text.is_utf8?
      filedata = CSV.parse(file_text)
      filedata
    end

    def get_refund_details_for_orders(filedata,email)
      order_details,rates_according_to_currency_code,order_ids,currency_codes={},{},[],Set.new()
      filedata.each do |row|
        number = row[0].try(:strip)
        order_details[number] = (order_details[number]||=[]) + row[1].strip.split("/") if number.present? && row[1].present?
      end
      orders = Order.where(number: order_details.keys).preload(:line_items,:returns,:events).group_by(&:number)
      orders.each do |key,value|
        order =  value.first
        order_ids << order.id
        currency_codes << order.currency_code
      end  
      coupon_data = Coupon.where(source_order_id: order_ids).group_by(&:source_order_id)
      rates = CurrencyConvert.select('symbol,rate,paypal_rate').where(symbol: currency_codes.to_a)
      rates.map{|rate| rates_according_to_currency_code[rate.symbol] = [rate.rate,rate.paypal_rate]} 
      refund_data_hash = []
      order_details.each do |order_number, product_codes_array|
        order = orders[order_number].first if orders[order_number].present?
        begin
          if order.present?  
            order_returns = order.returns.select{|r| (r.product_id.to_s.split('/') & product_codes_array).present?}
            currency_code = order.currency_code
            paypal_supported_currency = (Order::PAYPAL_ALLOWED_CURRENCIES).include?(currency_code) ? true : false
            design_ids = order.line_items.collect(&:design_id).map(&:to_s)
            total_discount = order_returns.collect(&:total).sum
            returned_products = order_returns.collect(&:product_id).map{|pid| pid.split('/') if pid.present?}.flatten
            invalid_design_ids = product_codes_array - design_ids
            non_returned_products =  product_codes_array - returned_products
            coupons,line_item_states = [],[]
            order_coupons = coupon_data[order.id]
            (order_coupons||=[]).each do |c|
              use_flag = c.use_count >= c.limit ? 'Y' : 'N'
              coupons << [c.name, c.flat_off, use_flag]
            end
            duplicate_orders = Order.find_child_duplicate_orders(order)
            refund_amounts = []
            return_line_items = order.line_items.select{|item| returned_products.include? item.design_id.to_s}
            return_line_items.each do |item|
              line_item_states << "#{item.design_id} - #{item.status}"
              if paypal_supported_currency
                currency_price = (item.snapshot_price.to_f * item.quantity).to_f/order.currency_rate.to_f
              else  
                currency_price = (item.snapshot_price.to_f * item.quantity).to_f/rates_according_to_currency_code[currency_code][0].to_f
              end
              final_price = paypal_supported_currency ? "#{currency_code} #{currency_price.round(2)}" : "USD #{(currency_price * rates_according_to_currency_code[currency_code][1]).round(2)}"
              refund_amounts << [item.design_id, final_price]
            end
            refund_data_hash << [order_number,order.paypal_txn_id,product_codes_array,total_discount,invalid_design_ids,non_returned_products,line_item_states,coupons,duplicate_orders,refund_amounts,order.notes]  
          end
        rescue
          refund_data_hash << [order_number,"ORDER DETAILS COULD NOT BE FETCHED"]
        end  
      end
      csv_file = CSV.generate do |csv|
        csv << ['Order Number','Paypal TXN ID','Return Products','Total Discount','Invalid Prodcuts','Non Returned Products','Line Item States','Coupons','Duplicate Orders','Refund Amounts','Order Notes']
        refund_data_hash.each do |row|
          csv << row
        end  
      end
      emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'} 
      OrderMailer.report_mailer('Order Data For Refund Query.csv','Please Find Attachment.',emails,{'Order Data For Refund'=>csv_file}).deliver
    end
    
    def paypal_automated_refund(filedata,email,automated: false,type: 'paypal')
      designer_orders,valid_returns,invalid_returns = {},[],[]
      if automated
        return_ids = filedata
      else
        return_ids = []
        filedata.each{|row| return_ids << row[0].to_i  }
      end
      returns =  Return.where(id: return_ids).preload(order: [:tags,:events], line_items: [designer_order: :line_items])
      return_ids.each do |return_id|
        return_obj = returns.select{|r| r.id == return_id}.first
        currency_code,currency_rate = '',1.0
        if return_obj.present?
          order = return_obj.order
          order_tags = order.tags.collect{|t| t.name.try(:downcase)}
          order_notes = order.notes.try(:downcase)
          if (state_err = return_obj.state == 'pending_payment') && (tag_err = order_tags.exclude?('dnr')) && (dup_note_err = order_notes.exclude?('replacement')) && (dup_note_err = order_notes.exclude?('duplicate'))
            if type == 'paypal'
              return_currency_code,paid_currency_rate,paypal_mc_gross = order.paid_currency_code,order.paid_currency_rate.to_f,order.paypal_mc_gross
            else
              return_currency_code, actual_return_amount = ['INR', return_obj.discount]
            end
            reasons = []
            if (txn_id = order.get_payment_transaction_id.try(:values).try(:first)).present?
              status,reasons = return_obj.validate_designer_order_states(order,reasons)
              reasons = Return.validate_return_pay_type(return_obj,reasons,type)
              actual_return_amount, reasons = Return.get_refund_amount_for_paypal(return_obj.discount.to_f,paypal_mc_gross,paid_currency_rate,reasons) if !automated || type == 'paypal'
              if reasons.compact.flatten.blank?
                valid_returns << [return_id,return_currency_code,actual_return_amount,txn_id,order.number,return_obj.discount,return_obj.product_id]
              else
                invalid_returns << [return_id,return_currency_code,actual_return_amount,txn_id,order.number,return_obj.discount,return_obj.product_id,reasons]
              end
            else
              invalid_returns << [return_id,order.number,"#{type} Txn Id Not Present"]
            end
          else
            invalid_returns << [return_id,'','Return State Is Not Valid'] unless state_err
            invalid_returns << [return_id,'','Order have dnr tag'] unless tag_err
            invalid_returns << [return_id,'','Order has notes replacement or duplicate'] unless dup_note_err
          end
        else
          invalid_returns << [return_id,'','Return Not Found']
        end
      end
      report_invalid_returns(invalid_returns,email,valid_returns,automated: automated, type: type) if (invalid_returns.present? || valid_returns.present?)
      if automated
        Return.paypal_automated_refund_process(valid_returns,email,false,automated: automated, type: type)
      end
    end

    def paypal_automated_refund_process(filedata,email,forced,automated: false, type: 'paypal')
      return_data,valid_returns,invalid_data = {},[],[]
      filedata.each do |row|
        if row.length == 7
          return_data[row[0]] = [row[0],row[1],row[2],row[3],row[4],row[5]]
        else
          invalid_data << [row[0],'Incomplete Data']
        end
      end
      returns = Return.select('id,discount').where(id: return_data.keys)
      return_data.each do |key,value|
        if value[2].to_f <= returns.select{|r| r.id == key.to_i}.first.try(:discount).to_f
          valid_returns << value
        else
          invalid_data << [value[0],'Refund Total Do Not Match With Discount.']
        end  
      end
      intitate_int_dom_refund_process(valid_returns,email,forced,automated: automated, type: type) if valid_returns.present?
      report_invalid_returns(invalid_data,email) if invalid_data.present? && !automated
    end

    def update_cashgram_refund_status(cashgram_params)
      if (rfd = Refund.find_by_refund_mode_id(cashgram_params['cashgramId'])).present? && (rtn = rfd.return).present?
        rfd.update_column(:status, cashgram_params['event'])
        case rfd.status
        when 'CASHGRAM_REDEEMED'
          rtn.payment_made! if rtn.can_payment_made?
        when 'CASHGRAM_EXPIRED'
          rtn.order.add_notes_without_callback(cashgram_params['reason'], "Automated COD refund")
        end
      end
    end

    def validate_return_pay_type(return_obj,reasons,type)
      total = return_obj.total > return_obj.shipping.to_i ? return_obj.total + return_obj.shipping.to_i : return_obj.total
      reasons << 'Type of Return Is Not Refund' unless return_obj.type_of_refund.to_s.downcase == 'refund'
      reasons << "Paytype Is Not #{type}" if return_obj.pay_type.to_s.downcase != type && return_obj.order.payment_gateway.to_s != type
      reasons << 'Refund Total Do Not Match With amount to be refunded.' if return_obj.discount > total
      if (coupon_code = return_obj.coupon_code).present?
        coupon = Coupon.where(code: coupon_code).first
        if coupon.present?
          reasons << 'Return Have Used Coupon' if coupon.use_count > 0
          reasons << 'Return Have Live Unused Coupon' if coupon.live?
        end
      end
      reasons
    end

    def get_refund_amount_for_paypal(discount,paypal_mc_gross,paid_currency_rate,reasons)
      return_paypal_amount = 0.0
      if paypal_mc_gross.present?
        paid_currency_rate <= 0 ? reasons << "Paid Currency Rate Not Valid : #{paid_currency_rate.to_f}": return_paypal_amount = discount / paid_currency_rate
      else
        reasons << "Paypal MC Gross Amount Not Present"
      end  
      return return_paypal_amount.round(2), reasons
    end

    def intitate_int_dom_refund_process(valid_returns,email,forced=false, automated: false, type: 'paypal')
      if automated
        balance = type == 'paypal' ? SystemConstant.where(name: 'REMAINING_PAYPAL_REFUND_BALANCE').first : SystemConstant.where(name: 'REMAINING_DOMESTIC_REFUND_BALANCE').first
        balance_amount = JSON.parse(balance.value)
        if DateTime.now.day > balance.updated_at.day
          per_day_balance = balance_amount['PER_DAY_LIMIT'].to_f
        else
          per_day_balance = balance_amount['PER_DAY_BALANCE'].to_f
        end
        if DateTime.now.month > balance.updated_at.month
          per_month_balance = balance_amount['PER_MONTH_LIMIT'].to_f
        else
          per_month_balance = balance_amount['PER_MONTH_BALANCE'].to_f
        end
        per_order_value = type == 'paypal' ? SystemConstant.where(name: 'PAYPAL_REFUND_ORDER_LIMIT').first.try(:value).to_f : balance_amount['PER_ORDER_LIMIT'].to_f
      end
      refund_successful,refund_unsuccessful = {},[]
      valid_returns.each do |return_data|
        if !automated || (per_month_balance >= return_data[5] && per_order_value >= return_data[5] && per_day_balance >= return_data[5])
          response = case type
            when 'paypal'
              paypal_try_refund(return_data[3],return_data[1],return_data[2],"Refund For #{return_data[4]} : #{return_data[0]}",forced)
            when 'payu'
              payu_try_refund(return_data[3], return_data[2], return_data[0])
            when 'razorpay'
              razorpay_try_refund(return_data[3], return_data[2], return_data[0])
            end
          if response[0] == 'success'
            refund_successful[return_data[0].to_i] = [response[1],response[2]]
            if automated
              per_month_balance = per_month_balance - return_data[5]
              per_day_balance   = per_day_balance - return_data[5]
            end
          else
            refund_unsuccessful << [return_data[0],response[1]]
          end
        else
          refund_unsuccessful << [return_data[0], 'Refund value too high to process through system.']
        end
      end
      if automated
        balance_amount['PER_DAY_BALANCE']   = per_day_balance
        balance_amount['PER_MONTH_BALANCE'] = per_month_balance
        balance.value = balance_amount.to_json
        balance.save
      end
      Return.where(id: refund_successful.keys).preload(:open_tickets).find_each do |return_obj|
        refund_data = refund_successful[return_obj.id]
        return_obj.refund_transaction_id = refund_data[0]
        return_obj.all_products_received if !automated && !return_obj.payment_complete? && return_obj.can_all_products_received?
        if return_obj.pending_payment? && return_obj.can_payment_made?
          return_obj.payment_made
          if (ticket = return_obj.open_tickets.first).present?
            ticket.resolve_automated(return_obj.refund_transaction_id, refund_data[1])
          end
        else
          refund_unsuccessful << [return_obj.id, 'Return not in valid state']
        end
      end
      success_file = CSV.generate{|csv| refund_successful.each{|id, record| csv << ([id] + record)}}
      failure_file = CSV.generate{|csv| refund_unsuccessful.each{|record| csv << record}}
      emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>','cc_email'=> ACCESSIBLE_EMAIL_ID['forced_paypal_refund_mail_recipient']}
      attachments = {'Completed Transactions.csv'=> success_file,'Failed Transaction.csv'=> failure_file}
      content, subject = if automated
        ["Please Find The Attachments For Successful and Failed #{type} Refunds. Transactions were initiated by System", "Automated #{type} Refund Report #{DateTime.now.strftime('%d %b %Y %H:%M')}"]
      else
        ["Please Find The Attachments For Successful and Failed #{type} Refunds. Transactions was requested by #{email}", "#{type} Refund Report #{DateTime.now.strftime('%d %b %Y %H:%M')}"]
      end
      subject = 'Forced Transaction Alert :' + subject if forced
      OrderMailer.report_mailer(subject,content,emails,attachments).deliver
    end

    def payu_try_refund(payu_id, amount, return_id)
      begin
        payu = Mirraw::Application.config.payu
        url = Rails.env.admin? || Rails.env.production? ? payu[:payu_web_service_production_url] : payu[:payu_web_service_test_url]
        secure_hash = Digest::SHA512.hexdigest "#{payu[:merchant_key]}|cancel_refund_transaction|#{payu_id}|#{payu[:salt]}" #(key|command|var1|salt)
        response = HTTParty.post(url, headers: {'Content-Type'=> 'application/x-www-form-urlencoded'}, body: {key: payu[:merchant_key], command: 'cancel_refund_transaction', hash: secure_hash, var1: payu_id, var2: return_id, var3: amount}).parsed_response
        if response.present? && (response = JSON.parse(response).try(:deep_symbolize_keys)).present?
          if response[:status] == 1
            ['success', response[:request_id], "#{amount} INR"]
          else
            ['failed', response[:msg]]
          end
        else
          ['failed', 'Something Went Wrong']
        end
      rescue => e
        ['failed', e.message]
      end
    end

    def razorpay_try_refund(razorpay_id, amount, return_id)
      begin
        response = if (refund = RazorpayGateway.create_refund(razorpay_id, amount, return_id)).present? && refund.entity == 'refund' && refund.id.present?
          ['success', refund.id, "#{refund.amount/100} #{refund.currency}"]
        else
          ['failed', 'Amount Already Refunded']
        end
      rescue => e
        ['failed', e.message]
      end
    end

    def paypal_try_refund(paypal_txn_id,return_currency_code,refund_amount,description,forced)
      begin
        sale = PayPal::SDK::REST::Sale.find(paypal_txn_id)  
        if sale.present?
          sale_state = sale.state
          if sale.state == 'refunded' && !forced
            response = ['failed',"Trasaction Id #{paypal_txn_id} Is Already Fully Refunded"]
          elsif sale.state == 'partially_refunded' && !forced
            response = ['failed',"Trasaction Id #{paypal_txn_id} Is Already Partially Refunded"]
          else
            refund = sale.refund({
              amount:{
                currency: return_currency_code,
                total: '%.2f' % refund_amount
              }
            })
            if refund.success? 
              response = ['success',refund.id, "#{refund.amount.total} #{refund.amount.currency}"]
            else
              error_message = (details = refund.error['details']).present? ? details.first['issue'] : refund.error['message']
              response = ['failure', error_message]
            end
          end  
        else
          response = ['failed',"Transaction Id #{paypal_txn_id} Was Not Found"]
        end  
        response
      rescue => e
        response = ['failed',e.message]
      end  
    end

    def report_invalid_returns(invalid_returns,email,valid_returns=[],automated: false, type: 'paypal')
      invalid_file = CSV.generate do |csv|
        csv << ['return_id','return_currency_code','return_amount','txn_id','order_number','discount','product_id','reason']
        invalid_returns.each  do |ir|
          csv << ir
        end 
      end
      if valid_returns.present?
        valid_file =  CSV.generate do |csv|
          csv << ['return_id','return_currency_code','return_amount','txn_id','order_number','discount','product_id']
          valid_returns.each{|ir| csv << ir}
        end
      end
      files = {'Wrong Return Data.csv'=> invalid_file} 
      files['Correct Refund Data.csv'] = valid_file if valid_returns.present?
      emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
      content, subject = if automated
        ["Following are the two files generated by system to initiate automated #{type} Refund Process. Please check the following Correct and Wrong Refund Data Sheet.", "Automated #{type} Return Data"]
      else
        ['Please Find The Attachments For Refund Data.', 'Return Data']
      end
      OrderMailer.report_mailer(subject, content, emails,files).deliver
    end

    def process_return_data(state,start_date,end_date,refund_type,pay_type,state_type,to_email)
      row, attachments = [], {}
      row += ['Order Number','Order_State','Order Total','Order Paid','Order created at','No of returns','Order_Pay_type','Transaction ID','Total No. of line_items', 'Order Tags','Designer Order Status','Shipment Status','Product_State','Internal Discount','Internal Discount amount']
      rtn_attrs = %w(id discount pay_type state type_of_refund agent_info reason notes created_at pending_on designer_name product_id account_holder_name bank_name branch)
      row += rtn_attrs + ['IFSC Code','Account Number','Return_Designer_Total','Coupon_code','Used','Expired','Applied_On','Order Notes']
      row += ['Reasons for not valid'] if state != 'payment_complete'
      
      FileOperation.temp('csv') do |tfile|
        CSV.open(tfile, 'a+') do |csvtfile|
          csvtfile << row
          
          Return.where(created_at: start_date.beginning_of_day..end_date.end_of_day).where(state_type).where(refund_type).where(pay_type).order(:created_at)
          .includes(:return_designer_orders,line_items: :designer_order,order: [:tags,:events,:line_items,outbound_coupons: :coupon_used_on]).find_in_batches(batch_size: 500) do |returns|
            returns.each do |return_order|
              row,discount_state,discount_amount,reasons=[],'','',[]
              order = return_order.order
              if order.present?
                order_reasons,order_detail = order.return_order_details
                li_status,dos_status = if order.pay_type == COD
                  [['buyer_return'],['buyer_returned']]
                else
                  [['buyer_return','cancel'],['buyer_returned','canceled','vendor_canceled','rto']]
                end
                reasons << order_reasons
                reasons = return_order.check_if_return_is_correct(reasons)
                status_update,reasons = return_order.validate_designer_order_states(order,reasons,(order.pay_type == COD ? true : false),false)
                coupons_data = order.get_return_coupon_data(order.outbound_coupons.select{|c| c.return_id == return_order.id})
                
                row += (order_detail + [order.line_items.length, order.tags.to_a.join(', ')] + status_update)
                row += return_order.attributes.values_at(*rtn_attrs)
                row += ['="'+return_order.ifsc_code.to_s+'"','="'+return_order.account_number.to_s+'"',return_order.return_designer_orders.to_a.map(&:total).compact.sum,coupons_data['code'],coupons_data['used'],coupons_data['expired'],coupons_data['apply'],order.notes]
                row += [reasons.flatten.join(", ")] if state != 'payment_complete'
                csvtfile << row
              end
            end
          end
        end
        
        FileOperation.split(tfile, size: 8.megabytes, preserve_lines: true) do |sfile, file_no|
          attachments["Returns-Data--(#{start_date.strftime('%d/%m/%y')}-#{end_date.strftime('%d/%m/%y')})--Page-No-#{file_no}.csv"] = sfile.read
          OrderMailer.report_mailer("Returns Bulk Data (#{start_date.strftime('%d/%m/%y')}-#{end_date.strftime('%d/%m/%y')}) Page No #{file_no}",'Please check following csv file',{'to_email'=>to_email,'from_email_with_name'=> "Mirraw.com <<EMAIL>>"},attachments).deliver
        end
      end
    end

    def update_payu_refund_status(id)
      if (refund = Refund.find id).present?
        return true unless refund.payu_pending?
        return1 = refund.return
        fresh_response = return1.payu_track_refund(refund.refund_mode_id)
        if fresh_response.first.downcase == 'success'
          refund_status = fresh_response.second[:"#{refund.refund_mode_id}"][:status]
          if refund_status.downcase == 'success'
            return1.update_column(:refund_transaction_id, fresh_response.second[:"#{refund.refund_mode_id}"][:bank_ref_num]) if fresh_response.second[:"#{refund.refund_mode_id}"][:bank_ref_num].present?
            refund.update_column(:status,refund_status.upcase)
            return1.payment_made! if return1.can_payment_made?
            return1.order.add_notes_without_callback("Payu Refund Processed transaction_id: #{return1.refund_transaction_id}", "Automated JUSPAY refund")
            phone = Order.get_mobile_num(return1.order.phone)
            return1.notify_customer_refund_processed(phone,return1.order.number, fresh_response.second[:"#{refund.refund_mode_id}"][:amt].to_f) if phone.present?
          elsif refund_status.downcase == 'queued'
            refund.update_column(:status,'PENDING') if refund.status != 'PENDING'
            Return.sidekiq_delay_until(30.minutes.from_now, queue: 'high')
                  .update_payu_refund_status(refund.id)
          elsif refund_status.downcase == 'failure'
            refund.update_column(:status,refund_status.upcase)
            return1.order.add_notes_without_callback("Payu Refund Failed", "Automated PAYU refund")
          end
        else
          ExceptionNotify.sidekiq_delay.notify_exceptions("Payu Refund error", "Payu Refund error",{params: {order_number: return1.order.number, refund: refund.id, amount: return1.discount.to_i,  payu_response: fresh_response.second}})
        end
      end
    end

    def update_juspay_refund_status(id)
      if (return1 = Return.find id).present?
        return true unless (refund = return1.refunds.juspay_pending.last).present?
        fresh_response = return1.track_refund
        juspay_uniq_id = "#{return1.order.number}-#{return1.id}"
        if fresh_response['error'].nil? && fresh_response['status'] == 'CHARGED'
          refund_response = fresh_response['refunds'].select{|a|a['unique_request_id']== juspay_uniq_id}.last
          refund_status = refund_response['status']
          if refund_status == 'SUCCESS' 
            return1.update_column(:refund_transaction_id, refund_response['ref']) if refund_response['ref'].present?
            refund.update_column(:status,refund_status)
            return1.payment_made! if return1.can_payment_made?
            return1.order.add_notes_without_callback("Juspay Refund Processed transaction_id: #{return1.refund_transaction_id}", "Automated JUSPAY refund")
            return1.notify_customer_refund_processed(fresh_response['customer_phone'],fresh_response['order_id'], refund_response['amount']) if fresh_response['customer_phone'].present?
          elsif refund_status == 'PENDING'
            refund.update_column(:status,refund_status) if refund.status != 'PENDING' 
            return1.order.add_notes_without_callback("Juspay Refund Still Pending, Action Required", "Automated JUSPAY Refund") if refund.status != 'PENDING'
            Return.sidekiq_delay_until(30.minutes.from_now, queue: 'high')
                  .update_juspay_refund_status(return1.id)
          elsif refund_status == 'FAILURE'
            refund.update_column(:status,refund_status)
            return1.order.add_notes_without_callback("Juspay Refund Failed", "Automated JUSPAY refund")
          end
        end
      end
    end
  end

  def eligible_for_juspay_auto_refund?
    AUTOMATED_JUSPAY_REFUND['enable'] && discount.to_f.between?(1,AUTOMATED_JUSPAY_REFUND['max_refund_amount'].to_f) && (order.juspay_txn_id.present? || order.attempted_payment_gateway == 'Juspay') && order.prepaid? && order.domestic? && refunds.juspay.blank?
  end

  def eligible_for_payu_auto_refund?
    AUTOMATED_PAYU_REFUND['enable'] && discount.to_i.between?(1,AUTOMATED_PAYU_REFUND['max_refund_amount']) && order.payu_mihpayid.present? && order.payment_gateway == 'payu' && order.prepaid? && order.domestic? && refunds.payu.blank?
  end

  def create_payu_refund
    refund_uniq_id = "#{self.order.number}-#{self.id}"
    amount = self.discount.to_f
    payu_id = self.order.payu_mihpayid
    payu_response = self.payu_refund_initiation(payu_id, refund_uniq_id, amount)
    if payu_response.first == 'success'
      self.refunds.create(refund_mode_id: payu_response.second, status: 'PAYU_INITIATED').payu!
      self.order.add_notes_without_callback('Payu Refund Created', "Automated PAYU refund")
      refund = self.refunds.last
      Return.sidekiq_delay_until(30.minutes.from_now, queue: 'high')
            .update_payu_refund_status(refund.id)
    else
      self.order.add_notes_without_callback('Payu Refund not Created', payu_response.second)
      ExceptionNotify.sidekiq_delay.notify_exceptions("PAYU REFUND API Error", payu_response.to_s,{params: {order_number: self.order.number, amount: self.discount.to_i,  juspay_response: payu_response}})
    end
  end

  def create_juspay_refund
    juspay_response = self.create_refund
    juspay_uniq_id = "#{self.order.number}-#{self.id}"
    if juspay_response['error'].nil? && juspay_response['status'] == 'CHARGED'
      response_refund = juspay_response['refunds'].select{|a|a['unique_request_id']== juspay_uniq_id}.last
      self.refunds.create(refund_mode_id: juspay_uniq_id, status: 'JUSPAY_INITIATED').juspay!
      self.order.add_notes_without_callback('Juspay Refund Created', "Automated JUSPAY refund")
      Return.sidekiq_delay_until(30.minutes.from_now, queue: 'high')
            .update_juspay_refund_status(self.id)
    elsif juspay_response['error'].present?
      self.order.add_notes_without_callback('Juspay Refund not Created', "Automated JUSPAY refund")
      self.order.add_notes_without_callback(juspay_response.to_s, "Automated Juspay Refund Response")
      ExceptionNotify.sidekiq_delay.notify_exceptions("Cashgram API Error", juspay_response.to_s, {params: {order_number: self.order.number, amount: self.discount.to_i,  juspay_response: juspay_response}})
    end
  end

  def notify_customer_refund_processed(refund_phone_number, order_number, amount)
    template = "Refund Initiated : Amount for #{amount.round(2)}... of Order No #{order_number} and will get refunded in 5-7 business days."
    SmsNotification::NotificationService.notify_later(refund_phone_number, template)
  end

  def automate_cod_refund?
    AUTOMATED_COD_REFUND['enable'] && order.cod? && order.domestic? && discount <= AUTOMATED_COD_REFUND['max_refund_amount'].to_f && (other_dos_states = reverse_shipments.where.not(shipment_state: ['canceled', 'delivery_exception', 'pickup_exception']).pluck(:shipment_state).uniq).present? && (other_dos_states - ['in_transit', 'out_for_delivery', 'delivered']).blank?
  end

  def automate_cod_refund_admin?
    AUTOMATED_COD_REFUND['enable'] && order.cod? && order.domestic?
  end

  def check_if_return_is_correct(reasons = [])
    reasons << 'Return is not in pending payment state' unless self.pending_payment?
    reasons << 'Return Pay Type is not present.' unless self.pay_type.present?
    reasons << 'Bank Details not present.' if self.type_of_refund == 'Refund' && [COD,BANK_DEPOSIT,GHARPAY].include?(self.pay_type.to_s) && self.account_number.blank?
    reasons << 'Amount to be refunded is not present.' unless self.discount.present?
    reasons
  end

  def validate_designer_order_states(order,reasons = [],cod=false,store=true)
    li_status,dos_status = if cod
            [['buyer_return'],['buyer_returned']]
          else
            [['buyer_return','cancel'],['buyer_returned','canceled','vendor_canceled','rto']]
          end
    product_state,dos_state,shipment_state,discount_state,discount_amount,no_discounts = '','','','','',(!self.discount_line_items.present? ? true : false)
    all_dos_ids = self.line_items.collect(&:designer_order_id) unless store
    all_items = self.line_items.presence || self.discount_line_items.collect(&:line_item).presence || []
    all_items.each_with_index do |li,index|
      if (dos = li.designer_order).blank?
        reasons << "DesignerOrder not found"
        next
      end
      reasons << "#{li.design_id} Product Id State : #{li.status} || #{dos.id} Designer Order State : #{dos.state}" if (li_status.exclude?(li.status) && dos_status.exclude?(dos.state) && no_discounts)
      unless store
        product_state += "\n" if index > 0
        dos_state += "\n" if index > 0
        product_state += (li.design_id.to_s + ' - ' + li.status.to_s)
        dos_state += (dos.id.to_s + ' - ' + dos.state.to_s)
        if no_discounts && all_dos_ids.include?(dos.id) && cod && ['cancel','cancel_complete'].exclude?(order.state)
          state = dos.shipment.try(:shipment_state)
          all_dos_ids -= [dos.id]
          reasons << "Shipment of #{dos.id} is not delieverd" if state != 'delivered'
          shipment_state += (dos.id.to_s + ' - ' + state.to_s)
          shipment_state += "\n"
        end
      end
    end
    if order.international? && !store && no_discounts && ['cancel','cancel_complete'].exclude?(order.state)
      shipment_state = order.export_shipments.map(&:shipment_state).compact.uniq
      reasons << "Shipment is not yet delieverd" if shipment_state.exclude?('delivered')
    end
    unless store
      self.discount_line_items.each_with_index do |dli,index|
        discount_state += "\n" if index > 0
        discount_amount+= "\n" if index > 0
        discount_state += (dli.line_item.design_id.to_s + ' - ' + dli.discount_percent.to_s + '% on ' + dli.discount_on.to_s)
        discount_amount += dli.price.to_s
      end
    end
    return [dos_state,shipment_state,product_state,discount_state,discount_amount],[reasons]
  end

  def self.create_user_automated_refund(return_params, line_item)
    unless line_item.return_id.present?
      return_order = Return.new(return_params)
      order = return_order.order
      line_item.update_columns(return_reason: return_order.reason, return_quantity: line_item.quantity)
      rdo = return_order.return_designer_orders.build(designer_order_id: line_item.designer_order_id, designer_id: line_item.design.designer_id, line_item_ids: line_item.id)
      item_price = line_item.discount_price_with_addons
      gst_rate = line_item.purchase_gst_rate.presence || line_item.find_hscode_gst(item_price)[1]
      rdo.gst_tax = item_price - item_price / (1 + gst_rate/100.0)
      if return_order.save && return_order.discount > 0
        Coupon.validate_and_create_coupon({name: order.name, start_date: Time.current, end_date: 1.months.from_now, limit: 1, coupon_type: 'COFF', percent_off: 0, use_count: 0, notified: 0, min_amount: 0, flat_off: return_order.discount, coupon_reason: return_order.reason}, order.id, return_order) if return_order.type_of_refund == 'Coupon'
        rdo.product_canceled
      end
    end
  end

  def get_referral_amount
    amount = 0.0
    order = self.order
    if (discount = order.referral_discount).present? && discount > 0
      if order.line_items.count == self.line_items.count
        amount += order.referral_discount
      else
        self.line_items.each do |line_item|
          amount += line_item.return_referral_amount
        end
      end
    end
    return amount
  end

  def get_refund_amount(referral_amount)
    rate = self.order.currency_rate
    ((self.discount - referral_amount*rate)/rate).round(2)
  end

  def update_state_for_return
    line_items = self.line_items
    if self.app_source.present?
      line_items.each do |item|
        if (['Print /Design /Color Mismatch','Damaged Product'].include? item.return_reason) || (item.return_reason.include?('Wrong Product') && item.snapshot_country_code != 'IN')
          self.sent_for_image_approval_on = Time.current
          self.state = 'require_image_approval'
          break
        end
      end
    end
    self.total,self.discount = self.calculate_discounted_total
    self.save!
    # return1.sidekiq_delay.send_return_update_sms('pending') if self.app_source.present? && self.reverse_pickup?
    SidekiqDelayGenericJob.perform_async(return1.class.to_s, return1.id, "send_return_update_sms", 'pending') if self.app_source.present? && self.reverse_pickup?
    SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{self.class}": self.id}, 'open', dos_state) if self.app_source.present? || line_items.present?
    #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(self,'open') if self.app_source.present? || line_items.present?
  end

  def get_return_label
    ActiveRecord::Associations::Preloader.new.preload(self, [line_items: :design])
    pdf_content = ActionController::Base.new.render_to_string(
      template:  '/returns/return_package_label',
      layout:  false,
      locals: {:@return => self},
    )
    return_label = WickedPdf.new.pdf_from_string(pdf_content,{orientation: 'Landscape'})
  end

  def reverse_pickup?
    service_type == 'RVP'
  end

  def create_reverse_pickup_shipment
    click_post = ClickPostAutomation.new(order, self)
    click_post.create_reverse_pickup
  end

  def calculate_discounted_total
    total_tax, total,refund_amount= 0,0,0
    if (rdos = self.return_designer_orders).present?
      rdos.each do |rdo|
        total += rdo.total
        total_tax += rdo.get_total_return_tax_amount
      end
      order = self.order
      if (['cancel','cancel_complete'].include? order.state)
        shipping_cost = (order[:notes].present? && order[:notes].include?('system_shipping_cost')) ? order[:notes].split(/system_shipping_cost/)[1][/\d+/].to_i : 0
        shipping_cost += order.express_delivery if order.express_delivery?
        order_addon_cost = (order[:notes].present? && order[:notes].include?('order_addon_cost')) ? order[:notes].split(/order_addon_cost/)[1][/\d+/].to_i : 0
        refund_discount = self.type_of_refund == 'Wallet' ? 0 : (order.refund_discount.to_f * order.currency_rate.to_f) #so that this amount is directly credit to user's wallet
        total -= total_tax
        total += order.total_tax
        self.shipping = shipping_cost 
        total = (total + shipping_cost + order_addon_cost - refund_discount).to_i
      end
      refund_amount = total
    elsif (discount_li = self.discount_line_items).present?
      discount_li.each do |dli|
        item = dli.line_item
        amount = (item.snapshot_price * (dli.quantity.to_i > 0 ? dli.quantity : 1))
        total += amount
        addons = item.line_item_addons
        addons.each {|addon| total += (addon.snapshot_price * (dli.quantity.to_i > 0 ? dli.quantity : 1))} if addons.present? && ((['cancel','cancel_complete'].include? self.try(:order).try(:state)) || (['canceled','vendor_canceled'].include? item.designer_order.state) || item.status == 'cancel')
        
        refund_amount += dli.price
      end
    else
      total = refund_amount = (self.shipping || self.stitching_charge)
    end
    line_item_tax = calculate_total_tax_refund
    total -= line_item_tax.to_i
    return [total,refund_amount] 
  end

  def calculate_total_tax_refund(include_total_tax = true)
    total_tax, total, item_count = 0, 0, 0 
    order = self.order  
    if (rdos = self.return_designer_orders.preload(:line_items)).present?
      rdos.each do |rdo|
        item_count += rdo.line_items.count
        total_tax += rdo.get_total_return_tax_amount
      end
      if (['cancel','cancel_complete'].include? order.state) && include_total_tax && order.line_items.count == item_count
        total_tax = order.total_tax
      end
    end
    return total_tax
  end

  def update_total_state
    order = self.order
    self.pay_type = ((order.pay_type == PAYMENT_GATEWAY && order.payment_gateway.present?) ? order.payment_gateway.titleize : order.pay_type) if self.created_at == nil
    self.pay_type = PAYPAL if self.pay_type.try(:downcase) == 'paypal'
    self.total,self.discount = self.calculate_discounted_total  if !self.new_record? && self.type_of_refund_changed?
    self.tax_charges = calculate_total_tax_refund
    true
  end

  def check_if_all_rdo_have_tracking
    self.return_designer_orders.each do |rdo|
      if rdo.tracking == false
        return false
      end
    end
    return true
  end

  def update_return_tracking_info
    self.tracking_id = ''
    self.courier_name = ''
    self.return_designer_orders.each_with_index do |return_designer_order, index|
      if return_designer_order.tracking_number.present? && return_designer_order.tracking_company.present?
        self.tracking_id += "/" if index !=0
        self.courier_name += "/" if index !=0
        self.tracking_id += "#{return_designer_order.tracking_number}"
        self.courier_name += "#{return_designer_order.tracking_company}"
      end
    end
    self.save!
  end

  def check_refund_details
    if self.pay_type.present?
      case self.pay_type
      when 'Cash Before Delivery'
        self.bank_name.present? and self.branch.present? and self.ifsc_code.present? and self.account_number.present? and self.account_holder_name.present?
      else
        true
      end
    else
      false
    end
  end

  def check_items
    pending_items, received_items = [],[]
    self.return_designer_orders.each do |rdo|
      if rdo.buyer_dispatched?
        pending_items << rdo
      elsif rdo.vendor_received? or rdo.completed?
        received_items << rdo
      end
    end
    return pending_items, received_items
  end

  def self.update_state_of_returns(event, return_ids, support_agent)
    returns = self.where(id: return_ids)
    self.update_state_and_send_mail(event, returns, support_agent)
  end

  def self.update_state_and_send_mail(event,returns,support_agent)
    freshdesk_api_url  = "https://mirraw.freshdesk.com/api/v2/agents?email=#{support_agent.email}"
    headers = {'Authorization' => Base64.encode64(FRESHDESK_API_KEY),'Content-Type' => 'application/json'}
    response = HTTParty.get(freshdesk_api_url, {headers: headers}).parsed_response
    agent = response.try(:first).present? ? response.first['id'] : nil
    returns.each do |return_order|
      if event == 'raise_ticket'
        return_order.create_freshdesk_ticket(agent)
      elsif event == 'bounce'
        return_order.update_column(:status, "#{return_order.status}. Bounce Mail Sent")
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return_order.class}": return_order.id}, 'bounced')
        #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return_order,'bounced')
      else
        if return_order.type_of_refund == 'Replacement' && event == 'all_products_received' && (return_order.status.to_s.exclude? 'New order')
          new_order_number = ''
          oos_items        = []
          status           = ''
          begin
            return_order.line_items.each do |item|
              design = item.design
              if new_order_number.blank?
                if design.quantity >= 1
                  new_order_number,order_note = return_order.order.duplicate_order(design.id)
                else
                  oos_items << design.id
                end
              else
                new_order ||= Order.where(number: new_order_number).first
                new_order.add_notes_without_callback("Duplicated for Replacement",'return',support_agent) if new_order.present? && (new_order.notes.exclude? 'Duplicated for Replacement')
                oos_items << design.id unless new_order.add_design(design)
              end
            end
            status = " New order - #{new_order_number}." if new_order_number.present?
            if oos_items.present?
              status += " Items not added - #{oos_items.join(' / ')}"
              return_order.post_to_freshdesk("Customer requested for replacement of #{return_order.order_number}",(status.to_s+' due to Out of Stock.'),agent)
            end
          rescue Exception
            status = 'New Order couldnt be created.'
          end
          return_order.update_column(:status, return_order.status.to_s + status)
        end
        return_order.return_approved_by = support_agent.id if event == 'all_products_received'
        return_order.fire_state_event(event.to_sym)
      end
    end
  end

  def post_to_freshdesk(subject,body,agent_id)
    return_order = self.order
    freshdesk_api_url  = "https://mirraw.freshdesk.com/api/v2/tickets"
    headers = {'Authorization' => Base64.encode64(FRESHDESK_API_KEY),'Content-Type' => 'application/json'}
    region = (return_order.country.downcase == 'india') ? "Domestic " : "International "
    json_payload = {status: 2,
                    priority: 1,
                    description: body,
                    subject: subject,
                    email: return_order.email,
                    responder_id: agent_id,
                    custom_fields: {followup: 'No', order_number: return_order.number,country_name: (([return_order.country.try(:strip)] & (Order::PRIORITY_COUNTRIES + Order::NON_PRIORITY_COUNTRIES)).first.presence || 'Other')}
                    }.to_json
    options = {headers: headers, body: json_payload}
    response = HTTParty.post(freshdesk_api_url,options).parsed_response
    if response['errors'].present?
      self.update_column(:status, self.status.to_s + response['errors']['message'].to_s)
    else
      self.update_column(:ticket_id, response['id'])
    end
  end

  def create_freshdesk_ticket(agent_id)
    subject     = "#{self.type_of_refund} Request for #{self.order_number} for products #{self.product_id}"
    mail_body = "Customer Want to return following products with reason as mentioned below: "
    self.line_items.each_with_index do |item,index|
      mail_body += "#{index+1}. #{item.design.id} - #{item.return_reason}.    "
    end
    mail_body += "Customer Notes: #{self.notes}. Return ID : #{self.id}"
    post_to_freshdesk(subject,mail_body,agent_id)
  end

  def self.sendout_return_mails(filedata,type)
    return_ids = {}
    filedata.each do |row|
      return_ids[row[0].try(:strip)] = row[1].try(:strip)
    end
    returns = Return.where(id: return_ids.keys)
    if type == 'Complete'
      returns.each do |return1|
        txn_id = return_ids[return1.id.to_s]
        return1.refund_transaction_id = txn_id  if txn_id.present? && txn_id != return1.order_number
        if return1.refund_transaction_id.present? && ticket = return1.tickets.where(state: ['open','working','reopen']).first
          ticket.resolve_message = "Ticket Auto Resolved By System Transaction ID: #{return1.refund_transaction_id}"
          ticket.resolved_at = Time.now
          ticket.resolved_by = nil
          ticket.resolve_ticket!
        else
          if return1.pending_payment?
            return1.payment_made
          elsif !return1.payment_complete?
            return1.all_products_received
            return1.payment_made
          end
        end
      end
    elsif type == 'Reject'
      returns.each do |return1|
        if return1.can_reject_payment? && return1.type_of_refund == 'Refund'
          return1.refund_rejected_on = Time.current
          return1.reject_payment!
          if (reason = return_ids[return1.id.to_s]).present? && (ticket = return1.tickets.where(state: ['reopen','open','working']).first) && ticket.can_reject_ticket?
            ticket.resolve_message = reason
            ticket.resolved_at = Time.now
            ticket.resolved_by = nil
            ticket.reject_ticket!
          end
        end
      end
    end
  end

  def self.return_ticket_process_flow(id,state)
    return1 = Return.where(id: id).first
    ReturnMailer.return_ticket_process_flow(return1,state).deliver
  end

  def self.send_tracking_info_to_vendor(id)
    rdo = ReturnDesignerOrder.where(id: id).first
    ReturnMailer.send_tracking_info_to_vendor(rdo).deliver
  end

  def self.create_freshdesk_ticket_for_mobile(id)
    return1 = Return.where(id: id).first
    return1.create_freshdesk_ticket
  end

  def do_wallet_transaction(order_id, user_id, wallet_id, referral_amount, return_amount)
    state = WalletTransaction.get_parent_state(order_id, user_id, wallet_id)
    response = false
    if ['order_complete', 'no_wallet_return'].include?(state)
      wt = WalletTransaction.where(order_id: order_id,user_id: user_id, return_id: id, wallet_id: wallet_id, referral_amount: referral_amount, return_amount: return_amount, state: state).first_or_initialize
      wt.state = state
      wt.save
      response = true
    end
    response
  end


    def get_shipment_details(courier_name=nil)
      index ,content_des, item_name = 0, nil, nil
      all_shipment_item_details = HashWithIndifferentAccess.new({'item' => {}})
      designer_orders = self.order.line_items.where(status: "buyer_return").map { |line_item| line_item.designer_order }.uniq
      designer_orders.each do |designer_order|
        dos_line_items = designer_order.line_items.where(status: "buyer_return", return_id: self.id).to_a
        dos_total      = designer_order.get_return_item_total
        cargo_designer_wise = dos_line_items.collect(&:design).flatten.compact.collect(&:designable_type).include?('Jewellery')
        dos_line_items.each do |line_item|
          design = line_item.design
          orignal_item = line_item.get_replacement_product
          is_valid_type = ['saree','kurta','kurti','salwarkameez','lehenga','jewellery','other','islamic'].include? design.designable_type.try(:downcase)
          item_name = (is_valid_type && INVOICE_TITLE_CHANGE.include?(courier_name) ? design.invoice_category_name(courier_name).titleize : design.categories.first.name)
          item_price = orignal_item.snapshot_price
          item_addon_price = orignal_item.line_item_addons.to_a.sum(&:snapshot_price)
          if courier_name == 'delhivery' && currency_rate_market_value.present?
            item_price = international_cod_price(orignal_item.snapshot_price)
            item_addon_price = international_cod_price(orignal_item.line_item_addons.to_a.sum(&:snapshot_price))
          end
          item_data = {
            item_id: line_item.id,
            designable_type: design.designable_type,
            cargo_designer_wise: cargo_designer_wise,
            hsn_code: design.categories.hsn_code,
            design_link_text: (design.title + '- #' + design.id.to_s),
            design_id: design.id,
            designer_id: design.designer_id,
            image_link: design.master_image.photo(:small),
            addon_price: item_addon_price,
            designer_discount: (orignal_item.snapshot_price * orignal_item.return_quantity * orignal_item.designer_order.discount.to_i/dos_total).round(2),
            name: item_name,
            quantity: orignal_item.return_quantity,
            weight: self.order.get_line_item_wise_weight(orignal_item.return_quantity).to_f,
            price: item_price,
            item_snapshot: orignal_item.snapshot_price,
            sku_id: (design.design_code.present? && design.design_code.length < 15) ? design.design_code : design.id.to_s,
            meis: line_item.elligible_for_meis?(courier_name),
            shipment_bucket_id: line_item.shipment_bucket_id
          }
          all_shipment_item_details['item'][index.to_s] = HashWithIndifferentAccess.new(item_data)
          index += 1
        end
      end
      all_shipment_item_details
    end
  #
  # new, pending, completed
  #
  #
  #
  def state_enum
    Return.state_machine.states.map &:name
  end

  def apply_full_refund_event
    order = self.order
    if %w(cancel cancel_complete).exclude?(order.state)
      order_total = order.total.to_f - (order.discount.to_f + order.additional_discount.to_f)
      if (order.returns.inject(0){|sum,rtn| (sum.to_f + rtn.discount.to_f) if ['pending_payment','payment_complete'].include?(rtn.state)}.to_f * 100/(order_total > 0 ? order_total : 1)) >= 90
        order.send(:order_quality_event!,:add,'FullRefund')
      end
      self.return_designer_orders.preload(line_items: :designer_order).each do |rdo|
        if rdo.buyer_dispatched? && (dos=rdo.line_items.collect(&:designer_order).flatten.uniq).present?
          dos.each do |d_order|
            d_order.send(:order_quality_event!,:add,'FullRefund') if d_order.buyer_returned?
          end
        end
      end
      items = (self.line_items.preload(:designer_order).presence || self.discount_line_items.preload(line_item: :designer_order).collect(&:line_item))
      items.each do |li|
        li.order_quality_event!(:add,'RefundOrDiscountInitiated')
        li.designer_order.order_quality_event!(:add,'RefundOrDiscountInitiated')
      end
    end
  end

  def credit_amount_to_wallet(referral_amount, return_amount, account=nil)
    user = self.user
    rtn_order = self.order
    if user.wallet.nil?
      wallet = Wallet.create(currency_convert_id: CurrencyConvert.find_by_country_code(rtn_order.actual_country_code).id)
      user.update_attribute(:wallet_id, wallet.id)
    else
      wallet = user.wallet
    end
    if self.do_wallet_transaction(rtn_order.id, user.id, wallet.id, referral_amount, return_amount)
      if self.payment_complete? && (wallet_transaction = self.wallet_transactions.where(state: 'order_complete').where('referral_amount > 0 OR return_amount > 0').last).present?
        wallet_transaction.notes = 'Referral And Refund amount refunded as order canceled'
        wallet_transaction.refunded!
      elsif !self.payment_complete?
        self.payment_made
      end
      # user.sidekiq_delay
      #     .send_wallet_notification(
      #       referral_amount + return_amount,
      #       wallet.currency_convert.symbol,
      #       rtn_order.app_source
      #     )
      SidekiqDelayGenericJob.perform_async(user.class.to_s, 
                                            user.id, 
                                            "send_wallet_notification", 
                                            referral_amount + return_amount, 
                                            wallet.currency_convert.symbol,
                                            rtn_order.app_source
                                          )
      rtn_order.add_notes_without_callback("Added #{wallet.currency_convert.symbol} #{referral_amount} to Wallet Referrals" +
        " AND #{wallet.currency_convert.symbol} #{return_amount} to Wallet Refunds", 'return', account)
      return "Transfered to Wallet"
    else
      return "Transfer to Wallet failed. Please make sure order was marked sane"
    end
  end

  def send_return_update_sms(status)
    phone = Order.get_mobile_num(order.billing_phone)
    if phone && phone != 0 && phone.length == 12
      template = case status
      when 'pending'
        url = user_order_return_items_path(self.user_id,order_number: order.number,return_id: self.id)
        Shortener::ShortenedUrl.generate(url, owner: self)
        url = short_url(url, owner: self, url_options: {host: Rails.application.config.action_mailer.default_url_options[:host], protocol: Rails.application.config.partial_protocol})
        note = self.new? ? 'Return successfully created.' : 'Product picked-up successfully.'
        "#{note} Please provide your bank details so that we can transfer the payments smoothly. Follow the link : #{url}"
      when 'refund_initiated'
        shortened_product_name = self.designs.map(&:title).join(',').first(25).strip
        if order.cod?
          "Refund Initiated: Amount for #{shortened_product_name}.. of Order No #{order.number} and will get refunded in #{REFUND_PROCESS_DAYS_TEXT} business days after you provide bank details."  
        else
          "Refund Initiated: Amount for #{shortened_product_name}... of Order No #{order.number} and will get refunded in 5-7 business days."
        end  
      end
      if template.present?
        res = SmsNotification::NotificationService.notify(phone, template)
        if res && status == 'refund_initiated'
          order.add_notes_without_callback('Refund Initiated SMS Sent to Customer', 'return')
        end
      end
    end
  end

  def self.mobile_all_items_received(return_id)
    if (rtn = Return.find_by_id return_id).present?
      rtn.all_products_received!
    end
  end

  def update_return_payment
    if !self.canceled? && (other_dos_states = self.reverse_shipments.where.not(shipment_state: ['canceled', 'delivery_exception', 'pickup_exception']).pluck(:shipment_state).uniq).present? && (other_dos_states - ['in_transit', 'out_for_delivery', 'delivered']).blank?
      if (!self.order.cod? || (self.type_of_refund != 'Refund' || self.account_number.present? || self.refund_phone_number.present?))
        self.payment_details_not_found if self.images_approved?
        self.all_products_received!
      else
        self.payment_details_not_found!
      end
    end
  end

  def self.update_pending_return_payment_state(start_day: 1)
    return_log = []
    Return.joins(return_designer_orders: [:reverse_shipment]).where(return_designer_orders: {reverse_shipment: {service_type: "ReverseShipment", in_transit_datetime: start_day.days.ago.beginning_of_day..Time.now.end_of_day}}).uniq.find_in_batches(batch_size: 100).each do |return_batch|
      return_batch.each do |ret|
        if (ret.reverse_shipments.present? && ret.state == 'new' && (other_shipment_states = ret.reverse_shipments.where.not(shipment_state: ['canceled', 'delivery_exception', 'pickup_exception']).pluck(:shipment_state).uniq).present? && (other_shipment_states - ['in_transit', 'out_for_delivery', 'delivered']).blank? )
            ret.update_return_payment
            return_log << "#{ret.order.number} - #{ret.id}"
        end
      end
    end
    unless Rails.env.development? || Rails.env.staging?
      bucket = 'return-pending-payment-script-log'
      filename = "log.txt"
      directory = AwsOperations.get_directory(bucket: bucket, new_connection: true)
      file = directory.files.get(filename)
      body = file.try(:body).to_s + "#{Time.now} :- #{return_log} \n\n"
      directory.files.create(
            key: filename,
            body: body,
          )
    end
  end

  state_machine :initial => :new, use_transactions: false do
    event :all_products_received do
      transition [:new, :canceled, :ticket_raised,:refund_details_pending,:require_image_approval,:refund_rejected] => :pending_payment
    end

    event :payment_details_not_found do
      transition [:new, :canceled, :wrong_track_details, :require_image_approval, :images_approved] => :refund_details_pending
    end

    event :wrong_tracking_details do
      transition [:refund_details_pending] => :wrong_track_details
    end

    event :reject_payment do
      transition [:pending_payment] => :refund_rejected
    end

    event :return_canceled do
      transition all - [:pending_payment,:payment_complete] => :canceled
    end

    event :mistaked_canceled do
      transition :canceled => :new
    end

    event :payment_made do
      transition :pending_payment => :payment_complete
    end

    event :defective_images_approved do
      transition require_image_approval: :images_approved
    end

    state :require_image_approval
    state :ticket_raised

    before_transition :to => :canceled do |return1|
      return1.total = 0
      return1.discount = 0
      return1.cancelled_on = Time.current
    end

    after_transition to: :canceled do |return1|
      return1.line_items.update_all(return_id: nil, return_designer_order_id: nil, return_quantity: nil, return_reason: nil)
    end

    after_transition to: :refund_details_pending do |return1|
      return1.sent_for_return_approval_on = Time.current
      return1.update_return_tracking_info
      # return1.sidekiq_delay.send_return_update_sms('pending') if return1.reverse_pickup? && return1.account_number.blank?
      SidekiqDelayGenericJob.perform_async(return1.class.to_s, return1.id, "send_return_update_sms", 'pending') if return1.reverse_pickup? && return1.account_number.blank?
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'pending')
      #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'pending')
    end

    after_transition :to => :pending_payment do |return1|
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'approved') unless return1.pending_on.present? 
      AppEvent::OrderEvent.new(return1.order.id, "Refund Initiated").trigger_clevertap_event_deliver_later
      #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'approved') unless return1.pending_on.present?
      return1.pending_on = Time.current
      return1.update_return_tracking_info
      # return1.sidekiq_delay.send_return_update_sms('refund_initiated')
      SidekiqDelayGenericJob.perform_async(return1.class.to_s, return1.id, "send_return_update_sms", 'refund_initiated')
      if (ticket = return1.tickets.where(state: 'reject').last).present?
        ticket.reopened_on = Time.now
        ticket.reopened_by_id = return1.return_approved_by
        ticket.reopen_ticket! if ticket.can_reopen_ticket?
      elsif return1.type_of_refund.try(:downcase) == 'refund' && !RESTRICTED_REASONS_FOR_RETURN_ORDER.include?(return1.reason)
        if return1.eligible_for_juspay_auto_refund?
          return1.create_juspay_refund
        elsif return1.eligible_for_payu_auto_refund?
          return1.create_payu_refund
        elsif return1.automate_cod_refund? && return1.refund_phone_number.present?
          return1.generate_cashgram if return1.refunds.cashgram.blank?
        else
          freshdesk_ticket_number = return1.order.tickets.where(issue: ['Initiate Return', 'Return Confirmation']).where.not(freshdesk_ticket_number: nil).pluck(:freshdesk_ticket_number).first
          Ticket.where(order_id: return1.order_id, department: 'accounts', state: ['open','working']).first_or_create(issue: 'Issue Auto Created By System',state: 'open',return_id: return1.id,message: 'Ticket Auto Created By System', freshdesk_ticket_number: freshdesk_ticket_number)
        end
      end
    end

    before_transition :to => :payment_complete do |return1|
      note  = "#{return1.type_of_refund} of amount #{return1.order.paid_currency_code || return1.order.currency_code} #{(return1.discount.to_f/(return1.order.paid_currency_rate || return1.order.currency_rate)).round(2)} completed"
      note += " for design id: #{return1.product_id}" if return1.product_id.present?
      note += ". Refund Transaction id is : #{return1.refund_transaction_id}." if return1.refund_transaction_id.present?
      return1.order.add_notes_without_callback(note, 'return')
      return1.completed_on = Time.current
      return1.voucher = Return.get_return_voucher
    end

    after_transition to: :payment_complete do |return1|
      # return1.sidekiq_delay.apply_full_refund_event
      SidekiqDelayGenericJob.perform_async(return1.class.to_s, return1.id, "apply_full_refund_event")
      order = return1.order
      if return1.type_of_refund.try(:downcase) != 'wallet' && ['cancel', 'cancel_complete'].include?(order.state) && return1.user == order.user && !order.other_details['referral_and_refund_amount_credited'] && (((referral = return1.get_referral_amount) == order.referral_discount && referral > 0) || order.refund_discount.to_f > 0)
        if return1.credit_amount_to_wallet(referral, order.refund_discount.to_f) == "Transfered to Wallet"
          order.other_details['referral_and_refund_amount_credited'] = true
          order.skip_before_filter = true
          order.save
        end
      end
      if return1.type_of_refund == 'Coupon'
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'processing')
        #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'processing')
      else
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'payment complete')
        #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'payment complete')
      end
      # return1.sidekiq_delay(queue: 'critical').create_credit_note_for_return if (return1.order.international? && (return1.return_designer_orders.count > 0))
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(return1.class.to_s, return1.id, "create_credit_note_for_return") if (return1.order.international? && (return1.return_designer_orders.count > 0))
    end

    after_transition :to => :wrong_track_details do |return1|
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'rejected')
      #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'rejected')
    end

    after_transition to: :images_approved do |return1|
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async("ReturnMailer", nil, "return_ticket_process_flow", {"#{return1.class}": return1.id}, 'image approval')
      #ReturnMailer.sidekiq_delay(queue: 'critical').return_ticket_process_flow(return1,'image approval')
    end

    after_transition to: :payment_complete do |return1|
      if return1.type_of_refund == 'Wallet' && (wallet_transaction = return1.wallet_transactions.where(state: ['order_complete', 'no_wallet_return']).last).present?
        wallet_transaction.refunded!
      end
    end
  end

  include Rails.application.routes.url_helpers
  include Shortener::ShortenerHelper
  include CashgramApi
end
