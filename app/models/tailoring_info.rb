class TailoringInfo < ActiveRecord::Base
  belongs_to :item, polymorphic: true
  belongs_to :warehouse_size_item, ->{where(tailoring_infos: {item_type: 'WarehouseSizeItem'})}, foreign_key: :item_id
  belongs_to :line_item, ->{where(tailoring_infos: {item_type: 'LineItem'})}, foreign_key: :item_id
  belongs_to :order
  has_one :order_delivery_nps_info,through: :order,source: :delivery_nps_info
  belongs_to :tailor
  has_many :tailoring_bag_relations
  has_many :tailoring_batches, through: :tailoring_bag_relations
  has_many :alteration_batches, through: :tailoring_bag_relations
  has_many :rtv_batches, through: :tailoring_bag_relations 
  has_many :tailoring_issues
  has_one :last_tailoring_bag_rel, ->{ order(id: :desc)}, class_name: :TailoringBagRelation
  has_one  :assigned_bag, ->{ where(state: ['assigned', 'alteration assigned']).order(id: :desc) }, class_name: :TailoringBagRelation
  has_one :rtv_assigned_bag, ->{ where(state: 'rtv assigned').order(id: :desc) }, class_name: :TailoringBagRelation
  has_many :tailoring_assign_batches, through: :tailoring_bag_relations

  scope :only_line_items, -> { where(item_type: 'LineItem') }
  #attr_accessible :line_item_id, :order_id, :tailor_id, :tailor_info_done_by, :tailoring_material, :tailor_name, :material_received_status, :material_received_status_timestamp, :line_item_quantity, :assigned_to_tailor_at, :batch_code, :picked_up_at, :reassign_material_timestamp, :rtv_material, :replacement_material_timestamp
  FNP_TAILORING_MATERIALS = ['saree', 'combo saree', 'fnp', 'combo fnp']
  RTV_REASONS = ['RTV as Replacement','Stain in product', 'Damaged product received', 'Product not as per image', 'Wrong product received', 'Dupatta not as per image', 'Wrong color product received', 'Color mismatch', 'Earing damaged', 'Bottom not received', 'Dupatta not received', 'Damaged dupatta received', 'Blouse not as per image', 'Blouse color not as per image', 'Blouse not received', 'Embroidery / work damaged', 'Embroidery not as per image']
  # has_many :stitching_measurements, through: :line_item
  after_save :update_tailoring_batch
  after_create :update_bucket_relations, if: -> (t_info){ t_info.item_type == 'LineItem'}
  attr_accessor :tailoring_event  
  has_many :tailoring_inscan_bags, through: :tailoring_bag_relations do 
    def update_bag_status
      each do |inscan_bag|
        if !inscan_bag.tailoring_bag_relations.any?{|rel| ['inscanned', 'alteration'].include?(rel.state)}
          inscan_bag.update_column(:status, 'close')
        end
      end
    end
  end 

  def self.send_for_stitching_bulk(params_bulk, tailor_id, batch_code, assigned_to_tailor_at, current_account, params_for_review, stylist_id)
    params_bulk = params_bulk.with_indifferent_access
    params_for_review = params_for_review.with_indifferent_access
    name = current_account.name
    tailor = Tailor.find(tailor_id)
    tailor_name  = tailor.name
    tailoring_info_bulk = []
    line_item_ids = []
    repeated_ids = []
    tailor_metric_hash = {}
    assigned_to_tailor_at =  assigned_to_tailor_at.to_datetime
    manifest_data, tailor_assigned_products, w_stylist_check = [],{}, ''
    if stylist_id
      w_stylist_check = 'stylist_id IS NULL OR stylist_id = ?', stylist_id
    end
    line_item_preload_array = [:designer_order,:tailoring_info]
    line_item_preload_array << Tailor.get_auto_tailor_assign_item_preload_array if AUTO_TAILOR_ASSIGN['enable']
    stitching_measurement = StitchingMeasurement.where(id: params_bulk.keys).where(w_stylist_check).preload(:order,line_item: line_item_preload_array).group_by(&:line_item_id)
    stitching_measurement.each do |line_item_id,measurements|
      measurement = measurements.first
      line_item = measurement.line_item
      des_order = line_item.designer_order
      case measurement.product_designable_type.downcase
      when "anarkali","anarkali_pant","kameez_salwar","kameez_pant",'anarkali_with_bottom_as_per_image','kameez_with_bottom_as_per_image', 'kameez_chudidar'
        material = "Anarkali"
      when "blouse"
        material = measurement.padded? ? 'Padded Blouse' : 'Simple Blouse'
      when "lehenga_choli"
        material = measurement.padded? ? 'lehenga + padded choli' : 'lehenga+simple choli'
      when 'fnp'
        material = 'FNP'
      else
        material = measurement.product_designable_type.try(:titleize)
      end
      tailoring_materials = line_item.tailoring_info.collect(&:tailoring_material)
      is_fabric_not_confirmed = line_item.measuremnet_received_on.blank?
      check_material = material == 'FNP' ? tailoring_materials.include?('FNP') : (tailoring_materials - ['FNP']).present?
      if line_item.status || (check_des_order_state = ['canceled','buyer_returned'].include?(des_order.state)) || check_material || is_fabric_not_confirmed
        measurement.remark = "Designer Order In #{des_order.state} State" if check_des_order_state
        measurement.remark = "Product In #{line_item.status} State" if line_item.status
        measurement.remark = 'Fabric is not yet confirmed.' if is_fabric_not_confirmed
        repeated_ids << [measurement.order.number,measurement.line_item_id,measurement.design_id,measurement.remark,material]
      elsif params_bulk[measurement.id.to_s]
        material_qty = params_bulk[measurement.id.to_s][3]
        if (combo_split = params_bulk[measurement.id.to_s].last.strip.split('|'))[1].present? && combo_split[1].include?('Combo')
          material = 'Combo Product ' + material
          material_qty = combo_split[2].to_i.nonzero? || material_qty
        end
        tailoring_info_bulk << line_item.tailoring_info.new(order_id: measurement.order_id, tailor_info_done_by: name, tailor_name: tailor_name, line_item_quantity: material_qty, tailoring_material: material, assigned_to_tailor_at: assigned_to_tailor_at, batch_code: batch_code, tailor_id: tailor_id)        
        tailor.set_tailor_daily_metric_hash(line_item, tailor_metric_hash, material_qty) if AUTO_TAILOR_ASSIGN['enable']
        manifest_data << [measurement.order.number,measurement.line_item_id,measurement.design_id,material,material_qty]
        line_item_ids <<   measurement.line_item_id
        measurements.map(&:id).each do |m_id|
          tailor_assigned_products[m_id] = [measurement.design_id, measurement.order.number]
        end
      end
    end
    tailoring_info_bulk.each do |t_info|
      t_info.run_callbacks(:create){true}
    end
    result = TailoringInfo.import tailoring_info_bulk
    # self.sidekiq_delay(queue: 'low').create_scope_scores(result.ids)
    self.create_scope_scores(result.ids)
    LineItem.where(id:line_item_ids).update_all(stitching_sent: 'Y', stitching_sent_by: current_account.id, stitching_sent_on: DateTime.now) 
    LineItem.update_rack_status(condition: {id: line_item_ids}, status: 'rack_out')
    LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Assign To Tailor', current_account.id)
    if result.ids.present?
      tailoring_batch = tailor.tailoring_batches.create(batch_code: batch_code, pending_item: result.num_inserts,total_item: result.num_inserts, assignee: current_account, tailoring_info_ids: result.ids) 
      tailoring_batch.send_to_tailor!
    end
    # Update line item when outscan done.
    tailoring_info_bulk.first.notification_to_tailor unless tailoring_info_bulk.blank?
    TailoringInfo.send_for_stitching_reviewd_item(params_for_review,tailor_name, tailor_id, batch_code,assigned_to_tailor_at,current_account,manifest_data,repeated_ids, w_stylist_check, tailor_assigned_products, params_bulk, tailor, tailor_metric_hash)
  end

  def self.send_for_stitching_reviewd_item(params_bulk, tailor_name, tailor_id, batch_code, assigned_to_tailor_at, current_account, manifest_data, repeated_ids, w_stylist_check, tailor_assigned_products, total_scanned_products, tailor, tailor_metric_hash)
    name = current_account.name
    tailoring_info_bulk = []
    previous_info_tailoring_info = []
    line_item_ids = []
    line_item_preload_array = [:designer_order]
    line_item_preload_array << Tailor.get_auto_tailor_assign_item_preload_array if AUTO_TAILOR_ASSIGN['enable']
    StitchingMeasurement.preload(:order,line_item: line_item_preload_array).where(id: params_bulk.keys).where(w_stylist_check).find_each(batch_size: 100) do |measurement|
      line_item = measurement.line_item
      is_fabric_not_confirmed = line_item.measuremnet_received_on.blank?
      des_order = line_item.designer_order
      if line_item.status || (check_des_order_state = ['canceled','buyer_returned'].include?(des_order.state)) || is_fabric_not_confirmed
        measurement.remark = "Designer Order In #{des_order.state} State" if check_des_order_state
        measurement.remark = "Product In #{line_item.status} State" if line_item.status
        measurement.remark = 'Fabric is not yet confirmed.' if is_fabric_not_confirmed
        repeated_ids << [measurement.order.number,measurement.line_item_id,measurement.design_id,measurement.remark, '']
      else
        params_bulk[measurement.id.to_s].uniq.each_with_index do |material,index| 
          next if index == 0       
          material_split = material.strip.split('|')  
          tailor_info = line_item.tailoring_info.where(order_id: measurement.order_id, tailoring_material: material_split[0]).first_or_initialize
          if tailor_info.id.blank?
            tailor_info.tailor_id = tailor_id
            tailor_info.tailor_name = tailor_name
            tailor_info.batch_code = batch_code
            tailor_info.assigned_to_tailor_at = assigned_to_tailor_at
            tailor_info.tailor_info_done_by = name
            material_qty = (material_split[1].to_i.nonzero? || params_bulk[measurement.id.to_s][0][3])
            tailor_info.line_item_quantity = material_qty
            tailoring_info_bulk << tailor_info
            manifest_data << [measurement.order.number,measurement.line_item_id,measurement.design_id,material_split[0],material_qty]
            line_item_ids <<   measurement.line_item_id
            tailor_assigned_products[measurement.id] = [measurement.design_id, measurement.order.number]
            tailor.set_tailor_daily_metric_hash(line_item, tailor_metric_hash, material_qty) if AUTO_TAILOR_ASSIGN['enable']
          else
            repeated_ids << [measurement.order.number,measurement.line_item_id,measurement.design_id,'', material_split[0]]
          end
        end
      end
    end
    tailoring_info_bulk.each do |t_info|
      t_info.run_callbacks(:create){true}
    end 
    result = TailoringInfo.import tailoring_info_bulk
    # self.sidekiq_delay(queue: 'low').create_scope_scores(result.ids)
    self.create_scope_scores(result.ids)
    LineItem.where(id:line_item_ids).update_all(stitching_sent: 'Y', stitching_sent_by: current_account.id, stitching_sent_on: DateTime.now) 
    LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Assign To Tailor', current_account.id)
    LineItem.update_rack_status(condition: {id: line_item_ids}, status: 'rack_out')
    if result.ids.present?
      tailoring_batch = tailor.tailoring_batches.create(batch_code: batch_code, pending_item: result.num_inserts,total_item: result.num_inserts, assignee: current_account, tailoring_info_ids: result.ids) 
      tailoring_batch.send_to_tailor!
    end
    TailorDailyMetric.update_tailor_daily_metrics_pending_count(tailor_metric_hash) if tailor_metric_hash.keys.present?
    all_scanned_mes = total_scanned_products.merge(params_bulk)
    generate_tailor_manifest(tailor_name,current_account.email,batch_code,manifest_data,assigned_to_tailor_at,repeated_ids, all_scanned_mes.except!(*(tailor_assigned_products.keys.map(&:to_s))).with_indifferent_access)
    # Update line item when outscan done.
    tailoring_info_bulk.first.notification_to_tailor unless tailoring_info_bulk.blank?
  end

  def add_notes(note, to_save, current_account = nil)
    first = current_account.nil? ? 'System' : current_account
    note_update(first,note)
    self.save if to_save
  end

  def note_update(user,note)
    note_content = "#{Date.today.strftime("%m/%d")} : #{user} : #{note}"
    self.notes ||= ''
    unless self.notes.blank?
      self.notes += ' ... '
    end
    self.notes += note_content
  end

  def self.export_tailoring_info_csv(start_date, end_date, current_account, received_status, tailor_id, param_payout, batch_number)
    tailoring_infos = TailoringInfo.get_tailoring_info(start_date, end_date, received_status, tailor_id, param_payout, batch_number)
    header = ['ID','Order Number','Order created_at','Order confirmed_at','Design ID','Designer Order State', 'Tailor Inscan Date','Tailor Info Done by','Tailor','Material','Assign to Tailor at','Received Status','Tailor created_at','Tailor updated_at','Received Time','Reassign Timestamp', 'LineItem Quantity', 'Tailoring Quantity','Paid Status','Payout Time','Batch Code', 'Price Deduction']
    if (is_receive_pending = (received_status == 'Pending'))
      tailoring_infos = tailoring_infos.sort_by{|t| t.item.get_sort_date(t.order.try(:confirmed_at), 'Tailor Receiving', t.created_at)}
      header << 'LPST Date' 
    end
    file = CSV.generate do |csv|
      csv << header
      tailoring_infos.each do |t_info|
        if (ord = t_info.order).present?
          order_number = ord.number
          ord_created = ord.created_at
          ord_confirmed = ord.confirmed_at
        end
        if (item = t_info.item).present?
          design_id = item.design_id
          quantity = item.quantity
          designer_order_state = item.designer_order.try(:state)
          lpst_date = item.get_sort_date(ord_confirmed, 'Tailor Receiving', t_info.created_at) if is_receive_pending
        end
        csv << [t_info.id, order_number, ord_created, ord_confirmed, design_id, designer_order_state, t_info.tailoring_inscan_bags.map(&:created_at).max, t_info.tailor_info_done_by, t_info.tailor_name, t_info.tailoring_material, t_info.assigned_to_tailor_at, t_info.material_received_status, t_info.created_at, t_info.updated_at, t_info.material_received_status_timestamp, t_info.reassign_material_timestamp, quantity,t_info.line_item_quantity,t_info.paid_status_tailor, t_info.payout_tailor_paid_timestamp, t_info.batch_code, t_info.price_deduction, lpst_date]
      end
    end  
    emails = {'to_email'=> current_account.email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer('Tailoring Info CSV File','Please Find Attachment.',emails,{'Tailoring Info.csv'=> file} ).deliver
  end


  def self.get_tailoring_info(start_date, end_date, received_status, tailor_id, payout, batch_number, batching = true)
    tailoring_infos, w_batch_clause = [], []
    w_state_filter_clause, join_array, order_by_clause = [], [], ''
    preload_array = [:order, :tailoring_inscan_bags, item: :designer_order]
    w_tailor_name = 'tailor_id = ?',tailor_id if tailor_id.present?
    w_batch_clause = ['lower(batch_code) = ?', batch_number.strip.downcase] if batch_number.present?
    if payout.present?
      payout = payout.downcase
      if payout == 'y'
        w_payout = 'LOWER(paid_status_tailor) = ? ',payout
      else
        w_payout = 'LOWER(paid_status_tailor) = ? OR paid_status_tailor IS NULL AND LOWER(paid_status_tailor) <> ? ',payout,'y' if payout == 'pending'
      end  
    end
    if received_status.present?
      w_received = ((received_check = (received_status == 'Received')) ? ['material_received_status = ?', true] : ['material_received_status = ? or material_received_status is null', false])
      unless received_check
        w_state_filter_clause = ['orders.state not in (?) and designer_orders.state not in (?) and line_items.status is null and line_items.stitching_done_on is null', ['cancel', 'dispatched'], ['canceled', 'vendor_canceled']]
        join_array = (batching ? [line_item: [designer_order: :order]] : [line_item: [:tailor_receiving_process_date, [designer_order: :order]]])
        order_by_clause = 'process_dates.lpst_date'
        preload_array = [:order, :tailoring_inscan_bags, item: [:designer_order, :tailor_receiving_process_date]]
      end
    end
    if batching
      TailoringInfo.only_line_items.preload(preload_array).joins(join_array).where('tailoring_infos.created_at between ? and ?',start_date,end_date).where(w_tailor_name).where(w_payout).where(w_batch_clause).where(w_received).where(w_state_filter_clause).find_in_batches(batch_size: 500) do |t_info_batch|
        tailoring_infos += t_info_batch
      end
      return tailoring_infos
    else
      return TailoringInfo.only_line_items.preload(preload_array).joins(join_array).where('tailoring_infos.created_at between ? and ?',start_date,end_date).where(w_tailor_name).where(w_payout).where(w_received).where(w_batch_clause).where(w_state_filter_clause).order(order_by_clause)
    end
  end


  def self.bulk_assign_fnp(details,tailor_name,tailor_id,current_account,batch_code, combo_hash)
    details = details.with_indifferent_access
    combo_hash = combo_hash.with_indifferent_access
    new_tailoring_info = []
    line_item_ids = []
    repeated_data = []
    manifest_data = []
    tailor = Tailor.find_by_id tailor_id
    details.each do |key,val|
      LineItem.preload(:tailoring_info).joins(:order).select('line_items.id,line_items.design_id,line_items.quantity,orders.id as order_id,orders.number,orders.stylist_id').where(design_id:val).where('orders.state NOT IN (?) AND status IS NULL AND lower(stitching_required) = ? AND orders.number = ?',['cancel','new'],'y',key).find_each(batch_size: 100) do |item|
        previous_info = item.tailoring_info.collect(&:tailoring_material) & ['Saree', 'Combo Saree']
        if previous_info.present?
          repeated_data << [item.number,item.id,item.design_id,'',previous_info.last]
        elsif STYLIST_TAILOR_MAPPING['enable'] == 1 && (tailor_ids = STYLIST_TAILOR_MAPPING['mapping'][item.stylist_id.to_s]).present? && tailor_ids.exclude?(tailor_id.to_i)
          repeated_data << [item.number,item.id,item.design_id,'Tailor Not Found in Stylist-Tailor Mapping Group','Saree']
        else
          material_name = (combo_qty = combo_hash["#{item.number}-#{item.design_id}"]).present? ? 'Combo Saree' : 'Saree'
          material_qty = (combo_qty.presence || item.quantity).to_i 
          new_tailoring_info <<  item.tailoring_info.new(order_id:item.order_id, tailor_info_done_by:current_account.name,tailor_name:tailor_name,tailor_id: tailor_id, line_item_quantity: material_qty, tailoring_material: material_name, assigned_to_tailor_at: DateTime.now, batch_code: batch_code)
          manifest_data << [item.number,item.id,item.design_id, material_name, material_qty]
          line_item_ids << item.id        
        end
      end  
    end
    new_tailoring_info.each do |t_info|
      t_info.run_callbacks(:create){true}
    end
    result = TailoringInfo.import new_tailoring_info
    # self.sidekiq_delay(queue: 'low').create_scope_scores(result.ids)
    self.create_scope_scores(result.ids)
    tailoring_batch = tailor.tailoring_batches.create(batch_code: batch_code, pending_item: result.num_inserts,total_item: result.num_inserts, assignee: current_account, tailoring_info_ids: result.ids)
    tailoring_batch.send_to_tailor!
    new_tailoring_info.first.notification_to_tailor unless new_tailoring_info.blank?
    LineItem.where(id:line_item_ids).update_all(stitching_sent: 'Y', stitching_sent_by: current_account.id, stitching_sent_on: DateTime.now) 
    LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Assign To Tailor', current_account.id)
    LineItem.update_rack_status(condition: {id: line_item_ids}, status: 'rack_out')
    generate_tailor_manifest(tailor_name,current_account.email,batch_code,manifest_data,DateTime.now,repeated_data)
  end

  def self.bulk_receive_fnp(details, account_id)
    line_item_ids = []
    Order.select('orders.id,orders.number').preload(:line_items).where(number:details.keys).find_each(batch_size: 100) do |order|
      line_item_ids << order.line_items.find_all{|item| details[order.number].include?(item.design_id.to_s)}.collect(&:id)
    end
    where_clause = line_item_ids.flatten.present? ? "item_id IN (#{line_item_ids.flatten.join(',')}) AND tailoring_material IN ('Saree', 'Combo Saree')" : ''
    mark_tailoring_infos_received(where_clause, account_id) if where_clause.present?
  end

  def self.check_for_tailor_assignment_lockout(tailor_id, stylist_id)
    TailoringInfo.joins(:order).where('material_received_status_timestamp IS NULL AND ((tailor_id = ? AND tailoring_infos.created_at::DATE < ?) OR (tailoring_infos.created_at::DATE < ? AND lower(tailoring_material) <> ? AND orders.stylist_id = ?))', tailor_id, TAILOR_STYLIST_LOCKOUT_DAYS['tailor_lockout_threshold'].days.ago,TAILOR_STYLIST_LOCKOUT_DAYS['stylist_lockout_threshold'].days.ago, 'fnp', stylist_id).exists?
  end

  def self.mark_tailoring_infos_received(where_clause, account_id)
    to_be_imported = []
    TailoringInfo.only_line_items.where(where_clause).find_each(batch_size: 100) do |t_info|
      t_info.price_deduction = t_info.get_amount_to_be_deducted_for_payment_to_tailor if TAT_FOR_TAILOR['enable'] == 1 && !t_info.material_received_status
      t_info.material_received_status = true
      t_info.material_received_status_timestamp = DateTime.now()
      to_be_imported << t_info
    end
    to_be_imported.each do |t_info|
      t_info.run_callbacks(:save)
    end
    TailoringInfo.import to_be_imported, on_duplicate_key_update: {conflict_target: [:id], columns: [:material_received_status, :material_received_status_timestamp, :price_deduction]}
    LineItem.bulk_add_into_scan('LineItem', to_be_imported.collect(&:item_id), 'Received From Tailor', account_id)
    to_be_imported.count
  end

  def notification_to_tailor
    if (target_id = tailor.try(:contact_no)).present?
      app_source = 'android-tailor'
      campaign_key = 'Tailor_Notification'
      message = {
              'title' =>  'A new order is for You!',
              'body' => 'A new order is there at MIRRAW required stitching, visit MIRRAW to collect the material!'
            }

      msg = {
        'target' => target_id.to_s,
        'alert' => message,
        'android' => {'extra' => { 'notificationMainTitle' => message['title'] }}
      }
      ClevertapNotification.modify_and_push_notification([msg], campaign_key, app_source) if CLEVERTAP_NOTIFICATION == 'true'
    end
  end

  def notification_to_tailor_for_delayed_item(contact_no, count)
    if (target_id = contact_no).present? & count.present?
      app_source = 'android-tailor'
      campaign_key = 'Tailor_Notification'
      message = {
              'title' =>  'Mirraw stitching items are late',
              'body' => "#{count} Mirraw Stitching Items are delayed",
              'type' => 'delayed_items',
              'wzrk_cid' => 'mirraw'
            }

      msg = {
        'target' => target_id.to_s,
        'alert' => message,
        'android' => {'extra' => { 'notificationMainTitle' => message['title'],'type' => 'delayed_items', 'wzrk_cid' => 'mirraw' }}
      }
      ClevertapNotification.modify_and_push_notification([msg], campaign_key, app_source) if CLEVERTAP_NOTIFICATION == 'true'
    end
  end

  def get_amount_to_be_deducted_for_payment_to_tailor
    tat_for_tailor = (TAT_FOR_TAILOR['TAT'][self.tailoring_material.downcase].presence || TAT_FOR_TAILOR['TAT']['default']).to_i
    assign_date = self.notes.to_s.include?('Tailor changed') ? self.reassign_material_timestamp : self.created_at
    if (number_of_days_taken = ((Time.current - assign_date) / 1.day)) > tat_for_tailor
      (number_of_days_taken - tat_for_tailor).ceil * TAT_FOR_TAILOR['charges'].to_i
    else
      0
    end
  end

  def last_scanned_bag
    state_filters = (tailoring_event == :auto_tailor_reassign ? ['assigned'] : ['inscanned', 'alteration', 'rtv', 'reassigned'])
    @last_scanned_bag ||= tailoring_bag_relations.where(state: state_filters).last
  end  

  def self.send_for_post_tailoring_work(assign_data, barcodes_data, tailor_id, batch_code, current_account, assign_type, model_name, auto_tailor_reassign=false)
    assign_data = assign_data.with_indifferent_access
    barcodes_data = barcodes_data.with_indifferent_access
    model_name = model_name.constantize if model_name.is_a?(String) 
    failed_data, tailoring_to_be_import, successful_tailoring_infos, incorrect_scanned_ids, wrong_ids = {}, [], {}, {}, []
    columns_to_be_updated, auto_reassign_item_ids, tailor_metric_hash = [], [], {}
    titleized_assign_type = assign_type.titleize
    tailor = Tailor.find tailor_id if tailor_id.present?
    join_array, preload_array = [:tailoring_bag_relations], [:tailoring_bag_relations, :order, :item]
    req_bag_rel_state = assign_type
    default_success_remark = ''
    tailoring_event_name = "#{assign_type}_bag".to_sym
    if auto_tailor_reassign
      join_array.push(:order, :line_item) 
      preload_array = [:tailoring_bag_relations, :tailor, :order, item: [:design, line_item_addons: :addon_type_value]]
      req_bag_rel_state = 'assigned'
      default_success_remark = "Reassigned to #{tailor.try(:name)}"
      tailoring_event_name = :auto_tailor_reassign
    end
    assign_data.each do |k,v|
      v.uniq!
      w_tailor_check = ''
      w_tailor_check = 'tailor_id = ?', tailor_id if assign_type == 'reassigned'
      all_scanned_ids = v.dup
      if k.length > 10 || (wrong_ids = (all_scanned_ids - v.delete_if{|i| i.to_s.length > 10})).present?
        barcode_keys = (wrong_ids.presence || all_scanned_ids)
        get_auto_reassign_barcode_keys(barcode_keys, k) if auto_tailor_reassign
        incorrect_scanned_ids[k] = barcodes_data.values_at(*barcode_keys)
        next if wrong_ids.blank?
      end

      base_w_clause = {order_id: k, item_id: v}
      if auto_tailor_reassign
        base_w_clause = {order: {number: k}, line_item: {design_id: v}} 
        w_tailor_check = ['tailor_daily_metric_id is not null and tailor_id <> ?', tailor.id]
      end
      tailoring_data = TailoringInfo.only_line_items.preload(preload_array).joins(join_array).where(base_w_clause).where(w_tailor_check).where('tailoring_bag_relations.state = ?', req_bag_rel_state).uniq
      tailoring_data.each do |t_info|         
        order = t_info.order
        line_item = t_info.item
        t_info.assign_attributes(notes: (t_info.notes.to_s + " ...#{Date.today.strftime("%m/%d")} : #{current_account.name} : #{assign_type} Bag Assigned"), tailoring_event: tailoring_event_name)
        if auto_tailor_reassign
          tailor.set_tailor_daily_metric_hash(line_item, tailor_metric_hash, t_info.line_item_quantity, :increment) if tailor.present?
          t_info.tailor.set_tailor_daily_metric_hash(line_item, tailor_metric_hash, t_info.line_item_quantity, :decrement)
          t_info.update_as_per_auto_tailor_reassign(tailor)           
          auto_reassign_item_ids << t_info.item_id          
        end        
        tailoring_to_be_import << t_info
        successful_tailoring_infos[t_info.id] = [order.number, t_info.item_id, line_item.design_id, t_info.tailoring_material, (assign_type == 'alteration' ? t_info.alteration_note : (assign_type == 'rtv' ? t_info.rtv_material : default_success_remark))]
        columns_to_be_updated = (columns_to_be_updated.presence || t_info.changed.map(&:to_sym))
      end
      found_item_data = (auto_tailor_reassign ? tailoring_data.map(&:item).flatten.map(&:design_id) : tailoring_data.map(&:item_id))
      if (failed_ids = (v - found_item_data)).present?
        get_auto_reassign_barcode_keys(failed_ids, k) if auto_tailor_reassign
        (failed_data[k] ||= []) << barcodes_data.values_at(*failed_ids)
      end      
    end
    tailoring_to_be_import.each do |t_info|
      t_info.run_callbacks(:save)
    end    
    import_res = TailoringInfo.import tailoring_to_be_import, validate: false, on_duplicate_key_update: {conflict_target: :id, columns: columns_to_be_updated}
    if successful_tailoring_infos.present?
      post_tailoring_work_bag = model_name.create(batch_code: batch_code, pending_item: import_res.num_inserts, total_item: import_res.num_inserts, assignee_id: current_account.id, tailor_id: tailor_id, tailoring_info_ids: successful_tailoring_infos.keys)
      post_tailoring_work_bag.send_to_tailor! if assign_type == 'reassigned'
    end
    TailorDailyMetric.update_tailor_daily_metrics_pending_count(tailor_metric_hash, successful_tailoring_infos.keys) if auto_tailor_reassign && tailor_metric_hash.keys.present?
    file = CSV.generate do |csv|
      csv << ['Date', Time.current.strftime('%d-%m-%Y %I:%M %p')]
      csv << ['Batch Code', post_tailoring_work_bag.try(:batch_code)]
      if successful_tailoring_infos.present?
        2.times{|i| csv << []}
        csv << ['Successful Report']
        csv << ['Order Number', 'Line Item ID', 'Design ID', 'Material', "#{titleized_assign_type} Note"]
        successful_tailoring_infos.each{|k,v| csv << v }
      end
      if failed_data.present? || incorrect_scanned_ids.present?
        2.times{|i| csv << []}
        csv << ['Failed Report']
        csv << ['Barcode', 'status']
        failed_data.values.flatten.each{|code| csv << [code, "Tailoring Record Not in #{titleized_assign_type} state Or Tailor Mismatch"]}
        incorrect_scanned_ids.values.flatten.each{|code| csv << [code, 'Wrong Barcode Scanned Or Content Wrong.']}
      end  
    end
    emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email' => ACCESSIBLE_EMAIL_ID['bag_report_cc_emails']}
    OrderMailer.report_mailer("Tailoring #{titleized_assign_type} Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.", emails, "Tailoring #{titleized_assign_type} Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}.csv"=> file).deliver
  end

  def update_as_per_auto_tailor_reassign(tailor_obj)
    reassign_notes = "Tailoring Material is Reassign to Tailor (#{tailor_obj.name}), previous tailor #{tailor_name_was} and reassign reason=> Auto Tailor Assign"
    reassign_notes += '...Tailor changed' if tailor_name_was != tailor_obj.name
    self.notes += "...#{reassign_notes}"
    self.assign_attributes(
      tailor_id: tailor_obj.id, 
      tailor_name: tailor_obj.name, 
      material_received_status: nil, 
      material_received_status_timestamp: nil, 
      reassign_material_timestamp: Time.current, 
      active: true
    )
  end

  def mark_tailoring_received(done_by_account)
    self.price_deduction = get_amount_to_be_deducted_for_payment_to_tailor if TAT_FOR_TAILOR['enable'] == 1 && !material_received_status
    self.assign_attributes(material_received_status: true, material_received_status_timestamp: Time.current, tailor_info_done_by: done_by_account.name, tailoring_event: :receive)      
    if self.save
      StitchingMeasurement.unscoped.where(order_id: order_id, measurement_group: 'tailor').where('parent_measurement_id IS NOT NULL').update_all(suggested_measurements:nil) if order_id
      item.add_into_scan('Received From Tailor', done_by_account.id)
      {error: false, message: 'Successfully marked tailor received'}
    else
      {error: true, message: 'Something Went Worng.'}
    end
  end

  def mark_tailoring_reassigned(done_by_account, reason, previous_tailor_name, new_tailor_id, measurement_ids=[])
    if (reassign_tailor = Tailor.find_by_id(new_tailor_id))
      reassign_tailor_name = reassign_tailor.name            
      self.assign_attributes(tailor_id: reassign_tailor.id, tailor_name: reassign_tailor_name, material_received_status: nil, material_received_status_timestamp: nil, reassign_material_timestamp: Time.current, active: true)
      notes = "Tailoring Material is Reassign to Tailor (#{reassign_tailor_name}), previous tailor #{previous_tailor_name} and reassign reason=> #{reason}"
      notes += '...Tailor changed' if previous_tailor_name != reassign_tailor_name
      (notes += update_reassigned_measurements(measurement_ids).to_s) if measurement_ids
      self.tailoring_event = :reassign      
      item.add_into_scan('Reassign To Tailor', done_by_account.id)
      self.add_notes(notes, true, done_by_account.name)
      self.order_quality_event(:add, 'ProductReassignedToTailor')
      {error: false, message: "Tailoring info successfully reassigned to #{reassign_tailor_name}."}
    else
      {error: true, message: "Reassign tailor not found"}
    end
  end

  def mark_tailoring_rtv(rtv_reason, done_by_account, measurement_ids=[], other_reason=nil)    
    if rtv_reason.present?
      notes = "Return to vendor reason => #{rtv_reason}, #{other_reason}"
      if rtv_reason == 'RTV as Replacement'
        (notes += update_replacement_measurements(measurement_ids).to_s) if measurement_ids      
        self.assign_attributes(rtv_material: rtv_reason, replacement_material_timestamp: Time.current)
      else
        self.assign_attributes(rtv_material: rtv_reason, material_received_status: false, material_received_status_timestamp: Time.current)
      end
      self.tailoring_event = :rtv
      self.add_notes(notes, true, done_by_account.name)        
      item.add_into_scan('Tailoring RTV', done_by_account.id)
      if item.issue_status != 'Y'      
        item.update_columns(issue_status: 'Y', issue_created_by: done_by_account.id, issue_created_at: Time.zone.now, issue_message: "#{rtv_reason}, #{other_reason}")
        order.add_notes_without_callback("#{item.design_id} Issue: #{rtv_reason}, #{other_reason}",'other', done_by_account)
        item.add_into_scan('Issue Created', done_by_account.id)
        order.check_items
      end
      order.add_tags_skip_callback('issue')            
      {error: false, message: "RTV reason (#{rtv_reason}) added for below order."}
    else
      {error: true, message: 'Rtv reason missing.'}
    end
  end

  def mark_tailoring_alteration(alter_note, done_by_account)
    if alter_note.present?
      note = self.alteration_note.blank? ? alter_note.to_s : (self.alteration_note.to_s + '...' + alter_note.to_s)
      self.update_columns(alteration_note: note, alteration_added_at: Time.current)
      self.tailoring_event = :alter
      self.add_notes('Alteration Note Added', true, done_by_account.name)
      item.add_into_scan('Alteration Note Added', done_by_account.id)
      self.order_quality_event(:add, 'ProductAltered')
      {error: false, message: 'Alteration note successfully added.'}
    else
      {error: true, message: 'Alteration note missing.'}
    end
  end
  
  def do_tailoring_receiving_action(receive_type, reason, done_by_account)
    case receive_type
    when 'receive'
      mark_tailoring_received(done_by_account)
    when 'reassign'
      mark_tailoring_reassigned(done_by_account, reason, tailor_name, tailor_id)      
    when 'rtv'
      mark_tailoring_rtv(reason, done_by_account)      
    when 'alteration'
      mark_tailoring_alteration(reason, done_by_account)      
    else
      {error: true, message: 'This Action is calling the future.'}
    end
  end

  def self.create_scope_scores(ids)
    where(id: ids).find_each do |tailoring_info|
      tailoring_info.create_scope_score
    end
  end

  def create_scope_score
    ScopeScore::Scopes.each do |scope_name|
      send("create_#{scope_name}_score") unless send("#{scope_name}_score")
    end
  end

  def self.generate_tailor_manifest_for_django(params)
    tailoring_batch = TailoringBatch.find_by_id params[:tailor_batch_id]
    sent_to_account = Account.find_by_id(params[:account_id]).try(:email) || '<EMAIL>'
    manifest_data = []
    tailoring_batch.tailoring_infos.preload(:order, :item).each do |t_info|
      manifest_data << [t_info.order.number, t_info.item_id, t_info.item.design_id, t_info.tailoring_material, t_info.line_item_quantity]
    end
    self.generate_tailor_manifest(tailoring_batch.tailor.name, sent_to_account, tailoring_batch.batch_code, manifest_data, Time.now, [])
    new_tailor_batch = TailoringBatch.where(batch_code: params[:new_batch_code], state: 'openned').first
    if new_tailor_batch.present?
      pdf_content = ActionController::Base.new.render_to_string(
        template:  '/tailoring_info/tailor_manifest',
        layout:  false,
        locals: {:@tailor_name=>new_tailor_batch.tailor.name, :@manifest_data => [], :@batch_code => new_tailor_batch.batch_code, :@assigned_at =>nil, manifest_for: nil}
      )
      new_barcode_pdf = WickedPdf.new.pdf_from_string(pdf_content)
      OrderMailer.report_mailer('New Tailor Batch', 'Following Attached is the barcode for new tailor bag', {'to_email'=>sent_to_account, 'from_email_with_name'=> '<EMAIL>'}, {"#{new_tailor_batch.batch_code}_barcode.pdf" => new_barcode_pdf}).deliver
    end
  end

  def post_tailor_assign_callbacks
    self.notification_to_tailor
    self.create_scope_score
  end

  private

    def self.get_auto_reassign_barcode_keys(base_keys, prepend_data)
      base_keys.map{|key| key.to_s.prepend(prepend_data)}
    end

    def self.generate_tailor_manifest(tailor_name,email,batch_code,manifest_data,assigned_at,repeated_ids, non_assigned_products = nil, manifest_for: 'Order')
      tailor_manifest = nil
      if manifest_data.present?
        pdf_content = ActionController::Base.new.render_to_string(
          template:  '/tailoring_info/tailor_manifest',
          layout:  false,
          locals: {:@tailor_name=>tailor_name, :@manifest_data => manifest_data, :@batch_code => batch_code, :@assigned_at =>assigned_at, manifest_for: manifest_for}
        )
        tailor_manifest = WickedPdf.new.pdf_from_string(pdf_content)
      end
      notice = "No tailoring info is created." unless manifest_data.present?
      tailor_email = Tailor.get_tailor.find{|t| t[0] == tailor_name }[1][2]
      to_email_array = [email, tailor_email].compact
      ShipmentMailer.mail_tailor_manifest(batch_code,to_email_array,tailor_manifest,repeated_ids,notice, non_assigned_products, manifest_for: manifest_for).deliver
    end
    
    def update_replacement_measurements(measurement_ids)
      replaced_measurements = StitchingMeasurement.where(id: measurement_ids).where('product_received_from_tailor_on IS NOT NULL')
      StitchingMeasurement.unscoped.where(parent_measurement_id:measurement_ids, measurement_group: 'tailor').update_all(suggested_measurements:{rtv_or_rtt:'Return to Vendor'})
      if replaced_measurements.exists?
        " ... Measurement ID #{replaced_measurements.collect(&:id).join(',')}, Received from Tailor on #{replaced_measurements.collect(&:product_received_from_tailor_on).map(&:to_date).join(',')} by #{replaced_measurements.collect(&:product_received_from_tailor_by).join(',')}"
      end
    end

    def update_reassigned_measurements(measurement_ids)
      reassigned_measurements = StitchingMeasurement.where(id:measurement_ids).where('product_received_from_tailor_on IS NOT NULL')
      StitchingMeasurement.unscoped.where(parent_measurement_id:measurement_ids, measurement_group: 'tailor').update_all(suggested_measurements:{rtv_or_rtt: 'Reassign to Tailor'})    
      if reassigned_measurements.exists?
        " ... Measurement ID #{reassigned_measurements.collect(&:id).join(',')}, Received from Tailor on #{reassigned_measurements.collect(&:product_received_from_tailor_on).map(&:to_date).join(',')} by #{reassigned_measurements.collect(&:product_received_from_tailor_by).join(',')}"
      end 
    end

    def update_tailoring_batch      
      if last_scanned_bag.present?
        case tailoring_event
        when :receive
          last_scanned_bag.update_column(:state, 'received')
        when :reassign
          last_scanned_bag.update_column(:state, 'reassigned')
        when :rtv
          last_scanned_bag.update_column(:state, 'rtv')
        when :alter
          last_scanned_bag.update_column(:state, 'alteration')
        when :alteration_bag
          last_scanned_bag.update_column(:state, 'bag change alteration')
        when :rtv_bag
          last_scanned_bag.update_column(:state, 'bag change rtv')
        when :reassigned_bag, :auto_tailor_reassign
          last_scanned_bag.update_column(:state, 'bag change reassign')  
        end
        last_scanned_bag.tailoring_batch.update_count if material_received_status_changed? && last_scanned_bag.tailoring_batch.present?
        tailoring_inscan_bags.update_bag_status
        order.add_notes_without_callback("#{item.design_id} - #{last_scanned_bag.state.try(:titleize)}", 'tailoring') if order.present? && [:alteration_bag, :rtv_bag].include?(tailoring_event)
      end
    end

    def update_bucket_relations
      WarehouseBucket.bulk_update_warehouse_buckets(bucket_type: 'StitchingBucket', item_ids: [item_id], item_type: 'LineItem', new_state: 'Tailor Assigned')
    end

  include ScopeScoreEvent
end
