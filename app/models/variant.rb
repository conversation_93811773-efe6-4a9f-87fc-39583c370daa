class Variant < ActiveRecord::Base
  include SidekiqHandleAsynchronous
  attr_accessible :design_id, :position, :quantity, :option_type_values, :option_type_value_ids, :show_variant, :in_stock_warehouse, :reordering_percent, :ordering_quantity,:designer_quantity, :price, :design_code, :stitched, :transfer_price
  attr_accessor :has_quantity_changed, :previous_quantity
  has_and_belongs_to_many :option_type_values
  has_and_belongs_to_many :customized_option_type_values, :class_name => 'OptionTypeValue' #, :limit => 1  # used while generating facebook feed
  has_many :option_types, through: :option_type_values
  has_many :line_items, dependent: :nullify
  has_many :option_type_values_variants
  has_many :design_promotion_pipe_lines, through: :design
  validates :quantity, :numericality => { greater_than_or_equal_to: 0, only_integer: true}
  validates_numericality_of :price, greater_than_or_equal_to: MIN_PRICE_PER_PRODUCT, only_integer: true
  validate :price_lock_for_designer
  belongs_to :design
  before_validation :update_price, if: Proc.new {|variant| variant[:price].to_i < MIN_PRICE_PER_PRODUCT || variant.transfer_price_changed? || variant.price_changed?}
  before_save :cache_previous_values
  after_commit :update_design_state, if: -> { has_quantity_changed }
  # Need to optimize notification callback
  #, :variant_in_stock_notification, :variant_less_quantity_notification

  THRESHOLD_QTY = SystemConstant.get('THRESHOLD_QTY_DESIGN_NOTIFICATION').to_i
  delegate :is_transfer_model?, to: :design

  def self.default_scope
    where show_variant: true
  end

  def set_option_type_value_id(id=nil)
    id||=self.option_type_value_id #self.attributes[:option_type_value_id]
    if id.present? && self.quantity.present?
      self.option_type_values_variants.build(option_type_value_id: id)
    else
      self.class_eval {attr_accessor :option_type_value_id}
      self.option_type_value_id = nil
    end
    return nil
  end

  def category_ids
    design.category_ids
  end

  def pair_product_discount
    design.pair_product_discount
  end

  private
  def cache_previous_values
    self.has_quantity_changed = self.quantity_changed?
    self.previous_quantity = self.quantity_was
  end

  def update_design_state
    quantity_was_zero, quantity_zero = need_for_label_movement
    category_name = PLUS_SIZE_CATEGORY_PARAM[self.design.designable_type].try(:[], 0)
    if quantity_zero && category_name.present?
      assign_plus_category = Category.getid(category_name)
      # Quantity was changed to 0 from (X > 0)
      # Plus size product quantity is not present.
      # Already product tagged as plus size
      CategoriesDesign.add_designs([self.design.id], assign_plus_category, 'Remove') if self.design.get_plus_size_category_id.blank?
    elsif quantity_was_zero && (category_id = self.design.get_plus_size_category_id).present?
      # Quantity was changed to (X > 0) from 0.
      # Plus size product quantity is present.
      # Product not tagged as plus size.
      CategoriesDesign.add_designs([self.design.id], category_id)
    end
      design.try(:update_variant_quantity)
  end

  handle_asynchronously :update_design_state

  def need_for_label_movement
    if self.has_quantity_changed && ((quantity_was = (self.previous_quantity == 0)) || (quantity = (self.quantity == 0)))
      [quantity_was, quantity]
    else
      [false, false]
    end
  end

  def variant_in_stock_notification
    # design.try { |d| d.sidekiq_delay.in_stock_notification_android(id) } if quantity_changed? && quantity_was == 0 && quantity.to_i > THRESHOLD_QTY
    design.try { |d| SidekiqDelayGenericJob.perform_async(d.class.to_s, d.id, "in_stock_notification_android", id) } if quantity_changed? && quantity_was == 0 && quantity.to_i > THRESHOLD_QTY
  end

  def variant_less_quantity_notification
    # design.try { |d| d.sidekiq_delay.less_quantity_notification_android(id) } if quantity_changed? && quantity_was.to_i > quantity.to_i && (THRESHOLD_QTY.between? quantity.to_i, quantity_was.to_i)
    design.try { |d| SidekiqDelayGenericJob.perform_async(d.class.to_s, d.id, "less_quantity_notification_android", id) } if quantity_changed? && quantity_was.to_i > quantity.to_i && (THRESHOLD_QTY.between? quantity.to_i, quantity_was.to_i)
  end

  def update_price
    if self.transfer_price.to_i.nonzero?
      self[:price] = get_display_price_for_transfer_model
    elsif self[:price].to_i < MIN_PRICE_PER_PRODUCT
      self[:price] = self.design.try(:[],:price)
    end
  end

  include DesignerQuantity
  include Priceable
end
