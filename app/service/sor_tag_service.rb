class SorTagService
    def initialize(item)
      @item = item
    end
  
    def process_item
        design_id = @item.design_id
        variant_id = @item.variant_id
        if @item.variant_id.blank? && (size_chart_id = @item.line_item_addons.where.not(size_chart_id: nil).first.try(:size_chart_id)).present?
          size_bucket = SizeBucket.joins(:size_charts).where(size_charts: {id: size_chart_id}).first
        end
        if size_bucket.present?
          rack = RackListsWarehouseLineItem.joins(:rack_list,warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and design_id = ? ',@item.quantity,design_id).where(warehouse_size_item: {size_bucket_id: size_bucket.id}).order('quantity_present asc').first
          rack = RackListsWarehouseLineItem.joins(:rack_list,warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and design_id = ? ',@item.quantity,design_id).where(warehouse_size_item: {size_bucket_id: SizeChart::UNSTITCHED_SIZE_CHART.size_bucket_id}).order('quantity_present asc').first if rack.blank? && size_bucket.id != SizeChart::UNSTITCHED_SIZE_CHART.size_bucket_id
        else
          rack = RackListsWarehouseLineItem.joins(:rack_list,:warehouse_line_item).where('quantity_present >= ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL)) ',@item.quantity,variant_id,design_id,design_id).order('quantity_present asc').first || RackListsWarehouseLineItem.joins(:rack_list, warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL)) ',@item.quantity,variant_id,design_id,design_id).order('quantity_present asc').first
        end
        if rack.present?
            rack.decrease_rack_quantity(@item.quantity)
            WarehouseLineItemJoin.create_item(rack, @item)
            @item.rack_list_code = rack.rack_list.code
            if @item.variant_id.blank? && (size_addon = @item.line_item_addons.where.not(size_chart_id: nil).first).present? && size_addon.size_chart.size_bucket_id != rack.item.size_bucket_id
              size_addon.update_attributes(size_chart_id: SizeChart.where(size_bucket_id: rack.item.size_bucket_id).first.id)
            end
        else
            @item.rack_list_code = '14B'
            WarehouseLineItemJoin.create(line_item_id: @item.id)
        end
        @item.available_in_warehouse = 'true'
        @item.design.decrement!(:in_stock_warehouse, @item.quantity)
        @item.save
    end

  end
  