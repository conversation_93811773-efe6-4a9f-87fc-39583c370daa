= javascript_include_tag 'designer_orders'
= form_tag designer_warehouse_orders_path(@designer), :method => 'get' do
  .col-md-2
    = label_tag 'Search by Order Number '
  .col-md-2
    = text_field_tag :order_num, params[:order_num], :style => "margin-left:10px;padding: 6px;width: 205px;",maxlength: 14,class: 'form form-control'
  .col-md-2
    = select_tag :state, options_for_select(WarehouseOrder.state_machine.states.map{|i|[i.name.to_s.humanize, i.name]},params[:state]),class: 'form-control',prompt: 'All'
  #reportrange.col-md-4.col-sm-6.col-xs-6
    %i.glyphicon.glyphicon-calendar.fa.fa-calendar
    / %span #{Time.now.strftime('%d %B, %Y')} - #{7.days.ago.strftime('%d %B, %Y')}
    / -date = params[:daterange_selector].presence || "#{Time.now.strftime('%d %B, %Y')} - #{7.days.ago.strftime('%d %B, %Y')}"
    =hidden_field_tag :daterange_period,(params[:daterange_period].presence || 'Last 3 Months')
    =text_field_tag :daterange_selector,params[:daterange_selector],style: 'border: 0px;width: 85%'
    %b.caret
  %span.submit(style="margin-left:10px;") 
    %span.button-medium
      = submit_tag "Search", :name => nil,class: 'btn btn-success'
=will_paginate @warehouse_orders
.row
  %table.table
    %thead
      %tr
        %th.col-md-1.text-center Created On
        %th.col-md-4.text-center Order details
        %th.col-md-2.text-center Delivery
        %th.col-md-2.text-center Status
        %th.col-md-3.text-center Action
  .clearfix
  - @warehouse_orders.each do |warehouse_order|
    -warehouse_order_invoice = WarehouseOrderInvoice.new(warehouse_order)
    %span{class: "warehouse_order_#{warehouse_order.id}"}
      =render partial: 'warehouse_order_account', locals: {warehouse_order: warehouse_order, warehouse_order_invoice: warehouse_order_invoice}
