- cache "hierarchical_menu/#{request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']}/#{session[:country][:country_code]}" do
  - hierarchical_data = @hierarchical_menu
  
  - if hierarchical_data[:super_menus].present?
    - hierarchical_data[:super_menus].each do |super_menu|
      %li.menu-list.super-menu-item
        = link_to super_menu.link, class: 'menu-link super-menu-link' do
          = super_menu.title
          - if MENU_SUPER_TAG.keys.include?(super_menu.title)
            %sup= MENU_SUPER_TAG[super_menu.title]
          %span.arrow{style: ("width: 4px; height:4px" unless @integration_status == "new")}
        
        - if super_menu.visible_menus.present?
          .megamenu-box.container-fluid
            .row
              - super_menu.visible_menus.each do |menu|
                - menu_columns = menu.menu_columns.where(hide: false).order(:position)
                - if menu_columns.present?
                  .col-md-3.menu-section
                    %h4.menu-title= menu.title
                    = render 'layouts/menu_items', menu_columns: menu_columns

  - if hierarchical_data[:standalone_menus].present?
    - hierarchical_data[:standalone_menus].each do |menu|
      - menu_columns = menu.menu_columns
      - menu_columns = menu_columns.sort_by(&:position)
      %li.menu-list
        - if menu.id == 42
          = link_to menu.link, class: 'menu-link', target: '_blank' do
            = menu.title
            %sup= MENU_SUPER_TAG[menu.title]
          %span.arrow{style: ("width: 4px; height:4px" unless @integration_status == "new")}
        - else
          = link_to menu.link, class: 'menu-link' do
            = menu.title
            - if MENU_SUPER_TAG.keys.include?(menu.title)
              %sup= MENU_SUPER_TAG[menu.title]
            %span.arrow{style: ("width: 4px; height:4px" unless @integration_status == "new")}
        .megamenu-box.container-fluid
          .row
            = render 'layouts/menu_items', menu_columns: menu_columns
