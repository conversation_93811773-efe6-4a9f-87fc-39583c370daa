#update_partial
  -if @error_message.present?
    .alert.alert-danger= @error_message.html_safe
  -if @order.present?
    .alert.alert-success
      Order Successfully Checked
    -if @order.best_shipper.present? || @order.courier_company.present?
      .container
        .col-md-4
          %span= link_to "Order Check Label", check_order_label_url(@order.number, format: 'pdf', bucket_id: @cargo_bucket), :target => '_blank', class: 'label label-default'
        .col-md-4
          %span= link_to "Scan Next Order", orders_order_check_page_path, :target => '_blank', class: 'label label-default'
        .col-md-4
          %span= link_to "Order Label", label_url(@order.number, :format => 'pdf'), :target => '_blank', class: "label label-default"
        -if @order.actual_weight <= 3
          .col-md-4
            %br
            %br
            -if @order.best_shipper.to_s.downcase == 'ups'
              %p= link_to '[ups_automated]', ups_invoice_url(@order.number), target: '_blank', id: 'ups', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'xindus'
              %p= link_to '[xindus_automated]', xindus_invoice_url(@order.number), target: '_blank', id: 'xindus', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'xpressbees_ups'
              %p= link_to '[xpressbees_ups_automated]', xpressbees_ups_invoice_url(@order.number), target: '_blank', id: 'xpressbees_ups', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'fedex'
              %p= link_to '[fedex_automated]', fedex_invoice_url(@order.number), target: '_blank', id: 'fedex', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'dhl'
              %p= link_to '[dhl_automated]', dhl_invoice_url(@order.number), target: '_blank', id: 'dhl', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'aramex'
              %p= link_to '[aramex_automated]',aramex_international_invoice_url(@order.number) , target: '_blank', id: 'aramex', class: 'label label-default'
            -if @order.best_shipper.to_s.downcase == 'dhl ecom'
              %p= link_to "[dhl_ecom_automated]",dhl_ecom_invoice_url(number:@order.number), target:'_blank',id:'dhl_ecom', class: 'label label-default'
          .col-md-4.col-md-offset-4
            %br
            %br
            %p= link_to 'Combined Labels', combined_sticker_url(@order.id),target: '_blank', class: "label label-default"
