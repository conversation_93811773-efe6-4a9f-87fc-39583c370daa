- bag_relation_status_mapping = {'rtv' => 'Return To Vendor', 'assigned' => 'Assigned To Tailor', 'received' => 'Received From Tailor', 'inscanned' => 'Inscanned From Tailor', 'reassigned' => 'Reassigned To Tailor', 'alteration' => 'Alteration To Inhouse Tailor', 'alteration assigned' => 'Alteration Bag Created', 'rtv assigned' => 'RTV Bag Created'}
=form_for t_info, url: tailoring_info_update_path, :html => { :id => "tailoring_info_form_#{@item.id}"}  do |f|
  .row
    =f.hidden_field :order_id, value: @order.id, class: "form-control"
    =f.hidden_field :item_id, value: @item.id, class: "form-control"
    =f.hidden_field :line_item_quantity, value: @item.quantity, class: "form-control"
    =f.hidden_field :tailor_info_done_by, value: current_account.name, class: "form-control"
    =f.hidden_field :id, value: t_info.id
    - #tailoring_material_types = TAILORING_MATERIALS

  - disable_select_box = t_info.material_received_status
  .row
    .col-md-4.col-lg-4.form-group
      =f.label :tailor_name, "Tailor Name"
      %br
      - if @receiving_tailors.present?
        - tailor_data = Tailor.get_tailor.to_h
        - current_tailor_data = tailor_data[t_info.tailor_name]
        - current_tailor_data_with_detail = current_tailor_data.try(:[],2) ? "[\"#{t_info.tailor_name}\", #{t_info.tailor_id}, \"#{current_tailor_data[2]}\"]" : "[\"#{t_info.tailor_name}\", #{t_info.tailor_id}]"
        = f.select :tailor_id ,options_for_select(@receiving_tailors, current_tailor_data_with_detail),{prompt:'Select Tailor Name'}, disabled: (disable_select_box || !DISABLE_ADMIN_FUCTIONALITY['order_page_tailor_assign_and_update']), class: 'tailor'
    .col-md-4.col-lg-4.form-group
      =f.label :tailoring_material, "Type"  
      %br
      =f.select :tailoring_material, options_for_select(@item.tailoring_material_list, t_info.tailoring_material),{ prompt:'Select Type' }, disabled: disable_select_box
    .col-md-4.col-lg-4.form-group
      - if DISABLE_ADMIN_FUCTIONALITY['order_page_tailor_receiving']
        =f.label :material_received_status, "Receive Material"
        %br
        -if t_info.rtv_material.blank? || t_info.rtv_material == 'RTV as Replacement'
          = f.radio_button :material_received_status, true, checked: t_info.material_received_status
          = f.label :material_received_status, "Yes"
        - unless t_info.material_received_status
          %br
          = f.radio_button :material_received_status, false, checked: t_info.material_received_status
          = f.label :material_received_status, "No"
      - else
        =f.label :material_received_status, "Receive Status"
        %br
        = t_info.material_received_status ? 'Yes' : 'No'
  .row 
    .col-md-3.col-lg-3.form-group
      =f.label "Tailoring Info Done By:"
      %br
      =t_info.tailor_info_done_by
      %br
      =f.label t_info.created_at.to_date
    .col-md-3.col-lg-3.form-group
      =f.label "Tailoring Material:"
      %br
      =t_info.tailoring_material
    .col-md-3.col-lg-3.form-group
      =f.label "Tailoring Quantity:"
      %br
      =t_info.line_item_quantity
    - if t_info.material_received_status_timestamp.present? && t_info.material_received_status
      .col-md-3.col-lg-3.form-group
        =f.label "Tailoring Info Material status Done At:"
        %br
        =f.label t_info.material_received_status_timestamp.to_date
  .row
    .col-md-4.col-lg-4.form-group
      =f.label "Tailoring Notes:"
      %br
      %pre{style:'max-height: 100px;overflow-y: scroll;word-break: normal;white-space: pre-wrap;'}= t_info.notes
    - if t_info.reassign_material_timestamp.present?
      .col-md-4.col-lg-4.form-group
        =f.label "Tailoring Material reassign At:"
        %br
        =f.label t_info.reassign_material_timestamp.to_date
  .row
    - if t_info.alteration_note.present?
      .col-md-4.col-lg-4.form-group
        =f.label "Alteration Notes:"
        %br
        %pre{style:'max-height: 100px;overflow-y: scroll;word-break: normal;white-space: pre-wrap;'}= t_info.alteration_note
    - if t_info.rtv_material.present?
      .col-md-4.col-lg-4.form-group
        =f.label "Tailoring Material RTV reason:"
        %br
        =f.label t_info.rtv_material

  .row
    - if (bag_relations = t_info.tailoring_bag_relations).present?
      .col-md-8
        %table.table.table-bordered{style:'broder:3px solid black;padding:5px;color:black;'}
          %tr
            %th Assign Bag Date
            %th Package Status
            %th Inscanned Date
            %th Inscanned By
          - bag_relations.each do |rel|
            %tr
              %td= rel.created_at.try(:strftime, "%d-%b-%Y %I:%M %p")
              %td= bag_relation_status_mapping[rel.state].presence || rel.state.titleize
              %td= rel.tailoring_inscan_bag.try(:created_at).try(:strftime, "%d-%b-%Y %I:%M %p")
              %td= rel.tailoring_inscan_bag.try(:created_by)

  %br
  - if !t_info.material_received_status
    = f.submit "Update Tailoring Details", data: {disable_with: "Wait.."},class: "btn btn-primary btn-sm measurement_form", :id => "tailoring_info_form_#{@item.id}"
%br
.col-md-4.col-lg-4.form-group
  %button.remove_tailor{"data-order-id" => @order.id, "data-item-id" => @item.design_id} Remove
%hr
%hr
