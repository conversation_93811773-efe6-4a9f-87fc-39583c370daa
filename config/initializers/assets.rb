# Be sure to restart your server when you modify this file.

# Version of your assets, change this if you want to expire all your assets.
Rails.application.config.assets.version = '1.0'

# Add additional assets to the asset load path
Rails.application.config.assets.paths << "#{Rails.root}/app/assets/fonts"
# Precompile additional assets.
# application.js, application.css, and all non-JS/CSS in app/assets folder are already added.

Rails.application.config.assets.precompile += Ckeditor.assets if CKEDITOR_ENABLED
Rails.application.config.assets.precompile += %w(ckeditor_js.js ) if CKEDITOR_ENABLED
Rails.application.config.assets.precompile += %w(application_new.css jquery.min.js application.css  application_new.js application.js admin.css admin_style.css app_admin.js product_view.js jquery-ui.min.js sessions.css orders.js web_cart.js infinite_scroll.js awesomplete.js pages.css  pages.js awesomplete.css store_full_image.js banner-slider.js jquery.raty.js cart_mobile.js design_resize.js invitation.js flatpickr.min.js flatpickr.min.css qc_fail_images.js fashion_updates.js)
Rails.application.config.assets.precompile += %w(admin.js order_qc_check.js shipments.js stylist.js alertify.min.js alertify.css alertify.core.css alertify.bootstrap3.css alertify.default.css dynamic_prices.css package_management.css subscriptions.js 404.css  catalog_view.js designs1.css wallet.css order_unpacking.js stitching.js fancyzoom.min.js jquery.fancybox.js jquery.fancybox-buttons.js jquery.fancybox-thumbs.js FileSaver.min.js tableexport.min.js banner-1.3.min.js survey_question.js)
Rails.application.config.assets.precompile += %w(rack_check.js user.js users.css bootstrap.js returns.css returns.js addresses.css tailoring_info.js rack_list.css rack_list.js package_management.js jquery-critical.js application_critical.css critical_inline.css store_new.js stitching.css ancybox-buttons.css jquery.fancybox.css jquery.fancybox-buttons.css jquery.fancybox-thumbs.css shipments.js modal.css reviews.css reviews.js dynamic_landing_pages.css dynamic_landing_pages.js designers.js order_reports.js googlemaps_places.js image_slider.js respond.min.js) 
Rails.application.config.assets.precompile += %w(grading.css seller.css seller.js seller_style.js dynamic_size_charts.js coupon.js designs.js designers_show_edit.js chartkick.js  designer_collection.css designer_orders.js gauge/gauge.min.js gauge/gauge_demo.js datatables/jquery.dataTables.min.js datatables/dataTables.bootstrap.min.js dataTables.bootstrap.min.css vendor_performance.js vendor_performance.css bootstrap-select.min.css bootstrap-select.min.js designers.css coupons.js coupons.css designer_collections.css)
Rails.application.config.assets.precompile += %w( jquery-rakhi.js custom-rakhi.js jquery.bxslider.min.js carousel.js intlTelInput.min.js intlTelInput.css jquery.validate.min.js jquery.sticky-kit.min.js store.js surveys.js admin_designers.css product_analytics.css review-page.css fashion_updates.css horoscope.css mobile_menu.css eid_page.css landing.css banner-slider.css infinite_scroll.css surveys.css carousel.css rakhi.css sitemaps.css vendor_promotion.js delayed_files.js warehouse_orders.js designer_owner.js)
Rails.application.config.assets.precompile += %w(  application_red.scss addresses_red.scss application_critical_red.css.scss application_new_red.scss banner-slider_red.scss coupons_red.scss critical_inline_red.css.scss designs1_red.scss pages_red.scss returns_red.scss sessions_red.scss sitemaps_red.css stitching_red.scss users_red.scss wallet_red.css.scss fashion_updates_red.scss horoscope_red.scss dynamic_landing_pages_red.scss reviews_red.scss designer_owner.scss)
Rails.application.config.assets.precompile += %w(seamless_account.js gift_card_orders.css gift_card_orders.js)
Rails.application.config.autoload_paths += %W(#{Rails.application.config.root}/app/models/ckeditor) if CKEDITOR_ENABLED

Rails.application.config.assets.precompile += %w(common.js)
Rails.application.config.assets.precompile += %w(cancel_cod_order.js communication_topic.js)
Rails.application.config.assets.precompile += %w( stripe_checkout.css )
Rails.application.config.assets.precompile += %w( stripe_checkout.js )
Rails.application.config.assets.precompile += %w( franchise.css )
Rails.application.config.assets.precompile += %w( order.js place_order_form_validation.js paypal_client.js)
Rails.application.config.assets.precompile += %w( product.js)
Rails.application.config.assets.precompile += %w( unbxd_recommendation.js accounts.js grading_tag.js)
Rails.application.config.assets.precompile += %w( unbxd_recommendation.scss)
Rails.application.config.assets.precompile += %w(mirraw_admin.css)
Rails.application.config.assets.precompile += %w(clickpost_shipment.js)
Rails.application.config.assets.precompile += %w(tickets.scss)
Rails.application.config.assets.precompile += %w(tickets.js)
Rails.application.config.assets.precompile += %w(admin_mirraw.js)
Rails.application.config.assets.precompile += %w(rtv_shipment.js)
Rails.application.config.assets.precompile += %w(seller_campaign.js)
Rails.application.config.assets.precompile += %w(warehouse_cancel_order.js)
Rails.application.config.assets.precompile += %w(receiving_panel_style.scss)
Rails.application.config.assets.precompile += %w(designer_order.js seller_design.js)
