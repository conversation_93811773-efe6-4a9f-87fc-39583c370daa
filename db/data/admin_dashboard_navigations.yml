{
  oms: {
      InBound: [
        ['Inscan', '/packages/in_scan?dashboard_type=oms'], 
        ['QC Check', '/orders/tracking_details?dashboard_type=oms'], 
        ['Order Rack', '/orders/tracking_details?dashboard_type=oms'], 
        ['Repairing Pending', '/packages/quality_repairable_products'],
        ['RTO Inscan', '/packages/rto_inscan_panel'],
        ['RTO Panel', '/packages/rto_panel']
      ],
      OutBound: [
        ['RTV Pending', '/admin/rtv_pending'], 
        ['Order Check', '/orders/order_check_page?dashboard_type=oms'], 
        ['Packaging', '/packages/post_packaging_scan'], 
        ['Invoice Generation', '/admin/ready_for_dispatch'], 
        ['Outscan', '/packages/out_scan?dashboard_type=oms']
      ],
      Inventory: [        
        ['Stitching Searching & Picking', '/packages/stitching_searching_list'], 
        ['Warehouse Searching & Picking', '/packages/stitching_searching_list'], 
        ['RFD Searching & Picking', '/admin/ready_for_dispatch'],
        ['Open and Close Racks', '/rack_lists/rack_open_close']
      ],
      Production: [
        ['Tailor Assignment', '/tailoring_info/send_for_tailoring'], 
        ['Tailor Inscan', '/packages/tailoring_inscan?dashboard_type=oms'], 
        ['Alteration Inscan', '/packages/tailoring_inscan?dashboard_type=oms'], 
        ['Tailor Receiving', '/tailoring_info/receiving_panel'], 
        ['Alteration Pending', '/tailoring_info/send_for_tailoring?dashboard_type=oms_alteration'], 
        ['RTT Pending', '/tailoring_info/send_for_tailoring?dashboard_type=oms_rtt'], 
        ['Stitching Done', '/packages/stitching_done_panel'], 
        ['Stitched Rack Assignment', '/packages/pending_panels?process_name=Stitching Done Pending']
      ]
    },
  wms: {            
      InBound: [
        ['Inscan', '/packages/in_scan?dashboard_type=wms'], 
        ['QC Check', '/orders/tracking_details?dashboard_type=wms'],    
        ['Update QC Rate', '/mirraw_admin/designers/dynamic_qc_panel']
      ],
      OutBound: [
        ['RTV Outscan Pending', '/packages/pending_panels?process_name=RTV Outscan Pending'], 
        ['Outscan', '/packages/out_scan?dashboard_type=wms']
      ],      
      Production: [        
        ['Tailor Inscan', '/packages/tailoring_inscan?dashboard_type=wms'], 
        ['Alteration Pending', '/tailoring_info/send_for_tailoring?dashboard_type=wms_alteration']
      ],
      PurchaseOrder: [
        ['', '/warehouse_orders/index']
      ]
    }
}