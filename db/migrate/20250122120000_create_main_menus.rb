class CreateMainMenus < ActiveRecord::Migration
  def change
    create_table :main_menus do |t|
      t.string :title, null: false
      t.text :link
      t.integer :position
      t.string :country
      t.string :app_source
      t.string :app_name
      t.boolean :is_hidden, default: false
      t.timestamps null: false
    end

    add_index :main_menus, :position
    add_index :main_menus, :is_hidden
    add_index :main_menus, [:country, :app_source]
  end
end
