class PopulateInitialSuperMenus < ActiveRecord::Mi<PERSON>
  def up
    # Create initial super menus based on common categories
    super_menus_data = [
      { title: 'Women', link: '/women', position: 1 },
      { title: 'Men', link: '/men', position: 2 },
      { title: 'Kids', link: '/kids', position: 3 },
      { title: 'Jewellery', link: '/jewellery', position: 4 },
      { title: 'Home & Living', link: '/home-living', position: 5 }
    ]

    super_menus_data.each do |data|
      SuperMenu.create!(data) unless SuperMenu.exists?(title: data[:title])
    end

    # Optional: You can map existing menus to super menus here
    # Example mapping (uncomment and modify as needed):
    
    # women_super_menu = SuperMenu.find_by(title: 'Women')
    # if women_super_menu
    #   Menu.where(title: ['Sarees', 'Lehengas', 'Salwar Kameez', 'Kurtis']).update_all(super_menu_id: women_super_menu.id)
    # end
    
    # men_super_menu = SuperMenu.find_by(title: 'Men')
    # if men_super_menu
    #   Menu.where(title: ['<PERSON><PERSON>', 'She<PERSON>wan<PERSON>', 'Nehru Jackets']).update_all(super_menu_id: men_super_menu.id)
    # end
    
    # jewellery_super_menu = SuperMenu.find_by(title: 'Jewellery')
    # if jewellery_super_menu
    #   Menu.where(title: ['Necklaces', 'Earrings', 'Bangles', 'Rings']).update_all(super_menu_id: jewellery_super_menu.id)
    # end
  end

  def down
    # Remove the super menus created in the up method
    SuperMenu.where(title: ['Women', 'Men', 'Kids', 'Jewellery', 'Home & Living']).destroy_all
    
    # Reset menu associations
    Menu.update_all(super_menu_id: nil)
  end
end
