module FreshdeskApi
  module Helper

    def self.included(base)
      base.send :include, HTTParty
      base.base_uri 'https://mirraw.freshdesk.com/api/v2'
      base.headers 'Authorization' => 'eERYTEd6eXBNYWRYYXQyU2c2QUs=', 'Content-Type' => 'application/json'
      base.extend ClassMethods
      base.send :include, InstanceMethods
    end

    module ClassMethods

      def where(query={}, &block)
        #return all if query.blank?
        fetch_all_pages(search_path,300, query, &block)
      end


      def create_result(transaction, result_params = nil)
        check_errors transaction
        data = (result_params.nil? ? transaction : transaction[result_params])
        Array.wrap(data).map do |object|
          object.deep_symbolize_keys!
          new(Struct.new(*(keys = object.keys)).new(*object.values_at(*keys)))
        end
      end

      def check_errors transaction
        return unless transaction.is_a? HTTParty::Response
        code = transaction.response.code
        raise "freshdesk api error: #{code} #{transaction['errors'].to_s}" unless code == '200'
      end

      def all(&block)
        #create_result resource_path
        fetch_all_pages(resource_path, 300, &block)
      end

      def find id
        create_result(get resource_path + "/#{id}").first
      end

      def fetch_all_pages(url,max_page_size, query = {}, &block)
        page, result_count =  1, 0
        loop do
          query[:page] = page
          page += 1
          results = create_result get(url, {query: query})
          yield results if results.present?
          break unless result_count < results['total'] && page <= max_page_size
        end
      end

    end

    module InstanceMethods

      def initialize(data_container)
        @data_container = data_container
        members
        self
      end

      def methods
        super + data_container.methods
      end

      def method_missing(method, *args)
        if data_container.respond_to?(method)
          data_container.send(method, *args)
        else
          super
        end
      end

      def save
        self.class.put(resource_path+"/#{id}", changed_attributes)
      end

      private

      attr_reader :data_container

      def members
        class << data_container
          members.each do |member|
            define_method "#{member.to_s}=" do |value|
              (@changed_attributes ||= {})[member] = self.send member
              super value
            end
          end

          def changed_attributes
            @changed_attributes || {}
          end
        end
      end

    end

  end
end