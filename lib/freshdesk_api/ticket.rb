module FreshdeskApi
  class Ticket < Base

    def self.search_path
      '/tickets'.freeze
    end

    def self.resource_path
      '/tickets'.freeze
    end

    # def self.search(query={},&block)
    #   get('/search/tickets', {query: {query: "\"#{query}\"", page: 10}})#, 'results'
    # end

    def self.search(query={},&block)
      page, result_count =  1, 0
      loop do
        transaction = get('/search/tickets', {query: {query: "\"#{query}\"", page: page}})
        check_errors transaction
        page += 1
        #record += create_result(transaction['results'])
        result_count += transaction['results'].size

        yield create_result(transaction['results'])

        break unless result_count < transaction['total'] && page <= 10
      end
    end

    def self.post_reply(url, payload)
      post(url, {body: payload.to_json}).parsed_response
    end

    def self.update_ticket(url, payload)
      put(url, {body: payload.to_json}).parsed_response
    end
  end
end