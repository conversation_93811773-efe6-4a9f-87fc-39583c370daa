FactoryGirl.define do
  factory :menu_column do
    sequence(:title) { |n| "Menu Column #{n}" }
    sequence(:link) { |n| "/menu-column-#{n}" }
    sequence(:position) { |n| n }
    hide false
    country nil
    app_source nil
    app_name nil
    menu

    trait :hidden do
      hide true
    end

    trait :with_country do
      country 'IN'
    end

    trait :mirraw do
      app_name nil
    end

    trait :luxe do
      app_name 'luxe'
    end
  end
end
