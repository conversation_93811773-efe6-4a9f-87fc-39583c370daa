FactoryGirl.define do
  factory :super_menu do
    sequence(:title) { |n| "Super Menu #{n}" }
    sequence(:link) { |n| "/super-menu-#{n}" }
    sequence(:position) { |n| n }
    is_hidden false
    country nil
    app_source nil
    app_name nil

    trait :hidden do
      is_hidden true
    end

    trait :with_country do
      country 'IN'
    end

    trait :with_app_source do
      app_source 'desktop'
    end

    trait :mirraw do
      app_name nil
    end

    trait :luxe do
      app_name 'luxe'
    end

    # Specific super menu types
    trait :women do
      title 'Women'
      link '/women'
      position 1
    end

    trait :men do
      title 'Men'
      link '/men'
      position 2
    end

    trait :kids do
      title 'Kids'
      link '/kids'
      position 3
    end

    trait :jewellery do
      title 'Jewellery'
      link '/jewellery'
      position 4
    end
  end
end
