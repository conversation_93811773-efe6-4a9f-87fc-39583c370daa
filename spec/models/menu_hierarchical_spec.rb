require 'rails_helper'

RSpec.describe Menu, type: :model do
  describe 'hierarchical menu functionality' do
    describe 'associations' do
      it { should belong_to(:super_menu).optional }
    end

    describe '.hierarchical_menu_by_country' do
      let(:country) { 'IN' }
      let!(:super_menu) { create(:super_menu, :women, country: country) }
      let!(:menu_with_super) { create(:menu, :sarees, super_menu: super_menu, hide: false, country: country) }
      let!(:standalone_menu) { create(:menu, :lehengas, super_menu: nil, hide: false, country: country) }
      let!(:hidden_menu) { create(:menu, :kurtis, super_menu: super_menu, hide: true, country: country) }

      before do
        # Create menu columns and items for proper testing
        menu_column = create(:menu_column, menu: menu_with_super, hide: false)
        create(:menu_item, menu_column: menu_column, hide: false)
        
        standalone_column = create(:menu_column, menu: standalone_menu, hide: false)
        create(:menu_item, menu_column: standalone_column, hide: false)
      end

      subject { Menu.hierarchical_menu_by_country(country) }

      it 'returns a hash with super_menus and standalone_menus' do
        expect(subject).to have_key(:super_menus)
        expect(subject).to have_key(:standalone_menus)
      end

      it 'includes super menus with visible menus' do
        expect(subject[:super_menus]).to include(super_menu)
      end

      it 'includes standalone menus' do
        expect(subject[:standalone_menus]).to include(standalone_menu)
      end

      it 'excludes hidden menus from super menu associations' do
        super_menu_menus = subject[:super_menus].first.menus
        expect(super_menu_menus).to include(menu_with_super)
        expect(super_menu_menus).not_to include(hidden_menu)
      end
    end

    describe 'factory traits' do
      it 'creates menu with super menu association' do
        super_menu = create(:super_menu)
        menu = create(:menu, :with_super_menu, super_menu: super_menu)
        
        expect(menu.super_menu).to eq(super_menu)
      end

      it 'creates menu without super menu association' do
        menu = create(:menu)
        
        expect(menu.super_menu).to be_nil
      end
    end
  end
end
