require 'rails_helper'

RSpec.describe SuperMenu, type: :model do
  describe 'associations' do
    it { should have_many(:menus).dependent(:nullify) }
  end

  describe 'validations' do
    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:position) }
  end

  describe 'scopes' do
    let!(:visible_super_menu) { create(:super_menu, is_hidden: false) }
    let!(:hidden_super_menu) { create(:super_menu, is_hidden: true) }

    it 'returns only visible super menus' do
      expect(SuperMenu.visible).to include(visible_super_menu)
      expect(SuperMenu.visible).not_to include(hidden_super_menu)
    end

    it 'returns super menus in order' do
      super_menu_1 = create(:super_menu, position: 2)
      super_menu_2 = create(:super_menu, position: 1)
      
      expect(SuperMenu.ordered.first).to eq(super_menu_2)
      expect(SuperMenu.ordered.last).to eq(super_menu_1)
    end
  end

  describe '#visible_menus' do
    let(:super_menu) { create(:super_menu) }
    let!(:visible_menu) { create(:menu, super_menu: super_menu, hide: false) }
    let!(:hidden_menu) { create(:menu, super_menu: super_menu, hide: true) }

    it 'returns only visible menus' do
      expect(super_menu.visible_menus).to include(visible_menu)
      expect(super_menu.visible_menus).not_to include(hidden_menu)
    end
  end

  describe '#has_visible_menus?' do
    let(:super_menu) { create(:super_menu) }

    context 'when super menu has visible menus' do
      before { create(:menu, super_menu: super_menu, hide: false) }

      it 'returns true' do
        expect(super_menu.has_visible_menus?).to be true
      end
    end

    context 'when super menu has no visible menus' do
      before { create(:menu, super_menu: super_menu, hide: true) }

      it 'returns false' do
        expect(super_menu.has_visible_menus?).to be false
      end
    end
  end
end
